﻿<!--

Purpose: This include file needs to be enabled in Content Management Environment.

It enables features and components that are used by the API layer.

-->
<configuration xmlns:x="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <experienceAnalytics>
      <api>
        <!-- This section hosts the definitions of the Experience Analytics dimensions
            'id' attribute is the unique identifier and should match the ID of the corresponding dimension definition item in Marketing Control Panel
            'type' attribute references the fully qualified class name and assembly name with the dimension implementation.
            Transformer elements underneath define the reference to the class responsible for transforming raw value of dimension key from the server
            to the friendly label shown in reports.
        -->
        <dimensions>
          <dimension id="{EF129780-FFCF-4EAF-B429-80D25315248E}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByEmailManager, Sitecore.ExperienceAnalytics" >
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.Exm.EmailManagerRootKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{A1BCAB9A-1A98-4A3D-A58F-34ACF7931C49}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByEmail, Sitecore.ExperienceAnalytics" >
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.Exm.EmailMessageKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{214E8E8C-519A-4803-8579-3CA466F9F29F}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByLanguageSpecificEmail, Sitecore.ExperienceAnalytics" >
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.Exm.EmailLanguageTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{988A1D00-9D00-4F05-97EC-DF3391F8CCE7}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCountry, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.CountryDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{1879168B-AF5E-4E9C-9DAE-8B71125F2AD2}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByRegion, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.RegionDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{33ACD611-FE19-4769-99F9-1EF1D997BDC5}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCity, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.CityDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{6237CC24-4FF5-4869-B898-FC6A534F3C3E}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannelType, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ChannelTypeDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{86CA3214-A950-4EEF-BEE6-A2C5CF6FAC24}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannel, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ChannelDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{595A12E2-CEF6-4B1C-9990-83FDD7173533}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannelGroup, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ChannelGroupDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{DACF0445-EDB1-4A90-851E-380DC6A36541}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByDownloads, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DownloadsDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{3E01BA28-2B4D-408A-A4BA-6C51ED9FFB9C}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCampaign, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.CampaignDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{A8EC3E11-A417-4624-A612-12A406284D4B}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCampaignGroup, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.CampaignGroupDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{6295C8F0-5ACB-4634-A1D6-6D3248EC210C}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByLanguage, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.LanguageDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{C001F4B2-0523-436E-9097-AD4E39D51835}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPattern, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ItemDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{E6CC7A2B-6E72-40CE-9315-88F4617CDB10}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByReferringSite, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{F197D8FF-D4C8-42DF-BC6E-512F17292674}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPage, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.PageDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{81379156-7721-4EE5-8951-AE92E1CDF090}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByEntryPage, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.PageDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{3DC11B43-1D7F-4169-A87D-291D57ECEDA6}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByExitPage, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.PageDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{F2C1CCBF-C2D6-4FF9-A3BF-DD55E60071A2}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPageUrl, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{6B945AE7-673F-412A-96F8-F53EF9D63BBC}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByEntryPageUrl, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{CBD2E37E-CC79-4A5A-8B3B-95F6B7D7FF53}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByExitPageUrl, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{CBEE2CF5-51DB-4106-A7A9-BF87AB173742}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByLocalSearchKeyword, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{DF6FCD1E-CBCA-4747-9109-ABF132189A01}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.BySearchKeyword, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.DefaultDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{8345E6DF-8982-4F32-B478-40D672E914BB}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPageviews, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.PageViewsDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{6DD3E3A7-FE91-4860-9BF6-388863AD7C9D}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByConversions, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ConversionsDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
          <dimension id="{6F502E86-5CDB-4D4E-B3A1-905D41B86E9F}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByGoal, Sitecore.ExperienceAnalytics">
            <transformer type="Sitecore.ExperienceAnalytics.Api.Response.DimensionKeyTransformers.ItemDimensionKeyTransformer, Sitecore.ExperienceAnalytics" />
          </dimension>
        </dimensions>
        <services>
          <!-- DimensionDefinitionService is responsible for instantiating Experience Analytics Dimensions objects from configuration.
               The 'pathToConfigNode' parameter specifies the element where the dimensions are expected to be configured.
               Default: "experienceAnalytics/aggregation/dimensions"
           -->
          <dimensionDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.DimensionDefinitionService, Sitecore.ExperienceAnalytics" >
            <param desc="pathToConfigNode">experienceAnalytics/api/dimensions</param>
          </dimensionDefinitionService>

          <!-- ReportingService is responsible for processing reporting query requests. -->
          <reportingService type="Sitecore.ExperienceAnalytics.Api.ReportingService, Sitecore.ExperienceAnalytics" />

          <!-- SegmentDefinitionService is responsible for reading data from the Segments dimension table in RDB.
               The 'connectionStringName' parameter the name of the connection string of the RDB instance to connect to.
               Default: "reporting"
          -->
          <segmentDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.DataProviderSegmentDefinitionService, Sitecore.ExperienceAnalytics" />

          <!-- UserService is responsible for resolving user state during Web API execution. -->
          <userService type="Sitecore.ExperienceAnalytics.Core.Repositories.UserService, Sitecore.ExperienceAnalytics" />
        </services>
      </api>
    </experienceAnalytics>
    <pipelines>
      <initialize>
        <!-- Initializing Experience Analytics Web API -->
        <processor type="Sitecore.ExperienceAnalytics.Api.Pipelines.Initialize.WebApiInitializer, Sitecore.ExperienceAnalytics" x:after="processor[position()=last()]" />
      </initialize>
    </pipelines>
    <reporting>
      <dataProvider>
        <!-- enabling reporting cache -->
        <x:attribute name="cacheEnabled">true</x:attribute>
      </dataProvider>
    </reporting>
    <settings>
      <!-- EXPERIENCE ANALYTICS - API - CACHE EXPIRATION
           TTL for reporting data cache. Used in conjunction with the cacheEnabled=true flag on reporting/dataProvider.
           Default value: 00:00:30 (30 seconds)
        -->
      <setting name="ExperienceAnalytics.Api.CacheExpiration" value="00:00:30" />

      <!-- EXPERIENCE ANALYTICS - API - KEYS TOP DEFAULT
           The default value for keyTop query parameter.
           Default value: 100
        -->
      <setting name="ExperienceAnalytics.Api.KeysTopDefault" value="100" />

      <!-- EXPERIENCE ANALYTICS - API - MASTER DATABASE
           The content database used to decorate server data with labels and other auxiliary information.
           Default value: master
        -->
      <setting name="ExperienceAnalytics.Api.MasterDatabase">
        <x:attribute name="value">webcd</x:attribute>
      </setting>
    </settings>
  </sitecore>
</configuration>