﻿<?xml version="1.0" encoding="utf-8" ?>

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:set="http://www.sitecore.net/xmlconfig/set/">
  <sitecore>
    <settings>
      <setting name="Social.Messages.SearchIndex.Master" value="social_messages_web" patch:instead="setting[@name='Social.Messages.SearchIndex.Master']"/>
    </settings>
    <contentSearch>
      <configuration>
        <indexes>
          <!-- Remove indexes based on the master DB -->
          <index id="social_messages_master">
            <patch:delete/>
          </index>
        </indexes>
      </configuration>
    </contentSearch>

  </sitecore>
</configuration>