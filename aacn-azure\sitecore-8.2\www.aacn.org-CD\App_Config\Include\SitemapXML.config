<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>	    
    <!--
    STATIC FILE GENERATOR
    
    The following section is responsible for generating a static robots.txt and site map XML files per website 
    defined below. To disable the static file generator, comment out the <sitemapVariables> and <events> sections below.
    -->
    
    <sitemapVariables>
      <sitemapVariable name="xmlnsTpl" value="http://www.sitemaps.org/schemas/sitemap/0.9" />
      <sitemapVariable name="database" value="web" />
      <sitemapVariable name="sitemapConfigurationItemPath" value="/sitecore/system/Modules/Sitemap XML/" />
      <sitemapVariable name="productionEnvironment" value="false" />
      <sitemapVariable name="generateRobotsFile" value="true" />
    </sitemapVariables>
    <!--<events timingLevel="custom">
      <event name="publish:end">
        <handler type="Sitemap.XML.Models.SitemapHandler, Sitemap.XML" method="RefreshSitemap" />
      </event>
    </events>-->
        
    <!--
    SITEMAP HANDLER 
    
    The following section is responsible for adding a handler for generating a sitemap "on the fly". 
    To disable the handler, comment out the <pipelines> section below.
    -->

    <pipelines>
      <httpRequestBegin>
        <processor type="Sitemap.XML.Configuration.SitemapHandler, Sitemap.XML"
                   patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.CustomHandlers, Sitecore.Kernel']">
          <!-- 
          cacheTime: 
            Cache time in seconds.
          
          excludedPaths:
            Collection of item paths that you want to exclude of the sitemap.xml.
            The pipeline will exclude all items with this string as part of the path.
            You can add more that one, separating them with a pipe
          -->
          <cacheTime>1</cacheTime>
          <excludedPaths>/My Account/|/Customer/</excludedPaths>
        </processor>
      </httpRequestBegin>
      
      <!--Adding a .xml extension to the list of allowed extensions to avoid sitecore pipelines ignoring 
      requests with that extension-->  
      <preprocessRequest>
        <processor type="Sitecore.Pipelines.PreprocessRequest.FilterUrlExtensions, Sitecore.Kernel">
          <param desc="Allowed extensions (comma separated)">aspx, ashx, asmx, xml</param>
        </processor>
      </preprocessRequest>
    </pipelines>
  </sitecore>  
</configuration>