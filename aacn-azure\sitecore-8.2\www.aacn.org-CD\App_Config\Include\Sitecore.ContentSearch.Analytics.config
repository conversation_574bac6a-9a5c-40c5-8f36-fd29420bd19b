﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file defines a number of pipelines that are executed when the system indexes experience data.

The first section defines the set of aggregators that are used to index experience data.

For each aggregator that is defined, a corresponding inbound filter pipeline can be defined. This allows you to filter out specific
experience data that should not be indexed. For example, for the VisitObservable aggregator, the system runs a pipeline called
<visitobservable.filter.inbound>, and executes the global filters defined in the <aggregation.filter.inbound> pipeline.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <settings>
      <!--  CONTENT SEARCH – ANALYTICS - INDEX NAME
            Defines the name of the analytics index.
            The setting is used in IndexManager to return the analytics index.
            Important: The value of the setting must be the same as the actual name of the analytics index.
            Default value: sitecore_analytics_index
      -->
      <setting name="ContentSearch.Analytics.IndexName" value="sitecore_analytics_index"/>

    </settings>

    <pipelines>

      <group groupName="analytics.aggregation">
        <pipelines>
          <interactions>
            <!-- Aggregators for indexing experience data -->
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsVisitAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">VisitAggregationObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsVisitPageAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">VisitPageAggregationObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsVisitPageEventAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">VisitPageEventAggregationObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsContactAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">ContactAggregationObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsContactTagAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">ContactTagAggregationObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.AnalyticsAddressAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">AddressAggregationObservable</param>
            </processor>
          </interactions>
          <contacts>
            <!-- Aggregators for indexing contact changes -->
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.ContactChangeContactAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">ContactChangeObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.ContactChangeContactTagAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">ContactTagChangeObservable</param>
            </processor>
            <processor type="Sitecore.ContentSearch.Analytics.Aggregators.ContactChangeAddressAggregator, Sitecore.ContentSearch.Analytics">
              <param desc="name">AddressChangeObservable</param>
            </processor>
          </contacts>
        </pipelines>
      </group>

      <!-- AGGREGATION FILTER INBOUND
           This pipeline allows you to define global filters that can filter out specific experience data that should not be indexed. The
           filters you define here are evaluated for all the aggregators. The sample processors show how you, for example, can exclude data
           based on contact ID or visit path.
      -->
      <aggregation.filter.inbound>
        <processor type="Sitecore.ContentSearch.Analytics.Pipelines.AggregationFilters.InboundContactIdentificationLevelFilter, Sitecore.ContentSearch.Analytics">
          <filters hint="list:AddFilter">
            <filter>Anonymous</filter>
          </filters>
        </processor>
        <!--
        <processor type="Sitecore.ContentSearch.Analytics.Pipelines.AggregationFilters.InboundContactIdFilter, Sitecore.ContentSearch.Analytics">
          <filters hint="list:AddFilter">
            <filter>ca82109f-30cd-4414-956e-caea41c7510c</filter>
          </filters>
        </processor>
        <processor type="Sitecore.ContentSearch.Analytics.Pipelines.AggregationFilters.InboundContactClassificationFilter, Sitecore.ContentSearch.Analytics">
          <filters hint="list:AddFilter">
            <filter>0</filter>
          </filters>
        </processor>
        <processor type="Sitecore.ContentSearch.Analytics.Pipelines.AggregationFilters.InboundVisitPathFilter, Sitecore.ContentSearch.Analytics">
          <filters hint="list:AddFilter">
            <filter>/default.aspx</filter>
          </filters>
        </processor>
        -->
      </aggregation.filter.inbound>

      <!-- CONTACTOBSERVABLE FILTER INBOUND
           This pipeline is an example that shows you how to filter out specific experience data that should not be indexed, in this case
           for the ContactObservable aggregator.
      -->
      <contactaggregationobservable.filter.inbound>
        <!--
        <processor type="Sitecore.ContentSearch.Analytics.Pipelines.AggregationFilters.InboundVisitPathFilter, Sitecore.ContentSearch.Analytics">
          <filters hint="list:AddFilter">
            <filter>/other.aspx</filter>
          </filters>
        </processor>
        -->
      </contactaggregationobservable.filter.inbound>

    </pipelines>
  </sitecore>
</configuration>
