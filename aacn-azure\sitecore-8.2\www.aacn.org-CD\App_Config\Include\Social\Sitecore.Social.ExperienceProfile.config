﻿<?xml version="1.0" encoding="utf-8"?>
<!--
    
Purpose: This include file configures the Social tab in the Experience Profile.

To enable a Twitter feed, you must specify a value for the Social.ExperienceProfile.TwitterWidgetId.

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!-- SOCIAL - EXPERIENCE PROFILE - GOOGLE+ RECENT ACTIVITY POSTS COUNT
           Specifies the number of posts to display in the Recent Activity panel on the Social, Google+ tab.
           Default value: 3      
      -->
      <setting name="Social.ExperienceProfile.GooglePlusRecentActivityPostsCount" value="3" />
      <!-- SOCIAL - EXPERIENCE PROFILE - TWITTER RECENT ACTIVITY TWEETS COUNT
           Specifies the number of tweets to display in the Recent Activity panel on the Social, Twitter tab. Possible values 1 to 20.
           Default value: 3      
      -->
      <setting name="Social.ExperienceProfile.TwitterRecentActivityTweetsCount" value="3" />
      <!-- SOCIAL - EXPERIENCE PROFILE - TWITTER WIDGET ID
           Specifies the widget identifier to use in the Recent Activity panel on the Social, Twitter tab.
           Default value: ""
      -->
      <setting name="Social.ExperienceProfile.TwitterWidgetId" value="" />
    </settings>
    <pipelines>
      <social.initializeApi>
        <processor type="Sitecore.Social.ExperienceProfile.Client.IoC.IoCInitialization, Sitecore.Social.ExperienceProfile.Client" patch:after="processor[@type='Sitecore.Social.Infrastructure.IoC.IoCInitialization, Sitecore.Social.Infrastructure']">
          <assemblies hint="list:AddAssembly">
            <assembly>Sitecore.Social.ExperienceProfile.Client.dll</assembly>
          </assemblies>
        </processor>
      </social.initializeApi>
    </pipelines>
  </sitecore>
</configuration>