﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the visitor's profile information that is requested when a visitor logs in to the website 
with their Facebook credentials.

Please read the Sitecore Social Connected documentation before changing the configuration.

You can enable or disable a field to extend or reduce the amount of visitor profile information that is received from Facebook.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <!--  This section configures social profile synchronization. 
            To include a specific field from the social site in the user's profile, set the 'enabled' property to 'true'. 
            To remove a specific field on the social site from the user's profile, set the 'enabled' property to 'false. -->
      <profileKeyMappings>
        <!-- Configuration of profile synchronization with Facebook. -->
        <network name="Facebook">
          <!-- 'ID' field. -->
          <!-- The user's Facebook ID. This ID is unique to each app and cannot be used across different apps. -->
          <field enabled="true" originalKey="id" sitecoreKey="fb_id" mainNetworkDataPropertyName="Id" permission="" access="/me" text="Id" />

          <!-- 'Name' field. -->
          <!-- The user's full name. -->
          <field enabled="true" originalKey="name" sitecoreKey="fb_name"  permission="" access="/me" text="Name" />

          <!-- 'First name' field. -->
          <!-- The user's first name. -->
          <field enabled="true" originalKey="first_name" sitecoreKey="fb_first_name" permission="" access="/me" text="First Name" />

          <!-- 'Middle name' field. -->
          <!-- The user's middle name. -->
          <field enabled="true" originalKey="middle_name" sitecoreKey="fb_middle_name" permission="" access="/me" text="Middle Name" />

          <!-- 'Last name' field. -->
          <!-- The user's last name. -->
          <field enabled="true" originalKey="last_name" sitecoreKey="fb_last_name" permission="" access="/me" text="Last Name" />

          <!-- 'Gender' field. -->
          <!-- The user's gender: female or male. -->
          <field enabled="true" originalKey="gender" sitecoreKey="fb_gender" permission="" access="/me" text="Gender" />

          <!-- 'Locale' field. -->
          <!-- The user's locale. -->
          <field enabled="true" originalKey="locale" sitecoreKey="fb_locale" permission="" access="/me" text="Locale" />

          <!-- 'Link' field. -->
          <!-- The URL of the user's profile on Facebook. -->
          <field enabled="true" originalKey="link" sitecoreKey="fb_link" permission="" access="/me" text="Link" />

          <!-- 'Third party id' field. -->
          <!-- An anonymous, but unique identifier for the user. This field is only returned if it is specifically requested in the fields URL parameter. -->
          <field enabled="true" originalKey="third_party_id" sitecoreKey="fb_third_party_id" permission="" access="/me" text="Third Party Id" />

          <!-- 'Timezone' field. -->
          <!-- The user's timezone offset from UTC. -->
          <field enabled="true" originalKey="timezone" sitecoreKey="fb_timezone" permission="" access="/me" text="Time Zone" />

          <!-- 'Updated time' field. -->
          <!-- The last time the user's profile was updated. Changes to the languages, link, timezone, verified, interested_in, favorite_athletes, 
               favorite_teams, and video_upload_limits are not reflected in this value. -->
          <field enabled="true" originalKey="updated_time" sitecoreKey="fb_updated_time" permission="" access="/me" text="Updated Time" />

          <!-- 'Verified' field. -->
          <!-- The user's account verification status - true or false. -->
          <field enabled="true" originalKey="verified" sitecoreKey="fb_verified" permission="" access="/me" text="Verified" />

          <!-- 'Friends' field. -->
          <!-- The user's friends. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_friends" permission="user_friends" access="/me/friends" text="Friends" />

          <!-- 'Permissions' field. -->
          <!-- The permissions that the user has granted the application. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_permissions" permission="" access="/me/permissions" text="Permissions" />

          <!-- ================= Permissions ============================================================ -->

          <!-- 'Email' field. -->
          <!-- This person's primary email address listed on their profile. This field will not be returned if no valid email address is available. -->
          <field enabled="true" originalKey="email" sitecoreKey="fb_email" permission="email" access="/me" text="Email Address" />

          <!-- ================= Frieds lists and groups =============================== -->
          <!-- 'Friendlists' field. -->
          <!-- The user's friend lists. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_friendlists" permission="manage_friendlists" access="/me/friendlists" text="Friend Lists" />

          <!-- 'Groups' field. -->
          <!-- The Facebook groups that a person is a member of. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_groups" permission="user_groups" access="/me/groups" text="Groups" />

          <!-- ================= User Likes =============================== -->

          <!-- 'Likes' field. -->
          <!-- The pages this user has liked. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_likes" permission="user_likes" access="/me/likes" text="Likes" />

          <!-- 'Movies' field. -->
          <!-- The movies listed on the user's profile. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_movies" permission="user_likes" access="/me/movies" text="Movies" />

          <!-- 'Music' field. -->
          <!-- The music listed on the user's profile. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_music" permission="user_likes" access="/me/music" text="Music" />

          <!-- 'Games' field. -->
          <!-- The games the user has added to the Arts and Entertainment section of their profile. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_games" permission="user_likes" access="/me/games" text="Games" />

          <!-- 'Languages' field. -->
          <!-- The user's languages. -->
          <field enabled="true" originalKey="languages" sitecoreKey="fb_languages" permission="user_likes" access="/me" text="Languages" />

          <!-- 'Favorite athletes' field. -->
          <!-- The user's favorite athletes. -->
          <field enabled="true" originalKey="favorite_athletes" sitecoreKey="fb_favorite_athletes" permission="user_likes" access="/me" text="Favorite Atheletes" />

          <!-- 'Favorite teams' field. -->
          <!-- The user's favorite teams. -->
          <field enabled="true" originalKey="favorite_teams" sitecoreKey="fb_favorite_teams" permission="user_likes" access="/me" text="Favorite Teams" />

          <!-- 'Books' field. -->
          <!-- The books listed on the user's profile. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_books" permission="user_likes" access="/me/books" text="Books" />

          <!-- 'Television' field. -->
          <!-- The television shows listed on the user's profile. -->
          <field enabled="true" originalKey="" sitecoreKey="fb_television" permission="user_likes" access="/me/television" text="Television" />

          <!-- ================= Personal ====================================== -->

          <!-- 'Bio' field. -->
          <!-- The user's biography. -->
          <field enabled="false" originalKey="bio" sitecoreKey="fb_bio" permission="user_about_me" access="/me" text="Bio" />

          <!-- 'Quotes' field. -->
          <!-- The user's favorite quotes. -->
          <field enabled="false" originalKey="quotes" sitecoreKey="fb_quotes" permission="user_likes" access="/me" text="Quotes" />

          <!-- 'Interests' field. -->
          <!-- The interests listed on the user's profile. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_interests" permission="user_interests" access="/me/interests" text="Interests" />

          <!-- 'Birthday' field. -->
          <!-- The user's birthday. -->
          <field enabled="true" originalKey="birthday" sitecoreKey="fb_birthday" permission="user_birthday" access="/me" text="Birthday" />

          <!-- 'Education' field. -->
          <!-- A list of the user's education history. -->
          <field enabled="true" originalKey="education" sitecoreKey="fb_education" permission="user_education_history" access="/me" text="Education" />

          <!-- 'Hometown' field. -->
          <!-- The user's hometown. -->
          <field enabled="false" originalKey="hometown" sitecoreKey="fb_hometown" permission="user_hometown" access="/me" text="Home Town" />

          <!-- 'Interested in' field. -->
          <!-- The genders the user is interested in. -->
          <field enabled="false" originalKey="interested_in" sitecoreKey="fb_interested_in" permission="user_relationship_details" access="/me" text="Interested In" />

          <!-- 'Location' field. -->
          <!-- The user's current city. -->
          <field enabled="true" originalKey="location" sitecoreKey="fb_location" permission="user_location" access="/me" text="Location" />

          <!-- 'Political' field. -->
          <!-- The user's political opinion. -->
          <field enabled="true" originalKey="political" sitecoreKey="fb_political" permission="user_religion_politics" access="/me" text="Political" />

          <!-- 'Relationship status' field. -->
          <!-- The user's relationship status: Single, In a relationship, Engaged, Married, It's complicated, In an open relationship, Widowed, Separated, Divorced, In a civil union, In a domestic partnership. -->
          <field enabled="true" originalKey="relationship_status" sitecoreKey="fb_relationship_status" permission="user_relationships" access="/me" text="Relationship Status" />

          <!-- 'Family' field. -->
          <!-- The user's family relationships. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_family" permission="user_relationships" access="/me/family" text="Family" />

          <!-- 'Religion' field. -->
          <!-- The user's religion. -->
          <field enabled="true" originalKey="religion" sitecoreKey="fb_religion" permission="user_religion_politics" access="/me" text="Religion" />

          <!-- 'Significant other' field. -->
          <!-- The user's significant other. -->
          <field enabled="false" originalKey="significant_other" sitecoreKey="fb_significant_other" permission="user_relationships" access="/me" text="Significant Other" />

          <!-- 'Website' field. -->
          <!-- The URL of the user's personal website. -->
          <field enabled="true" originalKey="website" sitecoreKey="fb_website" permission="user_website" access="/me" text="Website" />

          <!-- 'Work' field. -->
          <!-- A list of the user's work history. -->
          <field enabled="true" originalKey="work" sitecoreKey="fb_work" permission="user_work_history" access="/me" text="Work" />

          <!-- 'Accounts' field. -->
          <!-- The Facebook apps and pages owned by the current user. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_accounts" permission="manage_pages" access="/me/accounts" text="Accounts" />

          <!-- 'Achievements' field. -->
          <!-- The achievements of the user. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_achievements" permission="user_games_activity" access="/me/achievements" text="Achievements" />

          <!-- 'Scores' field. -->
          <!-- The scores for the user. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_scores" permission="user_games_activity" access="/me/scores" text="Scores" />

          <!-- 'Activities' field. -->
          <!-- The activities listed on the user's profile. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_activities" permission="user_activities" access="/me/activities" text="Activities" />

          <!--============================= photo and video ========================================== -->

          <!-- 'Albums' field. -->
          <!-- The photo albums this user has created. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_albums" permission="user_photos" access="/me/albums" text="Albums" />

          <!-- 'Photos' field. -->
          <!-- The photos the user (or friend) is tagged in. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_photos" permission="user_photos" access="/me/photos" text="Photos" />

          <!-- 'Videos' field. -->
          <!-- The videos this user has been tagged in. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_videos" permission="user_videos" access="/me/videos" text="Videos" />

          <!--============================= events and checkins ======================================= -->

          <!-- 'Events' field. -->
          <!-- The events this user is attending. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_events" permission="user_events" access="/me/events" text="Events" />

          <!-- 'Tagged Places' field. -->
          <!-- A list of tags of this person at a place in a photo, video, post, status or link. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_tagged_places" permission="user_tagged_places" access="/me/tagged_places" text="Tagged Places" />

          <!--============================= read stream ========================================== -->

          <!-- 'Home' field. -->
          <!-- The user's news feed. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_home" permission="read_stream" access="/me/home" text="Home" />

          <!-- 'Links' field. -->
          <!-- The user's posted links. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_links" permission="read_stream" access="/me/links" text="Links" />

          <!-- 'Posts' field. -->
          <!-- The user's own posts. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_posts" permission="read_stream" access="/me/posts" text="Posts" />

          <!-- 'Statuses' field. -->
          <!-- The user's status updates. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_statuses" permission="read_stream" access="/me/statuses" text="Statuses" />

          <!-- 'Tagged' field. -->
          <!-- Posts the user is tagged in. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_tagged" permission="read_stream" access="/me/tagged" text="Tagged" />

          <!--==============================  ========================================= -->

          <!-- 'Notifications' field. -->
          <!-- The notifications for the user. -->
          <field enabled="false" originalKey="" sitecoreKey="fb_notifications" permission="manage_notifications" access="/me/notifications" text="Notifications" />
        </network>
      </profileKeyMappings>
    </social>
  </sitecore>
</configuration>