﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file implements improved logic for robot detection.

Do not modify or disable this file.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <initializeTracker>
        <processor patch:instead="*[@type='Sitecore.Analytics.Pipelines.InitializeTracker.Robots, Sitecore.Analytics']" type ="Sitecore.Analytics.RobotDetection.Pipelines.InitializeTracker.Robots, Sitecore.Analytics.RobotDetection"/>
      </initializeTracker>
    </pipelines>
    <events>
      <event name="media:request">
        <handler patch:instead="*[@type='Sitecore.Analytics.Media.MediaRequestEventHandler, Sitecore.Analytics']" type="Sitecore.Analytics.RobotDetection.Media.MediaRequestEventHandler, Sitecore.Analytics.RobotDetection" method="OnMediaRequest"/>
      </event>
    </events>
  </sitecore>
</configuration>
