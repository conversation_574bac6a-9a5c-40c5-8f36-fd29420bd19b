﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <contentSearch>
      <!-- Configuration sections for indexes -->
      <indexConfigurations>

        <!-- If an index has no configuration specified, it will use the configuration below. The configuration is not merged if the index also has
             configuration, it is either this configuration or the index configuration. -->
        <defaultSolrIndexConfiguration type="Sitecore.ContentSearch.SolrProvider.SolrIndexConfiguration, Sitecore.ContentSearch.SolrProvider">
          <!-- This flag will index all fields by default. This allows new fields in your templates to automatically be included into the index.
               You have two choices : 
               
               1) Set this to true and place all the fields you would like to remove in the 'ExcludeField' list below.
               2) Set to false and place all fields you would like to be indexed in the 'IncludeField' list below.
            -->
          <indexAllFields>true</indexAllFields>

          <!-- Should index Initialize() method be called as soon as the index is added or wait for an external trigger -->
          <!-- For Solr Initialize() needs to be called after the IOC container has fired up -->
          <initializeOnAdd>false</initializeOnAdd>

          <!-- DEFAULT FIELD MAPPING 
               This field map allows you to take full control over how your data is stored in the index. This can affect the way data is queried, performance of searching and how data is retrieved and casted to a proper type in the API. 
            -->
          <fieldMap type="Sitecore.ContentSearch.SolrProvider.SolrFieldMap, Sitecore.ContentSearch.SolrProvider">
            <!-- This element must be first -->
            <typeMatches hint="raw:AddTypeMatch">
              <typeMatch typeName="guidCollection"     type="System.Collections.Generic.List`1[System.Guid]"     fieldNameFormat="{0}_sm"  multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="stringCollection"   type="System.Collections.Generic.List`1[System.String]"   fieldNameFormat="{0}_sm"  multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="intCollection"      type="System.Collections.Generic.List`1[System.Int32]"    fieldNameFormat="{0}_im"  multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="guid"               type="System.Guid"                                        fieldNameFormat="{0}_s"                                         settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="id"                 type="Sitecore.Data.ID, Sitecore.Kernel"                  fieldNameFormat="{0}_s"                                         settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="shortid"            type="Sitecore.Data.ShortID, Sitecore.Kernel"             fieldNameFormat="{0}_s"                                         settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="text"               type="System.String"                                      fieldNameFormat="{0}_t"   cultureFormat="_{1}"                  settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="string"             type="System.String"                                      fieldNameFormat="{0}_s"                                         settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="int"                type="System.Int32"                                       fieldNameFormat="{0}_tl"                                        settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="bool"               type="System.Boolean"                                     fieldNameFormat="{0}_b"                                         settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="datetime"           type="System.DateTime"                                    fieldNameFormat="{0}_tdt" format="yyyy-MM-dd'T'HH:mm:ss.FFF'Z'" settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="long"               type="System.Int64"                                       fieldNameFormat="{0}_tl"                                        settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="float"              type="System.Single"                                      fieldNameFormat="{0}_tf"                                        settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="double"             type="System.Double"                                      fieldNameFormat="{0}_td"                                        settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="stringArray"        type="System.String[]"                                    fieldNameFormat="{0}_sm"  multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="intArray"           type="System.Int32[]"                                     fieldNameFormat="{0}_im"  multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="datetimeArray"      type="System.DateTime[]"                                  fieldNameFormat="{0}_dtm" multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
              <typeMatch typeName="datetimeCollection" type="System.Collections.Generic.List`1[System.DateTime]" fieldNameFormat="{0}_dtm" multiValued="true"                    settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
            </typeMatches>

            <!-- This allows you to map a field name in Sitecore to the index and store it in the appropriate way -->
            <!-- Add schema fields here to enable multi-language processing -->
            <fieldNames hint="raw:AddFieldByFieldName">
              <field fieldName="__created_by"         returnType="string" />
              <field fieldName="__smallcreateddate"   returnType="datetime" format="yyyy-MM-dd'T'HH:mm:ss'Z'" />
              <field fieldName="__smallupdateddate"   returnType="datetime" format="yyyy-MM-dd'T'HH:mm:ss'Z'" />
              <field fieldName="__workflow_state"     returnType="string" />
              <field fieldName="extension"            returnType="text" />
              <field fieldName="title"                returnType="text" />
            </fieldNames>

            <!-- FIELD TYPE MAPPING
                 This allows you to map a field type in Sitecore to a type in the index.
                 USAGE: When you add new field types to Sitecore, add the mappings here so they work through the Linq Layer 
              -->
            <fieldTypes hint="raw:AddFieldByFieldTypeName">
              <fieldType fieldTypeName="checkbox"                                                                                                 returnType="bool"             />
              <fieldType fieldTypeName="date|datetime"                                                                                            returnType="datetime"         />
              <fieldType fieldTypeName="html|rich text|single-line text|multi-line text|text|memo|image|reference"                                returnType="text"             />
              <fieldType fieldTypeName="word document"                                                                                            returnType="text"             />
              <fieldType fieldTypeName="integer"                                                                                                  returnType="long"             />
              <fieldType fieldTypeName="number"                                                                                                   returnType="float"            />
              <fieldType fieldTypeName="icon|droplist|grouped droplist"                                                                           returnType="string"           />
              <fieldType fieldTypeName="checklist|multilist|treelist|tree list|treelistex|tree list|multilist with search|treelist with search"   returnType="stringCollection" />
              <fieldType fieldTypeName="name lookup value list|name value list"                                                                   returnType="stringCollection" />
              <fieldType fieldTypeName="droplink|droptree|grouped droplink|tree"                                                                  returnType="stringCollection" />
            </fieldTypes>
          </fieldMap>

          <!-- GLOBALLY EXCLUDE TEMPLATES FROM BEING INDEXED
               This setting allows you to exclude items that are based on specific templates from the index.
            -->
          <exclude hint="list:ExcludeTemplate">
            <BucketFolderTemplateId>{ADB6CA4F-03EF-4F47-B9AC-9CE2BA53FF97}</BucketFolderTemplateId>
          </exclude>

          <!-- GLOBALLY INCLUDE TEMPLATES IN INDEX
               This setting allows you to only include items that are based on specific templates in the index. You must specify all the
               templates that you want to include, because template inheritance is not checked. 
               When you enable this setting, all the items that are based on other templates are excluded, regardless of whether the template
               is specified in the ExcludeTemplate list or not.
            -->
          <!-- <include hint="list:IncludeTemplate">
            <BucketFolderTemplateId>{ADB6CA4F-03EF-4F47-B9AC-9CE2BA53FF97}</BucketFolderTemplateId>
            </include>-->

          <!-- GLOBALLY INCLUDE FIELDS IN INDEX
               This setting allows you to specify which fields to include in the index when the indexAllFields setting is set to false.
            -->
          <!--<include hint="list:IncludeField">
            <fieldId>{8CDC337E-A112-42FB-BBB4-4143751E123F}</fieldId>
            </include>-->

          <!-- GLOBALLY EXCLUDE FIELDS FROM BEING INDEXED
               This setting allows you to exclude fields from the index when the indexAllFields setting is set to true.
            -->
          <exclude hint="list:ExcludeField">
            <__Created>{25BED78C-4957-4165-998A-CA1B52F67497}</__Created>
            <__DefaultWorkflow>{CA9B9F52-4FB0-4F87-A79F-24DEA62CDA65}</__DefaultWorkflow>
            <__Lock>{001DD393-96C5-490B-924A-B0F25CD9EFD8}</__Lock>
            <__LongDescription>{577F1689-7DE4-4AD2-A15F-7FDC1759285F}</__LongDescription>
            <__Originator>{F6D8A61C-2F84-4401-BD24-52D2068172BC}</__Originator>
            <__Owner>{52807595-0F8F-4B20-8D2A-CB71D28C6103}</__Owner>
            <__ReadOnly>{9C6106EA-7A5A-48E2-8CAD-F0F693B1E2D4}</__ReadOnly>
            <__Renderings>{F1A1FE9E-A60C-4DDB-A3A0-BB5B29FE732E}</__Renderings>
            <__Revision>{8CDC337E-A112-42FB-BBB4-4143751E123F}</__Revision>
            <__Security>{DEC8D2D5-E3CF-48B6-A653-8E69E2716641}</__Security>
            <__ShortDescription>{9541E67D-CE8C-4225-803D-33F7F29F09EF}</__ShortDescription>
            <__SortOrder>{BA3F86A2-4A1C-4D78-B63D-91C2779C1B5E}</__SortOrder>
            <__Source>{1B86697D-60CA-4D80-83FB-7555A2E6CE1C}</__Source>
            <__Updated>{D9CF14B1-FA16-4BA6-9288-E8A174D4D522}</__Updated>
            <__UpdatedBy>{BADD9CF9-53E0-4D0C-BCC0-2D784C282F6A}</__UpdatedBy>
            <__ValidFrom>{C8F93AFE-BFD4-4E8F-9C61-152559854661}</__ValidFrom>
            <__Workflow>{A4F985D9-98B3-4B52-AAAF-4344F6E747C6}</__Workflow>
            <ArchiveDate>{56C15C6D-FD5A-40CA-BB37-64CEEC6A9BD5}</ArchiveDate>
            <ArchiveVersionDate>{1D99005E-65CA-45CA-9D9A-FD7016E23F1E}</ArchiveVersionDate>
            <Boost>{93D1B217-B8F4-462E-BABF-68298C9CE667}</Boost>
            <BucketParentReference>{9DAFCA1D-D618-4616-86B8-A8ACD6B28A63}</BucketParentReference>
            <Cacheable>{3D08DB46-2267-41B0-BC52-BE69FD618633}</Cacheable>
            <ContextMenu>{D3AE7222-425D-4B77-95D8-EE33AC2B6730}</ContextMenu>
            <Controller>{4C9312A5-2E4E-42F8-AB6F-B8DB8B82BF22}</Controller>
            <ControllerAction>{9FB734CC-8952-4072-A2D4-40F890E16F56}</ControllerAction>
            <DefaultBucketQuery>{AC51462C-8A8D-493B-9492-34D1F26F20F1}</DefaultBucketQuery>
            <DefaultView>{3607F9C7-DDA3-43C3-9720-39A7A5B3A4C3}</DefaultView>
            <Editor>{D85DB4EC-FF89-4F9C-9E7C-A9E0654797FC}</Editor>
            <Editors>{A0CB3965-8884-4C7A-8815-B6B2E5CED162}</Editors>
            <EnabledViews>{F2DB8BA1-E477-41F5-8EF5-22EEFA8D2F6E}</EnabledViews>
            <Facets>{21F74F6E-42D4-42A2-A4B4-4CEFBCFBD2BB}</Facets>
            <HelpLink>{56776EDF-261C-4ABC-9FE7-70C618795239}</HelpLink>
            <HideVersion>{B8F42732-9CB8-478D-AE95-07E25345FB0F}</HideVersion>
            <Icon>{06D5295C-ED2F-4A54-9BF2-26228D113318}</Icon>
            <Masters>{1172F251-DAD4-4EFB-A329-0C63500E4F1E}</Masters>
            <NeverPublish>{9135200A-5626-4DD8-AB9D-D665B8C11748}</NeverPublish>
            <PersistentFilter>{C7815F60-96E1-40CB-BB06-B5F833F73B61}</PersistentFilter>
            <Preview>{41C6CC0E-389F-4D51-9990-FE35417B6666}</Preview>
            <Publish>{86FE4F77-4D9A-4EC3-9ED9-263D03BD1965}</Publish>
            <ReminderDate>{ABE5D54C-59D7-41E6-8D3F-C1A3E4EC9B9E}</ReminderDate>
            <ReminderText>{BB6C8540-118E-4C49-9157-830576D7345A}</ReminderText>
            <Renderers>{B03569B1-1534-43F2-8C83-BD064B7D782C}</Renderers>
            <Ribbon>{0C894AAB-962B-4A84-B923-CB24B05E60D2}</Ribbon>
            <Skin>{079AFCFE-8ACA-4863-BDA7-07893541E2F5}</Skin>
            <Style>{A791F095-2521-4B4D-BEF9-21DDA221F608}</Style>
            <SubItemSorting>{6FD695E7-7F6D-4CA5-8B49-A829E5950AE9}</SubItemSorting>
            <SuppressedValidationRules>{F47C0D78-61F9-479C-96DF-1159727D32C6}</SuppressedValidationRules>
            <UnPublish>{7EAD6FD6-6CF1-4ACA-AC6B-B200E7BAFE88}</UnPublish>
            <UserAgent>{4E678FC0-8D35-4AB7-BB49-156F33C8B955}</UserAgent>
            <ValidTo>{4C346442-E859-4EFD-89B2-44AEDF467D21}</ValidTo>
            <VaryByData>{8B6D532B-6128-4486-A044-CA06D90948BA}</VaryByData>
            <VaryByDevice>{C98CF969-BA71-42DA-833D-B3FC1368BA27}</VaryByDevice>
            <VaryByLogin>{8D9232B0-613F-440B-A2FA-DCDD80FBD33E}</VaryByLogin>
            <VaryByParam>{3AD2506A-DC39-4B1E-959F-9D524ADDBF50}</VaryByParam>
            <VaryByQueryString>{1084D3D2-0457-456A-ABBC-EB4CC0966072}</VaryByQueryString>
            <VaryByUser>{0E54A8DC-72AD-4372-A7C7-BB4773FAD44D}</VaryByUser>
            <VaryByIndex>{F3E7E552-D7C8-469B-A150-69E4E14AB35C}</VaryByIndex>
          </exclude>

          <!-- REMOVE INBUILT SITECORE FIELDS
               This allows you to store a field in different ways in the index. You may want to store a field as Analyzed and Not Analyze
            -->
          <fields hint="raw:RemoveSpecialFields">
            <remove type="both">AllTemplates</remove>
            <remove type="both">Created</remove>
            <remove type="both">Editor</remove>
            <remove type="both">Hidden</remove>
            <remove type="both">Icon</remove>
            <remove type="both">Links</remove>
            <remove type="both">Updated</remove>
          </fields>

          <!-- COMPUTED INDEX FIELDS 
               This setting allows you to add fields to the index that contain values that are computed for the item that is being indexed.
               You can specify the storageType and indextype for each computed index field in the <fieldMap><fieldNames> section.
            -->
          <fields hint="raw:AddComputedIndexField">
            <field fieldName="__smallcreateddate"             returnType="string"          >Sitecore.ContentSearch.ComputedFields.CreatedDate,Sitecore.ContentSearch</field>
            <field fieldName="__smallupdateddate"             returnType="string"          >Sitecore.ContentSearch.ComputedFields.UpdatedDate,Sitecore.ContentSearch</field>
            <field fieldName="_content"                       returnType="string"     type="Sitecore.ContentSearch.ComputedFields.MediaItemContentExtractor,Sitecore.ContentSearch">
              <mediaIndexing ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration/mediaIndexing"/>
            </field>
            <field fieldName="calculateddimension"            returnType="stringCollection">Sitecore.ContentSearch.ComputedFields.CalculatedDimension,Sitecore.ContentSearch</field>
            <field fieldName="culture"                        returnType="string"          >Sitecore.ContentSearch.ComputedFields.Culture,Sitecore.ContentSearch</field>
            <field fieldName="haschildren"                    returnType="bool"            >Sitecore.ContentSearch.ComputedFields.HasChildren,Sitecore.ContentSearch</field>
            <field fieldName="istemplate"                     returnType="bool"            >Sitecore.ContentSearch.ComputedFields.IsTemplate,Sitecore.ContentSearch</field>
            <field fieldName="lock"                           returnType="bool"            >Sitecore.ContentSearch.ComputedFields.IsLocked,Sitecore.ContentSearch</field>
            <field fieldName="parsedcreatedby"                returnType="string"          >Sitecore.ContentSearch.ComputedFields.ParsedCreatedBy,Sitecore.ContentSearch</field>
            <field fieldName="parsedlanguage"                 returnType="string"          >Sitecore.ContentSearch.ComputedFields.ParsedLanguage,Sitecore.ContentSearch</field>
            <field fieldName="site"                           returnType="stringCollection">Sitecore.ContentSearch.ComputedFields.Site,Sitecore.ContentSearch</field>
            <field fieldName="sizerange"                      returnType="string"          >Sitecore.ContentSearch.ComputedFields.FileSizeGrouping,Sitecore.ContentSearch</field>
            <field fieldName="version"                        returnType="intCollection"   >Sitecore.ContentSearch.ComputedFields.StoreVersionTermVector,Sitecore.ContentSearch</field>
            <field fieldName="isbucket_text"                  returnType="string"          >Sitecore.ContentSearch.ComputedFields.IsBucket,Sitecore.ContentSearch</field>
            <!-- Disabled for speed of indexing. Enable if you would like to query by the fields below -->
            <!--<field fieldName="_isclone"                   returnType="bool"             >Sitecore.ContentSearch.ComputedFields.IsClone,Sitecore.ContentSearch</field>-->
            <!--<field fieldName="_links"                     returnType="string"           >Sitecore.ContentSearch.ComputedFields.ItemLinks, Sitecore.ContentSearch</field>-->
            <field fieldName="_templates"                 returnType="string"           >Aacn.Business.ContentSearch.ComputedFields.BaseTemplates, Aacn.Business</field>
            <!--<field fieldName="hasactivetest"              returnType="bool"             >Sitecore.ContentSearch.ComputedFields.IsBeingTested,Sitecore.ContentSearch</field>-->
            <!--<field fieldName="hasclones"                  returnType="bool"             >Sitecore.ContentSearch.ComputedFields.HasClones,Sitecore.ContentSearch</field>-->
            <!--<field fieldName="haspublishingrestrictions"  returnType="bool"             >Sitecore.ContentSearch.ComputedFields.HasPublishingRestrictions,Sitecore.ContentSearch</field>-->
            <!--<field fieldName="isinworkflow"               returnType="bool"             >Sitecore.ContentSearch.ComputedFields.IsItemInWorkflow,Sitecore.ContentSearch</field>-->
            <!--<field fieldName="persona"                    returnType="string"           >Sitecore.ContentSearch.ComputedFields.PersonaMatch,Sitecore.ContentSearch</field>-->
          </fields>

          <!-- MEDIA ITEM CONTENT EXTRACTOR FILE MAPPING 
               This map allows you to specify the extensions and mimetypes that we will pass through to the IFilters on your machine so they can be indexed.
               We also allow you to include all files or exclude all files and leave it to the IFilters to control what is and is not indexed.
          -->
          <mediaIndexing hint="skip">
            <mimeTypes>
              <excludes>
                <mimeType>application/pdf</mimeType>
                <mimeType>*</mimeType>
              </excludes>
              <includes>
                <mimeType type="Sitecore.ContentSearch.ComputedFields.MediaItemHtmlTextExtractor, Sitecore.ContentSearch">text/html</mimeType>
                <mimeType>text/plain</mimeType>
              </includes>
            </mimeTypes>
            <extensions>
              <excludes>
                <extension>doc</extension>
                <extension>docm</extension>
                <extension>docx</extension>
                <extension>dot</extension>
                <extension>dotm</extension>
                <extension>dotx</extension>
                <extension>odt</extension>
                <extension>pot</extension>
                <extension>potm</extension>
                <extension>potx</extension>
                <extension>ppa</extension>
                <extension>ppam</extension>
                <extension>pps</extension>
                <extension>ppsm</extension>
                <extension>ppsx</extension>
                <extension>ppt</extension>
                <extension>pptm</extension>
                <extension>pptx</extension>
                <extension>rtf</extension>
                <extension>xla</extension>
                <extension>xlam</extension>
                <extension>xls</extension>
                <extension>xlsb</extension>
                <extension>xlsm</extension>
                <extension>xlsx</extension>
                <extension>xlt</extension>
                <extension>xltm</extension>
                <extension>*</extension>
              </excludes>
              <includes>
              </includes>
            </extensions>
          </mediaIndexing>

          <!-- VIRTUAL FIELD PROCESSORS
               Virtual fields can be used to translate a field query into a different query.
            -->
          <virtualFieldProcessors hint="raw:AddVirtualFieldProcessor">
            <virtualFieldProcessor fieldName="daterange" type="Sitecore.ContentSearch.VirtualFields.DateRangeFieldProcessor, Sitecore.ContentSearch" />
            <virtualFieldProcessor fieldName="_lastestversion" type="Sitecore.ContentSearch.VirtualFields.LatestVersionFieldProcessor, Sitecore.ContentSearch" />
            <virtualFieldProcessor fieldName="updateddaterange" type="Sitecore.ContentSearch.VirtualFields.UpdatedDateRangeFieldProcessor, Sitecore.ContentSearch" />
            <virtualFieldProcessor fieldName="_url" type="Sitecore.ContentSearch.VirtualFields.UniqueIdFieldProcessor, Sitecore.ContentSearch" />
            <virtualFieldProcessor fieldName="_fullpath" type="Sitecore.ContentSearch.VirtualFields.FullPathFieldProcessor, Sitecore.ContentSearch" />
          </virtualFieldProcessors>

          <!-- SITECORE FIELDTYPE MAP
               This maps a field type by name to a Strongly Typed Implementation of the field type e.g. html maps to HTMLField
            -->
          <fieldReaders type="Sitecore.ContentSearch.FieldReaders.FieldReaderMap, Sitecore.ContentSearch">
            <mapFieldByTypeName hint="raw:AddFieldReaderByFieldTypeName">
              <fieldReader fieldTypeName="checkbox"                                             fieldReaderType="Sitecore.ContentSearch.FieldReaders.CheckboxFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="date|datetime"                                        fieldReaderType="Sitecore.ContentSearch.FieldReaders.DateFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="image"                                                fieldReaderType="Sitecore.ContentSearch.FieldReaders.ImageFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="single-line text|multi-line text|text|memo"           fieldReaderType="Sitecore.ContentSearch.FieldReaders.DefaultFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="integer"                                              fieldReaderType="Sitecore.ContentSearch.FieldReaders.NumericFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="number"                                               fieldReaderType="Sitecore.ContentSearch.FieldReaders.PrecisionNumericFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="html|rich text"                                       fieldReaderType="Sitecore.ContentSearch.FieldReaders.RichTextFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="multilist with search|treelist with search"           fieldReaderType="Sitecore.ContentSearch.FieldReaders.DelimitedListFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="checklist|multilist|treelist|treelistex|tree list"    fieldReaderType="Sitecore.ContentSearch.FieldReaders.MultiListFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="icon|droplist|grouped droplist"                       fieldReaderType="Sitecore.ContentSearch.FieldReaders.DefaultFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="name lookup value list|name value list"               fieldReaderType="Sitecore.ContentSearch.FieldReaders.NameValueListFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="droplink|droptree|grouped droplink|tree|reference"    fieldReaderType="Sitecore.ContentSearch.FieldReaders.LookupFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="attachment|frame|rules|tracking|thumbnail"            fieldReaderType="Sitecore.ContentSearch.FieldReaders.NullFieldReader, Sitecore.ContentSearch" />
              <fieldReader fieldTypeName="file|security|server file|template field source|link" fieldReaderType="Sitecore.ContentSearch.FieldReaders.NullFieldReader, Sitecore.ContentSearch" />
            </mapFieldByTypeName>
          </fieldReaders>

          <!-- INDEX FIELD STORAGE MAPPER 
               Maintains a collection of all the possible Convertors for the provider.
            -->
          <indexFieldStorageValueFormatter type="Sitecore.ContentSearch.SolrProvider.Converters.SolrIndexFieldStorageValueFormatter, Sitecore.ContentSearch.SolrProvider">
            <converters hint="raw:AddConverter">
              <converter handlesType="System.Guid"                                                          typeConverter="Sitecore.ContentSearch.Converters.IndexFieldGuidValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.Data.ID, Sitecore.Kernel"                                    typeConverter="Sitecore.ContentSearch.Converters.IndexFieldIDValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.Data.ShortID, Sitecore.Kernel"                               typeConverter="Sitecore.ContentSearch.Converters.IndexFieldShortIDValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="System.DateTime"                                                      typeConverter="Sitecore.ContentSearch.Converters.IndexFieldUTCDateTimeValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="System.DateTimeOffset"                                                typeConverter="Sitecore.ContentSearch.Converters.IndexFieldDateTimeOffsetValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="System.TimeSpan"                                                      typeConverter="Sitecore.ContentSearch.Converters.IndexFieldTimeSpanValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.ContentSearch.SitecoreItemId, Sitecore.ContentSearch"        typeConverter="Sitecore.ContentSearch.Converters.IndexFieldSitecoreItemIDValueConvertor, Sitecore.ContentSearch">
                <param type="Sitecore.ContentSearch.Converters.IndexFieldIDValueConverter, Sitecore.ContentSearch"/>
              </converter>
              <converter handlesType="Sitecore.ContentSearch.SitecoreItemUniqueId, Sitecore.ContentSearch"  typeConverter="Sitecore.ContentSearch.SolrProvider.Converters.SolrIndexFieldSitecoreItemUniqueIDValueConverter, Sitecore.ContentSearch.SolrProvider">
                <param type="Sitecore.ContentSearch.Converters.IndexFieldItemUriValueConverter, Sitecore.ContentSearch"/>
              </converter>
              <converter handlesType="Sitecore.Data.ItemUri, Sitecore.Kernel"                               typeConverter="Sitecore.ContentSearch.Converters.IndexFieldItemUriValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.Globalization.Language, Sitecore.Kernel"                     typeConverter="Sitecore.ContentSearch.Converters.IndexFieldLanguageValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="System.Globalization.CultureInfo"                                     typeConverter="Sitecore.ContentSearch.Converters.IndexFieldCultureInfoValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.Data.Version, Sitecore.Kernel"                               typeConverter="Sitecore.ContentSearch.Converters.IndexFieldVersionValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.Data.Database, Sitecore.Kernel"                              typeConverter="Sitecore.ContentSearch.Converters.IndexFieldDatabaseValueConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.ContentSearch.IIndexableId, Sitecore.ContentSearch"          typeConverter="Sitecore.ContentSearch.Converters.IndexableIdConverter, Sitecore.ContentSearch" />
              <converter handlesType="Sitecore.ContentSearch.IIndexableUniqueId, Sitecore.ContentSearch"    typeConverter="Sitecore.ContentSearch.Converters.IndexableUniqueIdConverter, Sitecore.ContentSearch" />
            </converters>
          </indexFieldStorageValueFormatter>

          <!-- INDEX PROPERTY TO DOCUMENT MAPPER
               Maintains a collection of all the possible Convertors for the provider.
            -->
          <indexDocumentPropertyMapper type="Sitecore.ContentSearch.SolrProvider.Mapping.SolrDocumentPropertyMapper, Sitecore.ContentSearch.SolrProvider">
            <!-- OBJECT FACTORY
                 Constructs search result objects based on the type that is passed in .GetQueryable<T>() and the rules defined in this section.
            -->
            <objectFactory type="Sitecore.ContentSearch.DefaultDocumentMapperObjectFactory, Sitecore.ContentSearch">
              <!-- OBJECT FACTORY RULES 

                   Examples:

                    <rules hint="list:AddRule">

                      Rule that applies to items with template "Sample Item":

                      <rule fieldName="template" comparison="Equal" value="{76036F5E-CBCE-46D1-AF0A-4143F9B557AA}" valueType="System.Guid, mscorlib"
                            creationType="MySearchTypes.SampleItemResultItem, MySearchTypes"
                            baseType="MySearchTypes.IMySearchResultItem, MySearchTypes"
                            type="Sitecore.ContentSearch.DefaultDocumentMapperFactorySimpleRule, Sitecore.ContentSearch">
                        <param desc="fieldName">$(fieldName)</param>
                        <param desc="comparison">$(comparison)</param>
                        <param desc="value">$(value)</param>
                        <param desc="type">$(valueType)</param>
                        <param desc="creationType">$(creationType)</param>
                        <param desc="baseType">$(baseType)</param>
                      </rule>

                      Rule that applies to items with template "Sample Item" AND has the title "Sample Item":

                      <rule type="Sitecore.ContentSearch.DefaultDocumentMapperFactoryRule, Sitecore.ContentSearch"
                            creationType="MySearchTypes.SampleItemResultItem, MySearchTypes"
                            baseType="MySearchTypes.IMySearchResultItem, MySearchTypes">
                        <param desc="creationType">$(creationType)</param>
                        <param desc="baseType">$(baseType)</param>
                  
                        <fieldComparisons hint="list:AddFieldComparison">

                          <fieldComparison fieldName="template" comparison="Equal" value="{76036F5E-CBCE-46D1-AF0A-4143F9B557AA}" valueType="System.Guid, mscorlib" type="Sitecore.ContentSearch.DefaultDocumentMapperFactoryRuleFieldComparison, Sitecore.ContentSearch">
                            <param desc="fieldName">$(fieldName)</param>
                            <param desc="comparison">$(comparison)</param>
                            <param desc="value">$(value)</param>
                            <param desc="type">$(valueType)</param>
                          </fieldComparison>

                          <fieldComparison fieldName="title" comparison="Equal" value="Sample Item" valueType="System.String, mscorlib" type="Sitecore.ContentSearch.DefaultDocumentMapperFactoryRuleFieldComparison, Sitecore.ContentSearch">
                            <param desc="fieldName">$(fieldName)</param>
                            <param desc="comparison">$(comparison)</param>
                            <param desc="value">$(value)</param>
                            <param desc="type">$(valueType)</param>
                          </fieldComparison>

                        </fieldComparisons>
                      </rule>
                
                    </rules>
              -->
            </objectFactory>
          </indexDocumentPropertyMapper>

          <!-- DOCUMENT BUILDER
               Allows you to override the document builder. The document builder class processes all the fields in the Sitecore items and prepares
               the data for storage in the index.
               You can override the document builder to modify how the data is prepared, and to apply any additional logic that you may require.
          -->
          <documentBuilderType>Sitecore.ContentSearch.SolrProvider.SolrDocumentBuilder, Sitecore.ContentSearch.SolrProvider</documentBuilderType>

          <defaultSearchSecurityOption ref="contentSearch/indexConfigurations/defaultSearchSecurityOption" />

        </defaultSolrIndexConfiguration>
      </indexConfigurations>
    </contentSearch>
    <pipelines>
      <contentSearch.translateQuery>
        <processor type="Sitecore.ContentSearch.SolrProvider.Pipelines.TranslateQuery.ApplySolrTranslation, Sitecore.ContentSearch.SolrProvider" />
      </contentSearch.translateQuery>
    </pipelines>
    <settings>

      <!--  CONTENT SEARCH SEARCH MAX RESULTS
            The max number of results that a query returns.
            If a limit is not specified in the query (ie Take(10)) the search will use this value to set the number of rows returned.
            It is important, for performance, that only the rows you need for a single request or page of results is returned.
      -->
      <setting name="ContentSearch.SearchMaxResults" value="500" />

      <!--  SEARCH PROVIDER
            Set to which provider is currently being used
            (Options: Lucene or Solr)
      -->
      <setting name="ContentSearch.Provider" value="Solr" />

      <!-- DEFAULT INDEX CONFIGURATION PATH
           If an index has no configuration specified then it uses this XPath to find the default.
           Note: If you have multiple provider types active at once make sure the configurations are individually referenced rather than relying on the default. 
      -->
      <setting name="ContentSearch.DefaultIndexConfigurationPath" value="contentSearch/indexConfigurations/defaultSolrIndexConfiguration" />

      <!--  SERVICE BASE ADDRESS
            Base url of the Solr server. (minus any cores and minus a trailing slash)
      -->
      
      <!--duplicate entry in XtendSitecoreSettings-->
      <!--<setting name="ContentSearch.Solr.ServiceBaseAddress" value="http://localhost:8983/solr" />-->

      <!-- CONTENT SEARCH – SOLR - ENABLE HTTP CACHE
           This setting specifies whether the HTTP Request cache is enabled. When enabled, every request is stored for 10 minutes or until
           the item or the result set changes.
           HTTP request caching only works if you also enable HTTP caching and ETags on the Solr server.
           Default value: true
      -->
      <setting name="ContentSearch.Solr.EnableHttpCache" value="true" />

      <!--  BATCH MODE
            Commits to the database in batches (to reduce trips to the database / server).
      -->
      <setting name="ContentSearch.Update.BatchModeEnabled" value="true" />

      <!--  BATCH SIZE
            The size of document batch before flushing to the database.
      -->
      <setting name="ContentSearch.Update.BatchSize" value="500" />

    </settings>
  </sitecore>
</configuration>
