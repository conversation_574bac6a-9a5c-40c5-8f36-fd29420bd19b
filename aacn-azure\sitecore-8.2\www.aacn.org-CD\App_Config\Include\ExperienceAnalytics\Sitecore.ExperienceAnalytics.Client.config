﻿<!--

Purpose: This include file needs to be enabled in Content Management Environment.

It enables the components and features related to Experience Analytics client features.

-->
<configuration xmlns:x="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <experienceAnalytics>
      <client>
        <services>
          <!-- SegmentDefinitionService is responsible for reading data from the Segments dimension table in RDB.
               The 'connectionStringName' parameter the name of the connection string of the RDB instance to connect to.
               Default: "reporting"
          -->
          <segmentDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.DataProviderSegmentDefinitionService, Sitecore.ExperienceAnalytics" />
          <!-- SiteDefinitionService is responsible for reading data from the SiteNames dimension table in RDB.
               The 'connectionStringName' parameter the name of the connection string of the RDB instance to connect to.
               Default: "reporting"
          -->
          <siteDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.DataProviderSiteDefinitionService, Sitecore.ExperienceAnalytics" />
        </services>
      </client>
    </experienceAnalytics>
    <pipelines>
      <initialize>
        <!-- One-time run code that updates the value of DeployDate to UTC.NOW.
             Processes only the segments shipped out of the box and have DeployDate set to NULL in RDB
        -->
        <processor type="Sitecore.ExperienceAnalytics.Client.SegmentInitializer, Sitecore.ExperienceAnalytics.Client" x:after="processor[position()=last()]" />
      </initialize>
      <deployDefinition>
        <processor type="Sitecore.ExperienceAnalytics.Client.Workflow.DeploySegmentDefinition, Sitecore.ExperienceAnalytics.Client"/>
      </deployDefinition>
    </pipelines>
  </sitecore>
</configuration>