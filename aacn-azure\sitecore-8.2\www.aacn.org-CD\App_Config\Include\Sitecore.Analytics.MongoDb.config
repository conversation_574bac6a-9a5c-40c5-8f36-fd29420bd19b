﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <sitecore>
    <!--
      MongoDB settings node.
      * `recoveryTimeout` - time to wait before a connection to the database is
        reattempted after it was marked as offline.
    -->
    <mongo recoveryTimeout="60">
      <driver type="Sitecore.Analytics.Data.DataAccess.MongoDb.MongoDbDriver, Sitecore.Analytics.MongoDB">
        <param desc="connectionString">$(0)</param>
        <param desc="failOnReadErrors" ref="settings/setting[@name='Analytics.FailOnDatabaseErrors']/@value" />
      </driver>
    </mongo>
  </sitecore>
</configuration>