﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <httpRequestBegin>
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.SetRuntimeSettings, Sitecore.ItemWebApi" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.SiteResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.CheckMode, Sitecore.ItemWebApi" patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.UserResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.ResolveUserFromRequest, Sitecore.ItemWebApi" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.UserResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.ResolveLanguage, Sitecore.ItemWebApi" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.LanguageResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.HandleActionRequest, Sitecore.ItemWebApi" patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.QueryStringResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.LaunchRequest, Sitecore.ItemWebApi" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']" />
      </httpRequestBegin>
      <httpRequestEnd>
        <processor type="Sitecore.ItemWebApi.Pipelines.HttpRequest.CatchError, Sitecore.ItemWebApi" patch:before="processor[@type='Sitecore.Pipelines.PreprocessRequest.CheckIgnoreFlag, Sitecore.Kernel']" />
      </httpRequestEnd>
      <preprocessRequest>
        <processor type="Sitecore.ItemWebApi.Pipelines.PreprocessRequest.RewriteUrl, Sitecore.ItemWebApi" patch:before="processor[@type='Sitecore.Pipelines.PreprocessRequest.CheckIgnoreFlag, Sitecore.Kernel']" />
      </preprocessRequest>
      <!--Handles the item delete operation. -->
      <itemWebApiDelete>
        <processor type="Sitecore.ItemWebApi.Pipelines.Delete.DeleteScope, Sitecore.ItemWebApi" />
      </itemWebApiDelete>
      <!--Determines which item fields should are included in response. -->
      <itemWebApiGetFields>
        <processor type="Sitecore.ItemWebApi.Pipelines.GetFields.GetFields, Sitecore.ItemWebApi" />
      </itemWebApiGetFields>
      <!--Determines which item properties (display name, ID etc.) are included in response. -->
      <itemWebApiGetProperties>
        <processor type="Sitecore.ItemWebApi.Pipelines.GetProperties.GetProperties, Sitecore.ItemWebApi" />
      </itemWebApiGetProperties>
      <!--Handles the item create operation. -->
      <itemWebApiCreate>
        <processor type="Sitecore.ItemWebApi.Pipelines.Create.CreateItem, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Create.TryUpdate, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Create.SetResult, Sitecore.ItemWebApi" />
      </itemWebApiCreate>
      <!--Handles the item read operation. -->
      <itemWebApiRead>
        <processor type="Sitecore.ItemWebApi.Pipelines.Read.GetResult, Sitecore.ItemWebApi" />
      </itemWebApiRead>
      <!--Processes Item Web API requests. -->
      <itemWebApiRequest>
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.CheckParameters, Sitecore.ItemWebApi" >
          <parameters hint="list">
            <name desc="item id1">sc_itemid</name>
            <name desc="item id2">sc_database</name>
            <name desc="item id3">language</name>
            <name desc="item id4">fields</name>
            <name desc="item id5">payload</name>
            <name desc="item id6">scope</name>
            <name desc="item id7">query</name>
            <name desc="item id8">page</name>
            <name desc="item id9">template</name>
            <name desc="item id10">name</name>
            <name desc="item id11">database</name>
            <name desc="item id12">pagesize</name>
            <name desc="item id13">renderingid</name>
            <name desc="item id14">sc_itemversion</name>
          </parameters>
        </processor>
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.CheckSecurity, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.HandleItemNotFound, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.ResolveItems, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.ResolveScope, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.ResolveAction, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.SerializeResponse, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.SendResponse, Sitecore.ItemWebApi" />
      </itemWebApiRequest>
      <!--Handles the item update operation. -->
      <itemWebApiUpdate>
        <processor type="Sitecore.ItemWebApi.Pipelines.Update.UpdateScope, Sitecore.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Update.ReadUpdatedScope, Sitecore.ItemWebApi" />
      </itemWebApiUpdate>
    </pipelines>
    <sites>
      <!-- ITEM WEB API SETTINGS FOR A SITE
           Supported attributes (first is default):
             itemwebapi.mode: [Off|StandardSecurity|AdvancedSecurity]
               If set to Off, Item Web API is turned off.
               If set to StandardSecurity, Item Web API is turned on. Default Sitecore security model is used.
               If set to AdvancedSecurity, Item Web API is turned on. Default Sitecore security model is extended with a requirement to explicitely set the 'remote:fieldread' access right for content fields.
             itemwebapi.access: [ReadOnly|ReadWrite]
               If set to ReadOnly, then only READ operation is allowed.
               If set to ReadWrite, then CREATE, READ, UPDATE, and DELETE operations are allowed.
             itemwebapi.allowanonymousaccess: [false|true].
               Defines if access is allowed for non-authenticated user.
      -->
      <site name="website">
        <patch:attribute name="itemwebapi.mode">Off</patch:attribute>
        <patch:attribute name="itemwebapi.access">ReadOnly</patch:attribute>
        <patch:attribute name="itemwebapi.allowanonymousaccess">false</patch:attribute>
      </site>
    </sites>

    <accessRights>
      <rights>
        <add name="remote:fieldread" comment="Field Read right for remoted clients." title="Field Remote Read"/>
      </rights>
      <rules>
        <add prefix="remote:field" templateId="{455A3E98-A627-4B40-8035-E683A0331AC7}" comment="remote:field for remote access to fields." />
      </rules>
    </accessRights>
    <cryptographyManager defaultProvider="rsa">
      <providers>
        <clear />
        <add name="rsa" type="Sitecore.ItemWebApi.Security.Cryptography.RSAProvider, Sitecore.ItemWebApi"/>
      </providers>
    </cryptographyManager>
    <settings>
      <!--  OUTPUT EXCEPTION DETAILS
            If set to 'true', exception output includes type of an exception and a stack trace;
            otherwise, only an exception message.
            Default: false
      -->
      <setting name="ItemWebApi.OutputExceptionDetails" value="false" />
    </settings>
    <itemWebApi type="Sitecore.ItemWebApi.Configuration.ItemWebApiConfiguration, Sitecore.ItemWebApi" singleInstance="true">
      <actions hint="list:AddAction">
        <action id="authenticate" type="Sitecore.ItemWebApi.Actions.AuthenticateAction, Sitecore.ItemWebApi">
          <param desc="name">$(id)</param>
        </action>
        <action id="getpublickey" type="Sitecore.ItemWebApi.Actions.GetPublicKeyAction, Sitecore.ItemWebApi">
          <param desc="name">$(id)</param>
        </action>
        <action id="getrenderinghtml" type="Sitecore.ItemWebApi.Actions.GetRenderingHtmlAction, Sitecore.ItemWebApi">
          <param desc="name">$(id)</param>
        </action>
        <action id="getsignedmediaurl" type="Sitecore.ItemWebApi.Actions.GetSignedMediaUrlAction, Sitecore.ItemWebApi">
          <param desc="name">$(id)</param>
        </action>
      </actions>
    </itemWebApi>
  </sitecore>
</configuration>