﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file contains configuration settings for the Sitecore Path Analyzer Web API services.

This file is required by the PathAnalyzer client. It can be removed/disabled from Sitecore CD (Content Delivery).

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- PIPELINES -->
    <pipelines>
      <initialize>
        <processor type="Sitecore.PathAnalyzer.Services.Pipelines.Initialize.RegisterHttpRoutes, Sitecore.PathAnalyzer.Services" />
        <processor type="Sitecore.PathAnalyzer.Services.Pipelines.Initialize.RegisterFormatters, Sitecore.PathAnalyzer.Services" />
        <processor type="Sitecore.PathAnalyzer.Services.Pipelines.Initialize.RegisterFilters, Sitecore.PathAnalyzer.Services" />
        <processor type="Sitecore.PathAnalyzer.Services.Pipelines.Initialize.RegisterModelBinding, Sitecore.PathAnalyzer.Services" />
        <processor type="Sitecore.PathAnalyzer.Services.Pipelines.Initialize.RegisterHttpControllerActivator, Sitecore.PathAnalyzer.Services" />
      </initialize>
    </pipelines>
    <!-- PATH ANALYZER
          Define the types used by Path Analyzer Web API controllers and services
    -->
    <pathAnalyzer>
      <!-- TREE MANAGER
            A facade service used by the Path Analyzer Web API controllers for managing map data retrieval and caching.
      -->
      <treeManager type="Sitecore.PathAnalyzer.Data.TreeManager, Sitecore.PathAnalyzer" singleInstance="true">
        <param desc="provider" ref="pathAnalyzer/treeProvider" />
        <param desc="cache" ref="pathAnalyzer/treeCache" />
      </treeManager>
    </pathAnalyzer>
    <!-- CONTROLLERS -->
    <controllers>
      <!-- PATHS CONTOLLER
           A Web API controller with actions related to retrieving path data.
      -->
      <controller type="Sitecore.PathAnalyzer.Services.Controllers.PathsController, Sitecore.PathAnalyzer.Services">
        <param desc="treeManager" ref="pathAnalyzer/treeManager" />
        <param desc="configuration" ref="pathAnalyzer/configuration" />
      </controller>
      <!-- CONTACTS BY PATH CONTOLLER
           A Web API controller with actions related to retrieving contacts based on a particular path.
      -->
      <controller type="Sitecore.PathAnalyzer.Services.Controllers.ContactsByPathController, Sitecore.PathAnalyzer.Services">
        <param desc="treeManager" ref="pathAnalyzer/treeManager" />
        <param desc="contactReader" ref="pathAnalyzer/contactReader" />
      </controller>
      <!-- TREE CONTOLLER
           A Web API controller with actions related to retrieving map data.
      -->
      <controller type="Sitecore.PathAnalyzer.Services.Controllers.TreeController, Sitecore.PathAnalyzer.Services">
        <param desc="treeManager" ref="pathAnalyzer/treeManager" />
        <param desc="configuration" ref="pathAnalyzer/configuration" />
      </controller>
      <!-- EXPLORER CONTOLLER
            A Web API controller with actions related to retrieving map and report data for the
            Path Analyzer Silverlight application.
      -->
      <controller type="Sitecore.PathAnalyzer.Services.PathExplorer.Controllers.ExplorerController, Sitecore.PathAnalyzer.Services">
        <param desc="treeManager" ref="pathAnalyzer/treeManager" />
        <param desc="configuration" ref="pathAnalyzer/configuration" />
      </controller>
    </controllers>
    <!-- API -->
    <api>
      <services>
        <configuration type="Sitecore.Services.Infrastructure.Configuration.ServicesConfiguration, Sitecore.Services.Infrastructure">
          <allowedControllers hint="list:AddController">
            <allowedController desc="PA_ExplorerController">Sitecore.PathAnalyzer.Services.PathExplorer.Controllers.ExplorerController, Sitecore.PathAnalyzer.Services</allowedController>
            <allowedController desc="PA_PathsController">Sitecore.PathAnalyzer.Services.Controllers.PathsController, Sitecore.PathAnalyzer.Services</allowedController>
            <allowedController desc="PA_TreeController">Sitecore.PathAnalyzer.Services.Controllers.TreeController, Sitecore.PathAnalyzer.Services</allowedController>
            <allowedController desc="PA_ContactsByPathController">Sitecore.PathAnalyzer.Services.Controllers.ContactsByPathController, Sitecore.PathAnalyzer.Services</allowedController>
          </allowedControllers>
        </configuration>
      </services>
    </api>
  </sitecore>
</configuration>
