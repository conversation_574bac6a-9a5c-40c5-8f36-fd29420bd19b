﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <model>

      <elements>
        <element interface="Sitecore.Analytics.Model.Entities.IContactAddresses, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactAddresses, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactEmailAddresses, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactEmailAddresses, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactIdentifiers, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactIdentifiers, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactPersonalInfo, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactPersonalInfo, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactPicture, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactPicture, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactPhoneNumbers, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactPhoneNumbers, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IAddress, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.Address, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IEmailAddress, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.EmailAddress, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IExtensionAttribute, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ExtensionAttribute, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IExtensionAttributeGroup, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ExtensionAttributeGroup, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IGeographicCoordinate, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.GeographicCoordinate, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IPhoneNumber, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.PhoneNumber, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.ITag, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.Tag, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.ITagValue, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.TagValue, Sitecore.Analytics.Model"/>
        <element interface="Sitecore.Analytics.Model.Entities.IContactCommunicationProfile, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactCommunicationProfile, Sitecore.Analytics.Model" />
        <element interface="Sitecore.Analytics.Model.Entities.IContactPreferences, Sitecore.Analytics.Model" implementation="Sitecore.Analytics.Model.Generated.ContactPreferences, Sitecore.Analytics.Model" />
      </elements>

      <entities>
        <contact>
          <factory type="Sitecore.Analytics.Data.ContactFactory, Sitecore.Analytics" singleInstance="true" />
          <template type="Sitecore.Analytics.Data.ContactTemplateFactory, Sitecore.Analytics" singleInstance="true" />
          <facets>
            <facet name="Personal" contract="Sitecore.Analytics.Model.Entities.IContactPersonalInfo, Sitecore.Analytics.Model" />
            <facet name="Addresses" contract="Sitecore.Analytics.Model.Entities.IContactAddresses, Sitecore.Analytics.Model" />
            <facet name="Emails" contract="Sitecore.Analytics.Model.Entities.IContactEmailAddresses, Sitecore.Analytics.Model" />
            <facet name="Phone Numbers" contract="Sitecore.Analytics.Model.Entities.IContactPhoneNumbers, Sitecore.Analytics.Model" />
            <facet name="Picture" contract="Sitecore.Analytics.Model.Entities.IContactPicture, Sitecore.Analytics.Model" />
            <facet name="Communication Profile" contract="Sitecore.Analytics.Model.Entities.IContactCommunicationProfile, Sitecore.Analytics.Model" />
            <facet name="Preferences" contract="Sitecore.Analytics.Model.Entities.IContactPreferences, Sitecore.Analytics.Model" />
          </facets>
        </contact>
      </entities>
    </model>
 
  </sitecore>
</configuration>
