﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the processing subsystem of the Sitecore CEP.

You should disable this file in case the current server does not need to perform processing tasks, such as executing scheduled Sitecore tasks.
To disable the file, you can rename it so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <scheduling>
      <!-- An agent that processes scheduled tasks embedded as items in the core database. -->
      <agent type="Sitecore.Tasks.DatabaseAgent" method="Run" interval="00:10:00" name="Core_Database_Agent">
        <param desc="database">core</param>
        <param desc="schedule root">/sitecore/system/tasks/schedules</param>
        <LogActivity>true</LogActivity>
      </agent>
      <!-- An agent that processes scheduled tasks embedded as items in the master database. -->
      <agent type="Sitecore.Tasks.DatabaseAgent" method="Run" interval="00:10:00" name="Master_Database_Agent">
        <param desc="database">master</param>
        <param desc="schedule root">/sitecore/system/tasks/schedules</param>
        <LogActivity>true</LogActivity>
      </agent>
    </scheduling>
  </sitecore>
</configuration>