﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose: This include file configures the content testing features of Sitecore for MVC based sites.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <mvc.getPageItem>
        <processor type="Sitecore.ContentTesting.Mvc.Pipelines.Response.GetPageItem.GetFromTestSet, Sitecore.ContentTesting.Mvc"/>
      </mvc.getPageItem>

      <mvc.requestBegin>
        <processor type="Sitecore.ContentTesting.Mvc.Pipelines.Response.RequestBegin.EvaluateTestExposure, Sitecore.ContentTesting.Mvc" patch:after="processor[@type='Sitecore.Mvc.Analytics.Pipelines.MvcEvents.RequestBegin.StartTracking, Sitecore.Mvc.Analytics']"/>
      </mvc.requestBegin>
    </pipelines>
  </sitecore>
</configuration>