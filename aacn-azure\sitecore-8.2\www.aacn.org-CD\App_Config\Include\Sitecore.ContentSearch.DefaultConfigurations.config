﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <contentSearch>
      <indexConfigurations>
        <!-- INDEXING STRATEGIES 
             Here is a list of the different indexing strategies that you can use. You can also combine these strategies to achieve what you want.
        -->
        <indexUpdateStrategies>

          <!-- INTERVAL BASED INDEX REBUILD STRATEGY FOR CORE DATABASE
               This strategy uses an interval based trigger and the history engine from the predefined 'core' database to incrementally rebuild the index. 
          -->
          <intervalAsyncCore type="Sitecore.ContentSearch.Maintenance.Strategies.IntervalAsynchronousStrategy, Sitecore.ContentSearch">
            <param desc="database">core</param>
            <param desc="interval">00:01:00</param>
            <!-- Whether or not a full index rebuild should be triggered when the number of items in the history engine exceeds the number specified 
                 in ContentSearch.FullRebuildItemCountThreshold. -->
            <CheckForThreshold>true</CheckForThreshold>
          </intervalAsyncCore>

          <!-- INTERVAL BASED INDEX REBUILD STRATEGY FOR MASTER DATABASE
               This strategy uses an interval based trigger and the history engine from the predefined 'master' database to incrementally rebuild the index. 
          -->
          <intervalAsyncMaster type="Sitecore.ContentSearch.Maintenance.Strategies.IntervalAsynchronousStrategy, Sitecore.ContentSearch">
            <param desc="database">master</param>
            <param desc="interval">00:00:05</param>
            <!-- Whether or not a full index rebuild should be triggered when the number of items in the history engine exceeds the number specified 
                 in ContentSearch.FullRebuildItemCountThreshold. -->
            <CheckForThreshold>true</CheckForThreshold>
          </intervalAsyncMaster>

          <!-- AUTOMATIC INDEXING DISABLED STRATEGY 
               Every index that uses this strategy must be manually rebuilt. 
          -->
          <manual type="Sitecore.ContentSearch.Maintenance.Strategies.ManualStrategy, Sitecore.ContentSearch" />

          <!-- REINDEX ON PUBLISH END
               This strategy is triggered on publish:end and uses the EventQueue to incrementally rebuild the index.  
          -->
          <onPublishEndAsync type="Sitecore.ContentSearch.Maintenance.Strategies.OnPublishEndAsynchronousStrategy, Sitecore.ContentSearch">
            <param desc="database">web</param>
            <!-- Whether or not a full index rebuild should be triggered when the number of items in the EventQueue exceeds the number specified 
                 in ContentSearch.FullRebuildItemCountThreshold. -->
            <CheckForThreshold>true</CheckForThreshold>
          </onPublishEndAsync>

          <!-- REINDEX ON PUBLISH END CD
               This strategy is triggered on publish:end and uses the EventQueue to incrementally rebuild the index.  
          -->
          <onPublishEndAsyncCD type="Sitecore.ContentSearch.Maintenance.Strategies.OnPublishEndAsynchronousStrategy, Sitecore.ContentSearch">
            <param desc="database">webcd</param>
            <!-- Whether or not a full index rebuild should be triggered when the number of items in the EventQueue exceeds the number specified 
                 in ContentSearch.FullRebuildItemCountThreshold. -->
            <CheckForThreshold>true</CheckForThreshold>
          </onPublishEndAsyncCD>

          <!-- REINDEX ON FULL PUBLISH
               Every index that uses this strategy is fully rebuilt after a full publish. 
          -->
          <rebuildAfterFullPublish type="Sitecore.ContentSearch.Maintenance.Strategies.RebuildAfterFullPublishStrategy, Sitecore.ContentSearch" />

          <!-- REMOTE INDEX REBUILD TRIGGER
               This strategy allows remote indexes to react to a full index rebuild operation that is run from another instance. This strategy requires
               that the EventQueue be enabled.
          -->
          <remoteRebuild type="Sitecore.ContentSearch.Maintenance.Strategies.RemoteRebuildStrategy, Sitecore.ContentSearch" />

          <!-- SYNCHRONOUS INDEX REBUILDS
               This strategy subscribes to data engine events directly and is CPU and IO intensive. Not recommended for Content Delivery servers.
          -->
          <syncMaster type="Sitecore.ContentSearch.Maintenance.Strategies.SynchronousStrategy, Sitecore.ContentSearch">
            <param desc="database">master</param>
          </syncMaster>

        </indexUpdateStrategies>

        <databasePropertyStore type="Sitecore.ContentSearch.Maintenance.IndexDatabasePropertyStore, Sitecore.ContentSearch">
          <Key>$(1)</Key>
          <Database>core</Database>
        </databasePropertyStore>

        <!-- DEFAULT SEARCH SECURITY OPTION
             This setting is the default search security option that will be used if the search security option is not specified during the creation
             of search context. The accepted values are DisableSecurityCheck and EnableSecurityCheck.
        -->
        <defaultSearchSecurityOption>DisableSecurityCheck</defaultSearchSecurityOption>
      </indexConfigurations>
    </contentSearch>
  </sitecore>
</configuration>