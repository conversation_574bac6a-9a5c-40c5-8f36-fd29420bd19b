﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the Engagement Automation subsystem to process timeout conditions for engagement automation states.

If you want to disable this functionality and prevent this server from processing timeout conditions for engagement automation states,
you can rename this config file so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <automationWorker type="Sitecore.Analytics.Automation.AutomationWorker, Sitecore.Analytics.Automation" singleInstance="true">
      <ThreadsCount>1</ThreadsCount>
      <Interval>00:00:10</Interval>
      <ContactRepository ref="contactRepository"/>
    </automationWorker>

    <pipelines>
      <initialize>
        <processor type="Sitecore.Analytics.Pipelines.Loader.InitializeAutomation,Sitecore.Analytics.Automation" />
      </initialize>
    </pipelines>
  </sitecore>
</configuration>
