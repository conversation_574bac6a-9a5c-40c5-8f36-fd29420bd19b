﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures a work of Sitecore Experience Editor. The SPEAK part of it - the declarations of server requests for Sitecore Experience Editor - is contained and described in the Sitecore.ExperienceEditor.Speak.Requests.config file.  

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <overrideXmlControls>
      <override xmlControl="Applications.WebEdit.Dialogs.AddMaster" with="/sitecore/client/Applications/ExperienceEditor/Dialogs/InsertPage" />
    </overrideXmlControls>
    <pipelines>
      <injectExperienceEditorRibbonComponents>
      </injectExperienceEditorRibbonComponents>
      <getExperienceEditorRibbon>
        <processor type="Sitecore.ExperienceEditor.Speak.Ribbon.Pipelines.GetExperienceEditorRibbon.AddWebEditRibbon, Sitecore.ExperienceEditor.Speak.Ribbon" />
        <processor type="Sitecore.ExperienceEditor.Speak.Ribbon.Pipelines.GetExperienceEditorRibbon.AddWebEditContentEditor, Sitecore.ExperienceEditor.Speak.Ribbon" />
      </getExperienceEditorRibbon>
      <httpRequestBegin>
        <processor type="Sitecore.Pipelines.HttpRequest.PageEditorHandleNoLayout, Sitecore.ExperienceEditor" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.LayoutResolver, Sitecore.Kernel']" />
        <processor type="Sitecore.ExperienceEditor.Pipelines.HttpRequest.CheckDevice, Sitecore.ExperienceEditor" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.LayoutResolver, Sitecore.Kernel']" />
      </httpRequestBegin>
      <!--  Controls actions performed on presentation components in Page Editor -->
      <executePageEditorAction>
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.InsertRendering, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.ReplaceRendering, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.PreviewRendering, Sitecore.ExperienceEditor" />
        <!-- Analytics pipeline -->
        <processor type="Sitecore.Analytics.Pipelines.ExecutePageEditorAction.ActivateCondition, Sitecore.ExperienceEditor" />
        <!-- Analytics pipeline -->
        <processor type="Sitecore.Analytics.Pipelines.ExecutePageEditorAction.ActivateTestVariation, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.StopIfSublayout, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.TryGetXslContol, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.TryGetWebControl, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.InitActionHandler, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.ExecutePageEditorAction.GetDefaultControl, Sitecore.ExperienceEditor" />
      </executePageEditorAction>
      <getPageEditorNotifications>
        <processor type="Sitecore.Pipelines.GetPageEditorNotifications.GetWorkflowNotification, Sitecore.ExperienceEditor" />
      </getPageEditorNotifications>
      <!-- Gets the validators, that will be executed when saving page in Page Editor-->
      <getPageEditorValidators>
        <processor type="Sitecore.Pipelines.GetPageEditorValidators.CheckSettings, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.GetPageEditorValidators.GetItemValidators, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.GetPageEditorValidators.GetGlobalValidators, Sitecore.ExperienceEditor" />
        <processor type="Sitecore.Pipelines.GetPageEditorValidators.GetFieldValidators, Sitecore.ExperienceEditor" />
      </getPageEditorValidators>
      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Sitecore/ExperienceEditor/" deep="true" category="ExperienceEditor" pattern="*.js,*.css" />
            <source folder="/sitecore/shell/client/Sitecore/Speak/Ribbon" deep="true" category="ribbon" pattern="*.js,*.css" />
          </sources>
        </processor>
      </speak.client.resolveScript>
    </pipelines>
    <pageextenders>
      <!-- Uncomment the page extenders below and comment the "Sitecore.ExperienceEditor.Speak.Ribbon.PageExtender.RibbonPageExtender" to switch to old SheerUI-based Experience Editor ribbon. -->
      <!--<pageextender type="Sitecore.Layouts.PageExtenders.PreviewPageExtender, Sitecore.ExperienceEditor" />
      <pageextender type="Sitecore.Layouts.PageExtenders.WebEditPageExtender, Sitecore.ExperienceEditor" />
      <pageextender type="Sitecore.Layouts.PageExtenders.DebuggerPageExtender, Sitecore.ExperienceEditor" />-->
      
      <pageextender type="Sitecore.Shell.Applications.Preview.SimulatedDevicePreview.PageExtenders.PreviewExtender, Sitecore.ExperienceEditor" />
      
      <!-- Page extender for SPEAK-based Experience Editor ribbon.  -->
      <pageextender type="Sitecore.ExperienceEditor.Speak.Ribbon.PageExtender.RibbonPageExtender, Sitecore.ExperienceEditor.Speak.Ribbon" />
    </pageextenders>
    <xslExtensions>
      <extension mode="on" type="Sitecore.Web.UI.WebControls.WebEditRibbon, Sitecore.ExperienceEditor" namespace="http://www.sitecore.net/webedit" singleInstance="true" />
    </xslExtensions>
	<settings>
      <!--  WEB EDIT EXPERIENCE EDITOR STYLESHEET
            The stylesheet to include in the experience editor.
            Default value: /shell/client/Sitecore/ExperienceEditor/Ribbon.css
      -->
      <setting name="WebEdit.ExperienceEditorStylesheet" value="/sitecore/shell/client/Sitecore/ExperienceEditor/Ribbon.css" />
      <!--  WEB EDIT CONTENT EDITOR STYLESHEET
            The stylesheet to include in the content editor in WebEdit mode.
            Default value: /webedit.css
      -->
      <setting name="WebEdit.ContentEditorStylesheet" value="/webedit.css" />
	  <!--  WEBEDIT DEFAULT BUTTON PATH
            Specifies default location of edit frame buttons in the core database.
            Default value: /sitecore/content/Applications/WebEdit/Edit Frame Buttons/Default
      -->
	  <setting name="WebEdit.DefaultButtonPath" value="/sitecore/content/Applications/WebEdit/Edit Frame Buttons/Default" />
	  <!--  WEBEDIT DISABLE ANIMATIONS
            Disables animation effects in the Page Editor.
            Default value: false
      -->
	  <setting name="WebEdit.DisableAnimations" value="false" />
	  <!--  WEB EDIT ENABLE VALIDATION
            If true, the Page Editor will execute item and field validation rules whenever a user tries to save items in the Page Editor.
            Only 'Critical' and 'Fatal' validators are evaluated, and item validation rules are executed for the current context item only.
            Field validation rules are only executed for fields that the current user can modify in the Page Editor.
            Default value: true
      -->
	  <setting name="WebEdit.EnableValidation" value="true" />
	  <!--  WEB EDIT ENABLE JS BUNDLING
            Indicates whether web edit specific JavaScript files should be bundled into one file.
            Default value: true
      -->
	  <setting name="WebEdit.EnableJSBundling" value="true" />
	  <!--  WEB EDIT BUNDLED JS FILES PATH
            Specifies the path where bundled JavaScript files are stored if WebEdit.EnableJSBundling = true
            Default value: /sitecore/shell/Applications/Page Modes/Ouput/
      -->
	  <setting name="WebEdit.BundledJSFilesPath" value="/sitecore/shell/Applications/Page Modes/Ouput/" />
	  <!--  WEB EDIT PLACEHOLDERS EDITABLE WITHOUT SETTINGS
            Indicates whether placeholders should be editable in the Page Editor if placeholder settings are not specified.
            Default value: false
      -->
	  <setting name="WebEdit.PlaceholdersEditableWithoutSettings" value="false" />
	  <!--  WEBEDIT PAGE DAILY VISITS CACHE EXPIRATION
            Sets the absolute expiration on the cached daily visits data in Page Editor.
            Makes sense only when analytics is enabled.            
            Default value: 1.00:00:00 (1 day)
      -->
	  <setting name="WebEdit.PageDailyVisitsCacheExpiration" value="1.00:00:00" />
	  <!--  WEBEDIT TEST STATISTICS CACHE EXPIRATION
            Sets the absolute expiration on the cached test statistics data in Page Editor.
            Makes sense only when analytics is enabled.            
            Default value: 01:00:00 (1 hour)
      -->
	  <setting name="WebEdit.TestStatisticsCacheExpiration" value="01:00:00" />
	  <!--  WEB EDIT USE POPUP EDITOR
            Indicates whether WebEdit uses the popup content editor.
            Default value: false
      -->
	  <setting name="WebEdit.UsePopupContentEditor" value="false" />
	</settings>
    
	<!-- EXPERIENCE EDITOR COMMANDS
         The commands that run in the Experience Editor.
    -->
	<commands>
      <command name="item:personalize" type="Sitecore.Shell.Applications.WebEdit.Commands.PersonalizeItem, Sitecore.ExperienceEditor" />
      <command name="webedit:add" type="Sitecore.Shell.Applications.WebEdit.Commands.Add, Sitecore.ExperienceEditor" />
      <command name="webedit:addrendering" type="Sitecore.Shell.Applications.WebEdit.Commands.AddRendering, Sitecore.ExperienceEditor" />
      <command name="webedit:aspnettrace" type="Sitecore.Shell.Applications.WebEdit.Commands.AspNetTrace, Sitecore.ExperienceEditor" />
      <command name="webedit:beginedit" type="Sitecore.Shell.Applications.WebEdit.Commands.BeginEdit, Sitecore.ExperienceEditor" />
      <command name="webedit:changedevice" type="Sitecore.Shell.Applications.WebEdit.Commands.ChangeDevice, Sitecore.ExperienceEditor" />
      <command name="webedit:changelanguage" type="Sitecore.Shell.Applications.WebEdit.Commands.ChangeLanguage, Sitecore.ExperienceEditor" />
      <command name="webedit:changelayout" type="Sitecore.Shell.Applications.WebEdit.Commands.ChangeLayout, Sitecore.ExperienceEditor" />
      <command name="webedit:chooseimage" type="Sitecore.Shell.Applications.WebEdit.Commands.ChooseImage, Sitecore.ExperienceEditor" />
      <command name="webedit:clearimage" type="Sitecore.Shell.Applications.WebEdit.Commands.ClearImage, Sitecore.ExperienceEditor" />
      <command name="webedit:close" type="Sitecore.Shell.Applications.WebEdit.Commands.Close, Sitecore.ExperienceEditor" />
      <command name="webedit:closepagedesigner" type="Sitecore.Shell.Applications.WebEdit.Commands.ClosePageDesigner, Sitecore.ExperienceEditor" />
	  <command name="webedit:componentoptions" type="Sitecore.Shell.Applications.WebEdit.Commands.ComponentOptions, Sitecore.ExperienceEditor" />
      <command name="webedit:debug" type="Sitecore.Shell.Applications.WebEdit.Commands.Debug, Sitecore.ExperienceEditor" />
      <command name="webedit:delete" type="Sitecore.Shell.Applications.WebEdit.Commands.Delete, Sitecore.ExperienceEditor" />
      <command name="webedit:downloadprofile" type="Sitecore.Shell.Applications.WebEdit.Commands.DownloadProfile, Sitecore.ExperienceEditor" />
      <command name="webedit:downloadtrace" type="Sitecore.Shell.Applications.WebEdit.Commands.DownloadTrace, Sitecore.ExperienceEditor" />
      <command name="webedit:edit" type="Sitecore.Shell.Applications.WebEdit.Commands.Edit, Sitecore.ExperienceEditor" />
      <command name="webedit:edithtml" type="Sitecore.Shell.Applications.WebEdit.Commands.EditHtml, Sitecore.ExperienceEditor" />
      <command name="webedit:editdate" type="Sitecore.Shell.Applications.WebEdit.Commands.EditDate, Sitecore.ExperienceEditor" />
      <command name="webedit:editimage" type="Sitecore.Shell.Applications.WebEdit.Commands.EditImage, Sitecore.ExperienceEditor" />
      <command name="webedit:editlink" type="Sitecore.Shell.Applications.WebEdit.Commands.EditLink, Sitecore.ExperienceEditor" />
      <command name="webedit:clearlink" type="Sitecore.Shell.Applications.WebEdit.Commands.ClearLink, Sitecore.ExperienceEditor"/>  
      <command name="webedit:editplaceholderproperties" type="Sitecore.Shell.Applications.WebEdit.Commands.EditPlaceholderProperties, Sitecore.ExperienceEditor" />
      <command name="webedit:editplaceholdersettings" type="Sitecore.Shell.Applications.WebEdit.Commands.EditPlaceholderSettings, Sitecore.ExperienceEditor" />
      <command name="webedit:editrenderingproperties" type="Sitecore.Shell.Applications.WebEdit.Commands.EditRenderingProperties, Sitecore.ExperienceEditor" />
      <command name="webedit:editvariations" type="Sitecore.Shell.Applications.WebEdit.Commands.EditVariations, Sitecore.ExperienceEditor" />
      <command name="webedit:editword" type="Sitecore.Shell.Applications.WebEdit.Commands.EditWord, Sitecore.ExperienceEditor" />
      <command name="webedit:end" type="Sitecore.Shell.Applications.WebEdit.Commands.End, Sitecore.ExperienceEditor" />
      <command name="webedit:fieldeditor" type="Sitecore.Shell.Applications.WebEdit.Commands.FieldEditor, Sitecore.ExperienceEditor" />
      <command name="webedit:insertlink" type="Sitecore.Shell.Applications.WebEdit.Commands.InsertLink, Sitecore.ExperienceEditor" />
      <command name="webedit:insertimage" type="Sitecore.Shell.Applications.WebEdit.Commands.InsertImage, Sitecore.ExperienceEditor" />
      <command name="webedit:layoutsaved" type="Sitecore.Shell.Applications.WebEdit.Commands.LayoutSaved, Sitecore.ExperienceEditor" />
      <command name="webedit:lock" type="Sitecore.Shell.Applications.WebEdit.Commands.Lock, Sitecore.ExperienceEditor" />
      <command name="webedit:logout" type="Sitecore.Shell.Applications.WebEdit.Commands.Logout, Sitecore.ExperienceEditor" />
      <command name="webedit:movepage" type="Sitecore.Shell.Applications.WebEdit.Commands.MovePage, Sitecore.ExperienceEditor" />
      <command name="webedit:new" type="Sitecore.Shell.Applications.WebEdit.Commands.New, Sitecore.ExperienceEditor" />
      <command name="webedit:newrendering" type="Sitecore.Shell.Applications.WebEdit.Commands.NewRendering, Sitecore.ExperienceEditor" />
      <command name="webedit:nextday" type="Sitecore.Shell.Applications.WebEdit.Commands.NextDay, Sitecore.ExperienceEditor" />
      <command name="webedit:open" type="Sitecore.Shell.Applications.WebEdit.Commands.Open, Sitecore.ExperienceEditor" />
      <command name="webedit:openmyitems" type="Sitecore.Shell.Applications.WebEdit.Commands.OpenMyItems, Sitecore.ExperienceEditor" />
      <command name="webedit:openpagedesigner" type="Sitecore.Shell.Applications.WebEdit.Commands.OpenPageDesigner, Sitecore.ExperienceEditor" />
      <command name="webedit:openworkbox" type="Sitecore.Shell.Applications.WebEdit.Commands.OpenWorkbox, Sitecore.ExperienceEditor" />
      <command name="webedit:otherviews" type="Sitecore.Shell.Applications.WebEdit.Commands.OtherViews, Sitecore.ExperienceEditor" />
      <command name="webedit:personalize" type="Sitecore.Shell.Applications.WebEdit.Commands.Personalize, Sitecore.ExperienceEditor" />
      <command name="webedit:preview" type="Sitecore.Shell.Applications.WebEdit.Commands.Preview, Sitecore.ExperienceEditor" />
      <command name="webedit:previousday" type="Sitecore.Shell.Applications.WebEdit.Commands.PreviousDay, Sitecore.ExperienceEditor" />
      <command name="webedit:rename" type="Sitecore.Shell.Applications.WebEdit.Commands.Rename, Sitecore.ExperienceEditor" />
      <command name="webedit:sortcontent" type="Sitecore.Shell.Applications.WebEdit.Commands.SortContent, Sitecore.ExperienceEditor" />
      <command name="webedit:save" type="Sitecore.Shell.Applications.WebEdit.Commands.Save, Sitecore.ExperienceEditor" />
      <command name="webedit:saved" type="Sitecore.Shell.Applications.WebEdit.Commands.Saved, Sitecore.ExperienceEditor" />
      <command name="webedit:saveprofile" type="Sitecore.Shell.Applications.WebEdit.Commands.SaveProfile, Sitecore.ExperienceEditor" />
      <command name="webedit:savetrace" type="Sitecore.Shell.Applications.WebEdit.Commands.SaveTrace, Sitecore.ExperienceEditor" />
      <command name="webedit:search" type="Sitecore.Shell.Applications.WebEdit.Commands.Search, Sitecore.ExperienceEditor" />
      <command name="webedit:selectdate" type="Sitecore.Shell.Applications.WebEdit.Commands.SelectDate, Sitecore.ExperienceEditor" />
      <command name="webedit:selectdevicesimulator" type="Sitecore.Shell.Applications.WebEdit.Commands.SelectDeviceSimulator, Sitecore.ExperienceEditor" />
      <command name="webedit:selectlayoutpreset" type="Sitecore.Shell.Applications.WebEdit.Commands.SelectLayoutPreset, Sitecore.ExperienceEditor" />
      <command name="webedit:setdatasource" type="Sitecore.Shell.Applications.WebEdit.Commands.SetDatasource, Sitecore.ExperienceEditor" />
      <command name="webedit:setdevice" type="Sitecore.Shell.Applications.WebEdit.Commands.SetDevice, Sitecore.ExperienceEditor" />
      <command name="webedit:setpagevariation" type="Sitecore.Shell.Applications.WebEdit.Commands.Testing.SetPageVariation, Sitecore.ExperienceEditor" />
      <command name="webedit:setlanguage" type="Sitecore.Shell.Applications.WebEdit.Commands.SetLanguage, Sitecore.ExperienceEditor" />
      <command name="webedit:simulatedevice" type="Sitecore.Shell.Applications.WebEdit.Commands.SimulateDevice, Sitecore.ExperienceEditor" />      
      <command name="webedit:stopdebug" type="Sitecore.Shell.Applications.WebEdit.Commands.StopDebug, Sitecore.ExperienceEditor" />      
      <command name="webedit:toggleborders" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleBorders, Sitecore.ExperienceEditor" />
      <command name="webedit:toggledesigncapability" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleDesignCapability, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleeditcapability" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleEditCapability, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleedit" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleEdit, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleinformation" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleInformation, Sitecore.ExperienceEditor" />
      <command name="webedit:togglecontrolbar" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleControlBar, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleprofile" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleProfile, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleselect" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleSelect, Sitecore.ExperienceEditor" />
      <command name="webedit:toggletrace" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleTrace, Sitecore.ExperienceEditor" />
      <command name="webedit:toggletreecrumb" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleTreecrumb, Sitecore.ExperienceEditor" />
      <command name="webedit:toggleshowcontrols" type="Sitecore.Shell.Applications.WebEdit.Commands.ToggleShowControls, Sitecore.ExperienceEditor" />
      <command name="webedit:unlock" type="Sitecore.Shell.Applications.WebEdit.Commands.Unlock, Sitecore.ExperienceEditor" />
      <command name="webedit:unlockall" type="Sitecore.Shell.Applications.WebEdit.Commands.UnlockAll, Sitecore.ExperienceEditor" />
      <command name="webedit:webedit" type="Sitecore.Shell.Applications.WebEdit.Commands.WebEdit, Sitecore.ExperienceEditor" />
      <command name="webedit:wordinsertlink" type="Sitecore.Shell.Applications.WebEdit.Commands.EditWordInsertLink, Sitecore.ExperienceEditor" />
      <command name="webedit:wordinsertmedia" type="Sitecore.Shell.Applications.WebEdit.Commands.EditWordInsertMedia, Sitecore.ExperienceEditor" />
    </commands>
  </sitecore>
</configuration>