﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the collection of diagnostics information that can help you analyze the behavior of Sitecore.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <initialize>
        <!-- DUMP CONFIGURATION FILES
        Dumps the specified configuration files and allows you to monitor all the changes that are made to the configuration files.
          Supported child nodes:
            DumpFolder: The path to the root folder where the config file dump is stored. Each config file dump is stored in a folder or
                        a zip archive. The name of the folder or zip archive has the following format: {date}.{time}.
                        Default value: $(dataFolder)/diagnostics/configuration_history
            Zip:        Boolean value that determines whether each dump should be zipped. Default value: true
            files:      Contains <file> nodes where the "path" attribute value is the path to the configuration file or the folder that
                        should be dumped.
        -->
        <processor type="Sitecore.Pipelines.Loader.DumpConfigurationFiles, Sitecore.Kernel">
          <dumpFolder>$(dataFolder)/diagnostics/configuration_history</dumpFolder>
          <zip>true</zip>
          <files hint="raw:AddPath">
            <file path="/App_Config" />
            <file path="/Web.config" />
            <file path="/Global.asax" />
            <file path="/sitecore/shell/sitecore.version.xml" />
          </files>
        </processor>
      </initialize>
      <healthMonitor>
        <!-- Dumps the information that the /sitecore/admin/cache.aspx page contains -->
        <processor type="Sitecore.Pipelines.HealthMonitor.HealthMonitor, Sitecore.Kernel" method="DumpAllCacheStatus">
          <dumpFile>$(dataFolder)/diagnostics/health_monitor/CacheStatus.{date}.{time}.html</dumpFile>
        </processor>
        <!-- Dumps the information that the /sitecore/admin/stats.aspx page contains -->
        <processor type="Sitecore.Pipelines.HealthMonitor.HealthMonitor, Sitecore.Kernel" method="DumpRenderingsStatistics">
          <dumpFile>$(dataFolder)/diagnostics/health_monitor/RenderingsStatistics.{date}.{time}.html</dumpFile>
        </processor>
      </healthMonitor>
    </pipelines>
    <scheduling>
      <agent type="Sitecore.Tasks.CleanupAgent">
        <files>
          <remove folder="$(dataFolder)/diagnostics/configuration_history" pattern="*" maxAge="30.00:00:00" recursive="false" />
          <remove folder="$(dataFolder)/diagnostics/health_monitor" pattern="*.*" maxAge="07.00:00:00" recursive="false" />
        </files>
      </agent>
    </scheduling>
  </sitecore>
</configuration>