﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <itemWebApiRequest>
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.Search, Sitecore.Speak.ItemWebApi" patch:after="processor[@type='Sitecore.ItemWebApi.Pipelines.Request.ResolveItems, Sitecore.ItemWebApi']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.Facets, Sitecore.Speak.ItemWebApi" patch:before="processor[@type='Sitecore.ItemWebApi.Pipelines.Request.SerializeResponse, Sitecore.ItemWebApi']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.ConvertValues, Sitecore.Speak.ItemWebApi" patch:before="processor[@type='Sitecore.ItemWebApi.Pipelines.Request.SerializeResponse, Sitecore.ItemWebApi']" />
        
        <processor type="Sitecore.ItemWebApi.Pipelines.Request.CheckParameters, Sitecore.ItemWebApi">
          <parameters hint="list">
            <name desc="speak-parameter1">facetsRootItemId</name>
            <name desc="speak-parameter2">search</name>
            <name desc="speak-parameter3">root</name>
            <name desc="speak-parameter4">searchConfig</name>
            <name desc="speak-parameter5">pageIndex</name>
            <name desc="speak-parameter6">sc_content</name>
            <name desc="speak-parameter7">format</name>
            <name desc="speak-parameter8">sorting</name>
            <name desc="speak-parameter9">showHiddenItems</name>
          </parameters>
        </processor>
        
      </itemWebApiRequest>
      <itemWebApiGetProperties>
        <processor type="Sitecore.ItemWebApi.Pipelines.GetProperties.GetUiProperties, Sitecore.Speak.ItemWebApi" patch:after="processor[@type='Sitecore.ItemWebApi.Pipelines.GetProperties.GetProperties, Sitecore.ItemWebApi']" />
        <processor type="Sitecore.ItemWebApi.Pipelines.GetProperties.GetSystemProperties, Sitecore.Speak.ItemWebApi" patch:after="processor[@type='Sitecore.ItemWebApi.Pipelines.GetProperties.GetProperties, Sitecore.ItemWebApi']" />
      </itemWebApiGetProperties>
      <itemWebApiSearch>
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.ParseSearchText, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.SetRootItem, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.SetLanguage, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.SetSearchParameters, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.FilterItems, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.Facets, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.Sorting, Sitecore.Speak.ItemWebApi" />
        <processor type="Sitecore.ItemWebApi.Pipelines.Search.Paging, Sitecore.Speak.ItemWebApi" />
      </itemWebApiSearch>

      <itemWebApiUpdate>
        <processor type="Sitecore.ItemWebApi.Pipelines.Update.Rename, Sitecore.Speak.ItemWebApi" />
      </itemWebApiUpdate>
    </pipelines>
    <sites>
      <site name="shell">
        <patch:attribute name="itemwebapi.mode">StandardSecurity</patch:attribute>
        <patch:attribute name="itemwebapi.access">ReadWrite</patch:attribute>
        <patch:attribute name="itemwebapi.allowanonymousaccess">false</patch:attribute>
      </site>
    </sites>
  </sitecore>
</configuration>