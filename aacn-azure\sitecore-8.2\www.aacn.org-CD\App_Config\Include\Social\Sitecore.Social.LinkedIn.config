﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the plugin to the Social Connected module that enables integration with LinkedIn (http://linkedin.com/).

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>

      <!-- SOCIAL - LINKEDIN - DEFAULT LINKEDIN SHARE GOAL
           The Sitecore Social Connected goal that is triggered when a visitor clicks the "Share" button.
           Default value: LinkedIn Share
      -->
      <setting name="Social.LinkedIn.DefaultLinkedInShareGoal" value="LinkedIn Share"/>

    </settings>

    <social>

      <!-- Personalization -->
      <!-- This section configures the Interests field in the user profile. -->
      <interestedInRule>
        <network name="LinkedIn">
          <field sitecoreKey="ln_interests" />
        </network>
      </interestedInRule>
      
      <networks>
        <network name="LinkedIn" ItemId="{DD7F52D8-B9DE-437B-9F3C-0542FDF13514}" prefix="ln" icon="linkedin3" url="http://www.linkedin.com/">
          <permissionBuilders>
            <permissionBuilder type="Sitecore.Social.LinkedIn.Connector.PermissionBuilders.LinkedInPermissionBuilder, Sitecore.Social.LinkedIn" />
          </permissionBuilders>
          <providers>
            <provider type="Sitecore.Social.LinkedIn.Networks.Providers.LinkedInProvider, Sitecore.Social.LinkedIn"/>
          </providers>
        </network>
      </networks>
      
    </social>
    
  </sitecore>
</configuration>
