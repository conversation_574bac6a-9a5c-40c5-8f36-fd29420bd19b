<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <commands>
      <command name="item:executescript"
               type="Cognifide.PowerShell.Client.Commands.MenuItems.ExecutePowerShellScript,Cognifide.PowerShell" />
      <command name="item:contextconsole"
               type="Cognifide.PowerShell.Client.Commands.MenuItems.ExecutePowerShellConsole,Cognifide.PowerShell" />
      <command name="item:scriptlibrary"
               type="Cognifide.PowerShell.Client.Commands.MenuItems.ScriptLibraryMenuItem,Cognifide.PowerShell" />
      <command name="powershell:startmenuaction"
               type="Cognifide.PowerShell.Client.Commands.MenuItems.ScriptLibraryMenuItem,Cognifide.PowerShell" />
      <command name="item:editscript"
               type="Cognifide.PowerShell.Client.Commands.MenuItems.EditPowerShellScript,Cognifide.PowerShell" />
      <command name="ise:save" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:saveas" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:execute" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:executeselection" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:open" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:new" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:run" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:abort" type="Cognifide.PowerShell.Client.Commands.AbortQueryState,Cognifide.PowerShell" />
      <command name="ise:reload"
               type="Cognifide.PowerShell.Client.Commands.ItemDefinedQueryState,Cognifide.PowerShell" />
      <command name="ise:editsettingsdropdown"
               type="Cognifide.PowerShell.Client.Commands.EditIseSettingsCombo,Cognifide.PowerShell" />
      <command name="ise:picksessionid"
               type="Cognifide.PowerShell.Client.Commands.GalleryRuntimeQueryState,Cognifide.PowerShell" />
      <command name="item:new">
        <patch:attribute name="type">Cognifide.PowerShell.Client.Commands.MenuItems.ItemNew, Cognifide.PowerShell</patch:attribute>
      </command>
      <command name="ise:executefieldeditor"
               type="Cognifide.PowerShell.VersionSpecific.Client.Commands.ExecuteFieldEditor, Cognifide.PowerShell.VersionSpecific" />
      <command name="ise:editsettings"
               type="Cognifide.PowerShell.VersionSpecific.Client.Commands.EditIseSettings,Cognifide.PowerShell.VersionSpecific" />
      <command name="powershell:fieldeditor"
               type="Cognifide.PowerShell.VersionSpecific.Client.Applications.FieldEditor, Cognifide.PowerShell.VersionSpecific" />
      <command name="ise:debug" type="Cognifide.PowerShell.Client.Commands.RuntimeQueryState,Cognifide.PowerShell" />
      <command name="ise:debugaction" type="Cognifide.PowerShell.Client.Commands.DebugQueryState,Cognifide.PowerShell" />
      <command name="ise:immediatewindow" type="Cognifide.PowerShell.Client.Commands.DebugQueryState,Cognifide.PowerShell" />
      <command name="ise:iframe" type="Cognifide.PowerShell.Client.Commands.MenuItems.ShowExternalPage" />
      <command name="pslvnav:first" type="Cognifide.PowerShell.Client.Commands.ListViewPagingQueryState,Cognifide.PowerShell" />
      <command name="pslvnav:last" type="Cognifide.PowerShell.Client.Commands.ListViewPagingQueryState,Cognifide.PowerShell" />
      <command name="pslvnav:previous" type="Cognifide.PowerShell.Client.Commands.ListViewPagingQueryState,Cognifide.PowerShell" />
      <command name="pslvnav:next" type="Cognifide.PowerShell.Client.Commands.ListViewPagingQueryState,Cognifide.PowerShell" />
      <command name="pslv:filter" type="Cognifide.PowerShell.Client.Commands.ListViewFilterQueryState,Cognifide.PowerShell" />               
      <command name="webedit:script" type="Cognifide.PowerShell.VersionSpecific.Client.Commands.WebEditScriptCommand,Cognifide.PowerShell.VersionSpecific" />               
    </commands>
    <typeMappings>
      <mapping name="IDateConverter" type="Cognifide.PowerShell.VersionSpecific.Utility.DateConverter, Cognifide.PowerShell.VersionSpecific" />
      <mapping name="IImmediateDebugWindowLauncher" type="Cognifide.PowerShell.VersionSpecific.Client.Applications.ImmediateDebugWindowLauncher, Cognifide.PowerShell.VersionSpecific" />
    </typeMappings>
    <settings>
      <setting name="Cognifide.PowerShell.CommandWaitMillis" value="25" />
      <setting name="Cognifide.PowerShell.InitialPollMillis" value="100" />
      <setting name="Cognifide.PowerShell.MaxmimumPollMillis" value="2500" />
      <setting name="Cognifide.PowerShell.PersistentSessionExpirationMinutes" value="30" />
      <setting name="Cognifide.PowerShell.SerializationSizeBuffer" value="5KB" />
      <setting name="Cognifide.PowerShell.VariableDetails.MaxArrayParseSize" value="20" />
    </settings>
    <ui>
      <references>
        <reference id="powershell">/bin/Cognifide.PowerShell.dll</reference>
      </references>
    </ui>
    <controlSources>
      <source mode="on" namespace="Cognifide.PowerShell.Client.Controls" assembly="Cognifide.PowerShell" />
      <source mode="on" namespace="Cognifide.PowerShell.Client.Applications"
              folder="/sitecore modules/Shell/PowerShell/" deep="true" />
    </controlSources>
    <powershell>
      <workingDatabase>
        <scriptLibrary>master</scriptLibrary>
        <settings>master</settings>
        <rules>master</rules>
      </workingDatabase>
      <integrationPoints>
        <pageEditorExperienceButton name="Page Editor Experience Button" creationScript="Platform/Development/Integration points/Page Editor Experience Buttons/Create Libraries">
          Page Editor/Experience Button
        </pageEditorExperienceButton>
        <contentEditorContextMenu name="Content Editor Context Menu"
                                  creationScript="Platform/Development/Integration points/Content Editor Context Menu/Create Libraries">
          Content Editor/Context Menu
        </contentEditorContextMenu>
        <contentEditorInsertItem name="Content Editor Insert Item"
                                  creationScript="Platform/Development/Integration points/Content Editor Insert Item/Create Libraries">
          Content Editor/Insert Item
        </contentEditorInsertItem>
        <contentEditorGutters name="Content Editor Gutter"
                              creationScript="Platform/Development/Integration points/Content Editor Gutter/Create Libraries">
          Content Editor/Gutters
        </contentEditorGutters>
        <contentEditorRibbon name="Content Editor Ribbon"
                             creationScript="Platform/Development/Integration points/Content Editor Ribbon/Create Libraries">
          Content Editor/Ribbon
        </contentEditorRibbon>
        <contentEditorWarning name="Content Editor Warning"
                             creationScript="Platform/Development/Integration points/Content Editor Warning/Create Libraries">
          Content Editor/Warning
        </contentEditorWarning>        
        <controlPanel name="Control Panel"
                      creationScript="Platform/Development/Integration points/Control Panel/Create Libraries">
          Control Panel
        </controlPanel>
        <functions name="Shared Functions"
                   creationScript="Platform/Development/Integration points/Functions/Create Libraries">
          Functions
        </functions>
        <listViewExport name="List View Exporters"
                        creationScript="Platform/Development/Integration points/List View Exporters/Create Libraries">
          Internal/List View/Export
        </listViewExport>
        <listViewRibbon name="List View Ribbon Actions"
                        creationScript="Platform/Development/Integration points/List View Ribbon Actions/Create Libraries">
          Internal/List View/Ribbon
        </listViewRibbon>
        <pageEditorNotification name="Page Editor Notifications"
                             creationScript="Platform/Development/Integration points/Page Editor Notifications/Create Libraries">
          Page Editor/Notification
        </pageEditorNotification>        
        <pipelineLoggedIn name="Logged In Pipeline"
                          creationScript="Platform/Development/Integration points/Logged In Pipeline/Create Libraries">
          Pipelines/LoggedIn
        </pipelineLoggedIn>
        <pipelineLoggingIn name="Logging in Pipeline"
                           creationScript="Platform/Development/Integration points/Logging in Pipeline/Create Libraries">
          Pipelines/LoggingIn
        </pipelineLoggingIn>
        <pipelineLogout name="Logout Pipeline"
                        creationScript="Platform/Development/Integration points/Logout Pipeline/Create Libraries">
          Pipelines/Logout
        </pipelineLogout>
        <toolbox name="Toolbox" creationScript="Platform/Development/Integration points/Toolbox/Create Libraries">Toolbox</toolbox>
        <startMenuReports name="Start Menu Reports"
                          creationScript="Platform/Development/Integration points/Start Menu Reports/Create Libraries">
          Reports
        </startMenuReports>
        <eventHandlers name="Event Handlers"
                       creationScript="Platform/Development/Integration points/Event Handlers/Create libraries for all events">
          Event Handlers
        </eventHandlers>
        <webAPI name="Web API" creationScript="Platform/Development/Integration points/Web API/Create libraries">Web API</webAPI>
        <isePlugin name="ISE Plugins" creationScript="Platform/Development/Integration points/ISE Plugin/Create libraries">Internal/ISE Plugins</isePlugin>
      </integrationPoints>
      <services>
        <restfulv1 enabled="false" />
        <restfulv2 enabled="true" />
        <remoting enabled="true" />
        <fileDownload enabled="false" />
        <fileUpload enabled="false" />
        <mediaDownload enabled="false" />
        <mediaUpload enabled="false" />
        <handleDownload enabled="true" />
        <client enabled="true" />
      </services>
      <commandlets>
        <add Name="Built-in Commandlets" type="*, Cognifide.PowerShell" />
      </commandlets>
      <translation>
        <ignoredFields>
          <field>__Archive date</field>
          <field>__Archive Version date</field>
          <field>__Lock</field>
          <field>__Owner</field>
          <field>__Page Level Test Set Definition</field>
          <field>__Reminder date</field>
          <field>__Reminder recipients</field>
          <field>__Reminder text</field>
          <!--field>__Security</field-->
        </ignoredFields>
      </translation>
    </powershell>
    <pipelines>
      <getChromeData>
        <processor type="Cognifide.PowerShell.Integrations.Pipelines.PageEditorExperienceButtonScript, Cognifide.PowerShell"/>
      </getChromeData>
      <getContentEditorWarnings>
        <processor patch:before="processor[@type='Sitecore.Pipelines.GetContentEditorWarnings.RunRules, Sitecore.Kernel']"
           type="Cognifide.PowerShell.Integrations.Pipelines.ContentEditorWarningScript, Cognifide.PowerShell" />
      </getContentEditorWarnings>
      <getPageEditorNotifications>
        <processor type="Cognifide.PowerShell.VersionSpecific.Integrations.Pipelines.PageEditorNotificationScript, Cognifide.PowerShell.VersionSpecific"/>
      </getPageEditorNotifications>
      <getLookupSourceItems>
        <processor
          patch:before="*[@type='Sitecore.Pipelines.GetLookupSourceItems.ProcessQuerySource, Sitecore.Kernel']"
          type="Cognifide.PowerShell.Integrations.Processors.ScriptedDataSource, Cognifide.PowerShell" />
      </getLookupSourceItems>
      <getRenderingDatasource>
        <processor
          patch:before="*[@type='Sitecore.Pipelines.GetRenderingDatasource.GetDatasourceLocation, Sitecore.Kernel']"
          type="Cognifide.PowerShell.Integrations.Processors.ScriptedRenderingDataSourceRoots, Cognifide.PowerShell" />
      </getRenderingDatasource>
      <resolveRenderingDatasource>
        <processor
          type="Cognifide.PowerShell.Integrations.Processors.ScriptedRenderingDataSourceResolve, Cognifide.PowerShell" />
      </resolveRenderingDatasource>
      <preprocessRequest>
        <processor type="Cognifide.PowerShell.Core.Processors.RewriteUrl, Cognifide.PowerShell"
                   patch:before="processor[@type='Sitecore.Pipelines.PreprocessRequest.CheckIgnoreFlag, Sitecore.Kernel']" />
      </preprocessRequest>
      <expandInitialFieldValue>
        <processor patch:before="*[@type='Sitecore.Pipelines.ExpandInitialFieldValue.ReplaceVariables, Sitecore.Kernel']" type="Cognifide.PowerShell.Integrations.Processors.SkipPowerShellScriptItems, Cognifide.PowerShell" />
      </expandInitialFieldValue>
    </pipelines>
    <processors>
      <loggingin argsType="Sitecore.Integrations.Pipelines.LoggingIn.LoggingInArgs">
        <!-- Pipeline to run scripts while the user is logging in. -->
        <processor patch:after="processor[position()=last()]" mode="on"
                   type="Cognifide.PowerShell.Integrations.Pipelines.LoggingInScript, Cognifide.PowerShell" />
      </loggingin>
      <loggedin argsType="Sitecore.Pipelines.LoggedIn.LoggedInArgs">
        <!-- Pipeline to run scripts after the user is logged in. -->
        <processor patch:after="processor[position()=last()]" mode="on"
                   type="Cognifide.PowerShell.Integrations.Pipelines.LoggedInScript, Cognifide.PowerShell" />
      </loggedin>
      <logout argsType="Sitecore.Pipelines.Logout.LogoutArgs">
        <!-- Pipeline to run scripts when the user logs out. -->
        <processor patch:after="*[@type='Sitecore.Pipelines.Logout.CheckModified, Sitecore.Kernel']" mode="on"
                   type="Cognifide.PowerShell.Integrations.Pipelines.LogoutScript, Cognifide.PowerShell" />
      </logout>
    </processors>
    <events>
      <event name="item:saved">
        <handler type="Cognifide.PowerShell.Core.Modules.ModuleMonitor, Cognifide.PowerShell" method="OnItemSaved" />
        <handler type="Cognifide.PowerShell.Core.Extensions.ItemShellExtensions, Cognifide.PowerShell" method="TemplateFieldsInvalidateCheck" />
      </event>
      <event name="item:saved:remote">
        <handler type="Cognifide.PowerShell.Core.Modules.ModuleMonitor, Cognifide.PowerShell" method="OnItemSavedRemote" />
        <handler type="Cognifide.PowerShell.Core.Extensions.ItemShellExtensions, Cognifide.PowerShell" method="TemplateFieldsInvalidateCheckRemote" />
      </event>
      <event name="item:saving">
        <handler type="Cognifide.PowerShell.Core.Modules.ModuleMonitor, Cognifide.PowerShell" method="OnItemSaving" />
      </event>
    </events>
    <log4net>
      <appender name="PowerShellExtensionsFileAppender" type="log4net.Appender.SitecoreLogFileAppender, Sitecore.Logging">
        <file value="$(dataFolder)/logs/SPE.log.{date}.txt"/>
        <appendToFile value="true"/>
        <layout type="log4net.Layout.PatternLayout">
          <conversionPattern value="%4t %d{ABSOLUTE} %-5p %m%n"/>
        </layout>
        <encoding value="utf-8"/>
      </appender>
      <logger name="Cognifide.PowerShell" additivity="false">
        <!-- Loggers may be assigned levels. Levels are instances of the log4net.Core.Level class. The following levels are defined in order of increasing priority:
             ALL > DEBUG > INFO > WARN > ERROR > FATAL > OFF
             Change the value below to DEBUG to log the scripts as they are executed.
         -->
        <level value="INFO"/>
        <appender-ref ref="PowerShellExtensionsFileAppender"/>
      </logger>
    </log4net>
  </sitecore>
</configuration>