﻿<?xml version="1.0" encoding="utf-8"?>
<!--
    
Purpose: This include file configures the Social Connected features that improve the social engagement of your website.

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!-- SOCIAL - ACCOUNT RENEWAL - DAYS BEFORE PRE-EXPIRATION
           The number of days before the network account expires that this account will be added to the Social Account Renewal workflow.
           Default value: 14
      -->
      <setting name="Social.AccountRenewal.DaysBeforePreExpiration" value="14" />
      <!-- SOCIAL - ACCOUNT RENEWAL - ENABLED
           Specifies whether the account renewal functionality is enabled. 
           Related to the known issue: http://developers.facebook.com/bugs/*************** 
           Default value: false
      -->
      <setting name="Social.AccountRenewal.Enabled" value="false" />
      <!-- SOCIAL - ADD NETWORK ACCOUNT WIZARD - AUTHENTICATION TIMEOUT
           Specifies the timeout in seconds for the authentication step of the Add network account wizard.
           Default value: 60
      -->
      <setting name="Social.AddNetworkAccountWizard.AuthenticationTimeout" value="60" />
      <!-- SOCIAL - ALLOW MESSAGE GOAL POSTING BY DEFAULT
           Allows goal posting if the message posting preference for specific user has "default" value.
           Default value: true
      -->
      <setting name="Social.AllowMessageGoalPostingByDefault" value="true" />
      <!-- SOCIAL - CONNECTOR AUTH COMPLETED TYPE
           Specifies the type that implements the IAuthCompleted interface. The AuthCompleted method is invoked  when authentication is completed by the Social Connector.
           Default value: Sitecore.Social.Client.Api.Connector.ConnectorAuthCompleted, Sitecore.Social.Client.Api
      -->
      <setting name="Social.ConnectorAuthCompletedType" value="Sitecore.Social.Client.Api.Connector.ConnectorAuthCompleted, Sitecore.Social.Client.Api" />
      <!-- SOCIAL - FIBONACCI REFRESH STRATEGY - STOP REFRESH AGE
           The number of hours after which the message statistics will not be updated. This should be a Fibonacci number that is greater than or equal to 2.
           2584 hours = 107.6 days = the 18th Fibonacci number
           Default value: 2584
      -->
      <setting name="Social.FibonacciRefreshStrategy.StopRefreshAge" value="2584" />
      <!-- SOCIAL - IOC - ASSEMBLY FULL NAME
           Specifies the full name of the IoC (Inversion of Control) container assembly.
           Default value: Ninject, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7
      -->
      <setting name="Social.IoC.AssemblyFullName" value="Ninject, Version=*******, Culture=neutral, PublicKeyToken=c7192dc5380945e7" />
      <!-- SOCIAL - LINK DOMAIN
           When this setting is set, every link that is generated by the Social Connected module will contain this hostName.
           Default value: ""
      -->
      <setting name="Social.LinkDomain" value="" />
      <!-- SOCIAL - LOGGING - TRACETOLOG
           Specifies if the tracing information should be written to the log files.
           Default value: false
      -->
      <setting name="Social.Logging.TraceToLog" value="false" />
      <!-- SOCIAL - MESSAGE BASE TEMPLATE ID
           Specifies the base template ID for the message.
           Default value: {F0897D68-36EF-4FEC-B3EE-4A58D88298D6}
           Path: /sitecore/templates/System/Social/Message/Message
      -->
      <setting name="Social.MessageBaseTemplateId" value="{F0897D68-36EF-4FEC-B3EE-4A58D88298D6}" />
      <!-- SOCIAL - MESSAGES BY CONTAINER CACHE - ENABLED
           Specifies if the messages by container cache is enabled.
           Default value: true
      -->
      <setting name="Social.MessagesByContainerCache.Enabled" value="true" />
      <!-- SOCIAL - MESSAGES BY CONTAINER CACHE - NAME
           Specifies the name of the messages by container cache.
           Default value: SocialMessagesByContainerCache
      -->
      <setting name="Social.MessagesByContainerCache.Name" value="SocialMessagesByContainerCache" />
      <!-- SOCIAL - MESSAGES BY CONTAINER CACHE - SIZE
           Specifies the size of the messages by container cache.
           Default value: 10MB
      -->
      <setting name="Social.MessagesByContainerCache.Size" value="10MB" />
      <!-- SOCIAL - MESSAGES ROOT PATH
           Specifies the root path for messages.
           Default value: /sitecore/social/Messages
      -->
      <setting name="Social.MessagesRootPath" value="/sitecore/social/Messages" />
      <!-- SOCIAL - MESSAGES - SEARCH INDEX - MASTER 
           The index to use in the repositories when searching for items in the "master" database.
           Default value: social_messages_master
      -->
      <setting name="Social.Messages.SearchIndex.Master" value="social_messages_master" />
      <!-- SOCIAL - MESSAGES - SEARCH INDEX - WEB
           The index to use in the repositories when searching for items in the "web" database.
           Default value: social_messages_web
      -->
      <setting name="Social.Messages.SearchIndex.Web" value="social_messages_web" />
      <!-- SOCIAL - POSTING CONFIGURATION BASE TEMPLATE ID
           The ID of the base template that should be used to configure posting.
           Default value: {F9CB72D5-5399-4B86-AC51-402B81D5D43C}
           Path: /sitecore/templates/System/Social/Posting Configuration/Posting Configuration
      -->
      <setting name="Social.PostingConfigurationBaseTemplateId" value="{F9CB72D5-5399-4B86-AC51-402B81D5D43C}" />
      <!-- SOCIAL - PROFILE - CONTACT LEASE DURATION
           Specifies the lock period in milliseconds. The contact is locked for this period to be updated with social data.
           Default value: 1000
      -->
      <setting name="Social.Profile.ContactLeaseDuration" value="1000" />
      <!-- SOCIAL - PROFILE UPDATING - DAYS BEFORE EXPIRATION
           The number of days before a user profile expires.
           Default value: 2
      -->
      <setting name="Social.ProfileUpdating.DaysBeforeExpiration" value="2" />
      <!-- SOCIAL - PROFILE UPDATING - ENABLED
           Specifies if the profile of the user should be updated after they login.
           Default value: true
      -->
      <setting name="Social.ProfileUpdating.Enabled" value="true" />
    </settings>
    <scheduling>
      <!-- The agent that processes pre-expired accounts. -->
      <agent type="Sitecore.Social.Client.Tasks.ManagePreExpiredAccounts, Sitecore.Social.Client" method="Run" interval="00:00:00">
        <param desc="Social accounts root (item path or ID)">/sitecore/system/social/accounts</param>
      </agent>
      <!-- The agent that synchronizes data with social networks. -->
      <agent type="Sitecore.Social.Client.Tasks.SynchronizeSocialMediaInfo, Sitecore.Social.Client" method="Run" interval="00:00:00" />
      <!-- The agent that posts messages that are marked to be posted automatically. -->
      <agent type="Sitecore.Social.Client.Tasks.PostFlaggedMessages, Sitecore.Social.Client" method="Run" interval="01:00:00">
        <param desc="Social accounts root (item path or ID)">/sitecore/system/social/accounts</param>
      </agent>
      <!-- The agent that updates the statistics about social messages. -->
      <agent type="Sitecore.Social.Client.Tasks.UpdateMessagesStatistics, Sitecore.Social.Client" method="Run" interval="01:00:00" />
    </scheduling>
    <social>
      <api>
        <accountBuilders>
          <builder accountTemplateID="{2C0B92B7-6F32-48AC-A880-14A07995F118}" builderType="Sitecore.Social.Builders.NetworkAccountBuilder,Sitecore.Social" accountType="Sitecore.Social.Domain.Model.NetworkAccount,Sitecore.Social.Domain" />
          <builder accountTemplateID="{39394D34-D9B8-4334-A712-B3216E936750}" builderType="Sitecore.Social.Builders.SocialMarketerAccountBuilder,Sitecore.Social" accountType="Sitecore.Social.Domain.Model.SocialMarketerAccount,Sitecore.Social.Domain" />
        </accountBuilders>
        <messageStatusBuilders>
          <builder postingConfigurationName="ContentPosting" builderType="Sitecore.Social.Builders.ContentPostingMessageStatusBuilder,Sitecore.Social" />
          <builder postingConfigurationName="GoalPosting" builderType="Sitecore.Social.Builders.GoalPostingMessageStatusBuilder,Sitecore.Social" />
        </messageStatusBuilders>
        <postingConfigurationBuilders>
          <builder postingConfigurationName="ContentPosting" builderType="Sitecore.Social.Builders.ContentPostingConfigurationBuilder,Sitecore.Social" postingConfigurationType="Sitecore.Social.Domain.Model.ContentPostingConfiguration,Sitecore.Social.Domain" />
          <builder postingConfigurationName="GoalPosting" builderType="Sitecore.Social.Builders.GoalPostingConfigurationBuilder,Sitecore.Social" postingConfigurationType="Sitecore.Social.Domain.Model.GoalPostingConfiguration,Sitecore.Social.Domain" />
        </postingConfigurationBuilders>
      </api>
      <interactionChannelMappings>
        <!-- 41-91-05: Online/Social Community/Facebook Social Community -->
        <channel channelId="{A9F2D058-95A5-4461-B1E5-8502D2303AF1}">
          <!-- Facebook -->
          <channelMapping urlReferrerHost="www.facebook.com" />
          <!-- Facebook for mobile -->
          <channelMapping urlReferrerHost="m.facebook.com" />
          <!-- Facebook's Link Shim -->
          <channelMapping urlReferrerHost="l.facebook.com" />
          <!-- Facebook's Link Shim for mobile -->
          <channelMapping urlReferrerHost="lm.facebook.com" />
        </channel>
        <!-- 41-91-04: Online/Social Community/LinkedIn Social Community -->
        <channel channelId="{6FD56D27-FD68-405A-BC26-566C7BEF7031}">
          <!-- LinkedIn -->
          <channelMapping urlReferrerHost="www.linkedin.com" />
        </channel>
        <!-- 41-91-03: Online/Social Community/YouTube Social Community -->
        <channel channelId="{86761432-FDBB-4C4B-AFAE-557130AE4D61}">
          <!-- YouTube -->
          <channelMapping urlReferrerHost="www.youtube.com" />
        </channel>
        <!-- 41-91-06: Online/Social Community/Twitter Social Community -->
        <channel channelId="{6D3D2374-AF56-44FE-B99A-20843B440B58}">
          <!-- Twitter's URL shortener, which is always set in the "Referrer" HTTP header -->
          <channelMapping urlReferrerHost="t.co" />
        </channel>
        <!-- 41-91-02: Online/Social Community/Google Plus Social Community -->
        <channel channelId="{DC70F72E-0A36-404D-8B10-86FE765A3BCC}">
          <!-- Google+ -->
          <channelMapping urlReferrerHost="plus.url.google.com" />
        </channel>
      </interactionChannelMappings>
      <logging>
        <!-- The assemblies that are searched for entity formatters that format information before it is added to the log file. -->
        <entityFormatterAssemblies>
          <assembly name="Sitecore.Social.Infrastructure.Logging.dll" />
        </entityFormatterAssemblies>
      </logging>
      <postingConfigurations>
        <postingConfiguration name="ContentPosting" TemplateId="{A6575AEC-8BC5-4591-B730-8FCDC0CCDDB5}">
          <builder type="Sitecore.Social.MessagePosting.Providers.ContentPostingMessageBuilder, Sitecore.Social" />
        </postingConfiguration>
        <postingConfiguration name="GoalPosting" TemplateId="{7E49B0AA-5EBA-4A02-852C-4744E2288AC2}">
          <builder type="Sitecore.Social.MessagePosting.Providers.GoalPostingMessageBuilder, Sitecore.Social" />
        </postingConfiguration>
      </postingConfigurations>
    </social>
    <commands>
      <command name="social:account:add" type="Sitecore.Social.Client.Commands.AddNetworkAccount, Sitecore.Social.Client" />
      <command name="social:account:renew" type="Sitecore.Social.Client.Commands.RenewNetworkAccount, Sitecore.Social.Client" />
      <command name="social:dialog:show" type="Sitecore.Social.Client.MessagePosting.Commands.SocialCenter, Sitecore.Social.Client" />
      <command name="social:message:delete" type="Sitecore.Social.Client.MessagePosting.Commands.DeleteMessage, Sitecore.Social.Client" />
      <command name="social:message:edit" type="Sitecore.Social.Client.MessagePosting.Commands.EditMessage, Sitecore.Social.Client" />
      <command name="social:message:executeWorkflowCommand" type="Sitecore.Social.Client.MessagePosting.Commands.ExecuteMessageWorkflowCommand, Sitecore.Social.Client" />
      <command name="social:message:new" type="Sitecore.Social.Client.MessagePosting.Commands.NewMessage, Sitecore.Social.Client" />
      <command name="social:message:post" type="Sitecore.Social.Client.MessagePosting.Commands.PostMessage, Sitecore.Social.Client" />
      <command name="social:socialmarketeraccount:add" type="Sitecore.Social.Client.Commands.AddSocialMarketerAccount, Sitecore.Social.Client" />
    </commands>
    <controlSources>
      <source mode="on" namespace="Sitecore.Social.Client" folder="/sitecore/shell/applications/social/wizards/AddNetworkAccount" deep="true" />
    </controlSources>
    <events>
      <event name="publish:itemProcessed">
        <handler type="Sitecore.Social.Client.MessagePosting.Handlers.ItemPublishedHandler, Sitecore.Social.Client" method="PostSocialMessages" />
      </event>
      <event name="publish:end">
        <handler type="Sitecore.Social.Client.MessagePosting.Handlers.PublishEndHandler, Sitecore.Social.Client" method="ClearDbMesageCache" />
      </event>
      <event name="user:deleted">
        <handler type="Sitecore.Social.Client.Connector.Handlers.DeleteUserHandler, Sitecore.Social.Client" method="OnUserDeleted" />
      </event>
      <!-- social:connector:user:created(Sitecore.Security.Accounts.User sitecoreUser)
           Raised after a new Sitecore user has been created by the Social Connected module. -->
      <event name="social:connector:user:created">
      </event>
      <!-- social:connector:user:loggedin(SocialNetworkUserLoggedInEventArgs args)
           Raised after a user has logged from a social network. -->
      <event name="social:connector:user:loggedin">
      </event>
      <!-- social:connector:user:socialprofileattached(SocialProfileAttachedEventArgs args)
           Raised after a new social profile has been attached to a user. -->
      <event name="social:connector:user:socialprofileattached">
      </event>
      <!-- social:message:created(SocialMessageCreatedEventArgs args)
           Raised after a new social message has been created. -->
      <event name="social:message:created">
      </event>
      <!-- social:message:deleted(SocialMessageDeletedEventArgs args)
           Raised after a social message has been deleted. -->
      <event name="social:message:deleted">
      </event>
      <!-- social:message:updated(SocialMessageUpdatedEventArgs args)
           Raised after a social message has been updated. -->
      <event name="social:message:updated">
      </event>
    </events>
    <fieldTypes>
      <fieldType name="Accounts Multilist" type="Sitecore.Data.Fields.MultilistField,Sitecore.Kernel" resizable="true" />
      <fieldType name="Campaign Tree" type="Sitecore.Data.Fields.ReferenceField,Sitecore.Kernel" />
      <fieldType name="Countable Edit" type="Sitecore.Data.Fields.TextField,Sitecore.Kernel" resizable="true" />
    </fieldTypes>
    <model>
      <elements>
        <element interface="Sitecore.Social.Connector.Facets.Contact.SocialProfile.ISocialProfileFacet, Sitecore.Social" implementation="Sitecore.Social.Connector.Facets.Contact.SocialProfile.SocialProfileFacet, Sitecore.Social" />
        <element interface="Sitecore.Social.Connector.Facets.Contact.SocialProfile.INetworkElement, Sitecore.Social" implementation="Sitecore.Social.Connector.Facets.Contact.SocialProfile.NetworkElement, Sitecore.Social" />
        <element interface="Sitecore.Social.Connector.Facets.Contact.SocialProfile.IFieldElement, Sitecore.Social" implementation="Sitecore.Social.Connector.Facets.Contact.SocialProfile.FieldElement, Sitecore.Social" />
      </elements>
      <entities>
        <contact>
          <facets>
            <facet name="SocialProfile" contract="Sitecore.Social.Connector.Facets.Contact.SocialProfile.ISocialProfileFacet, Sitecore.Social" />
          </facets>
        </contact>
      </entities>
    </model>
    <pipelines>
      <determineInteractionChannel>
        <!-- Determines the social channels for the interaction. The social channels to be determined are listed at the social/interactionChannelMappings configuration. -->
        <processor type="Sitecore.Social.Analytics.Pipelines.DetermineInteractionChannel.SocialChannels, Sitecore.Social" patch:after="*[last()]" />
      </determineInteractionChannel>
      <getLookupSourceItems>
        <processor type="Sitecore.Social.Client.Pipelines.GetLookupSourceItems.GetAccountsDataSource, Sitecore.Social.Client" />
      </getLookupSourceItems>
      <initialize>
        <!-- Processor initializes the "Social" MVC area. -->
        <processor type="Sitecore.Social.Client.Mvc.Pipelines.Initialize.RegisterSocialArea, Sitecore.Social.Client.Mvc" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Loader.InitializeRoutes, Sitecore.Mvc']" />
        <!-- Processor maps remote events.-->
        <processor type="Sitecore.Social.Client.Pipelines.Initialize.RemoteEventMap, Sitecore.Social.Client" />
      </initialize>
      <social.createMessage>
        <processor type="Sitecore.Social.MessagePosting.Pipelines.CreateMessage.GetRoot, Sitecore.Social" />
        <processor type="Sitecore.Social.MessagePosting.Pipelines.CreateMessage.CreateItem, Sitecore.Social" />
        <processor type="Sitecore.Social.MessagePosting.Pipelines.CreateMessage.SaveData, Sitecore.Social" />
      </social.createMessage>
      <social.buildMessage>
        <processor type="Sitecore.Social.MessagePosting.Pipelines.BuildMessage.ResolveRenderer, Sitecore.Social" />
        <processor type="Sitecore.Social.MessagePosting.Pipelines.BuildMessage.BuildMessage, Sitecore.Social" />
        <processor type="Sitecore.Social.MessagePosting.Pipelines.BuildMessage.ReplaceTokens, Sitecore.Social" />
      </social.buildMessage>
      <social.matchUser>
        <processor type="Sitecore.Social.Connector.Pipelines.MatchUser.PrepareUserData, Sitecore.Social" />
        <processor type="Sitecore.Social.Connector.Pipelines.MatchUser.FindByEmail, Sitecore.Social" />
        <processor type="Sitecore.Social.Connector.Pipelines.MatchUser.FindByDomain, Sitecore.Social" />
        <processor type="Sitecore.Social.Connector.Pipelines.MatchUser.FindByNetworkCredentials, Sitecore.Social" />
        <processor type="Sitecore.Social.Connector.Pipelines.MatchUser.CreateUser, Sitecore.Social" />
      </social.matchUser>
      <social.initializeApi>
        <processor type="Sitecore.Social.Infrastructure.IoC.IoCInitialization, Sitecore.Social.Infrastructure">
          <assemblies hint="list:AddAssembly">
            <assembly>Sitecore.Social.Configuration.dll</assembly>
            <assembly>Sitecore.Social.Infrastructure.dll</assembly>
            <assembly>Sitecore.Social.Infrastructure.Logging.dll</assembly>
            <assembly>Sitecore.Social.SitecoreAccess.dll</assembly>
            <assembly>Sitecore.Social.NetworkProviders.dll</assembly>
            <assembly>Sitecore.Social.dll</assembly>
            <assembly>Sitecore.Social.Api.dll</assembly>
            <assembly>Sitecore.Social.Client.Api.dll</assembly>
            <assembly>Sitecore.Social.Client.Common.dll</assembly>
          </assemblies>
        </processor>
      </social.initializeApi>
      <social.readMessage>
        <processor type="Sitecore.Social.MessagePosting.Pipelines.ReadMessage.ReadMessages, Sitecore.Social" />
      </social.readMessage>
      <social.postMessage>
        <processor type="Sitecore.Social.MessagePosting.Pipelines.PostMessage.ResolveMessagePostingProvider, Sitecore.Social" />
        <processor type="Sitecore.Social.MessagePosting.Pipelines.PostMessage.PostMessage, Sitecore.Social" />
      </social.postMessage>
    </pipelines>
    <processors>
      <loggedin>
        <processor mode="on" type="Sitecore.Social.Client.Pipelines.LoggedIn.UpdateUserProfile, Sitecore.Social.Client" />
      </loggedin>
    </processors>
    <sitecore.experienceeditor.speak.requests>
      <!-- Messages -->
      <request name="ExperienceEditor.Social.SocialCenter.GetMessagesCount" type="Sitecore.Social.Client.Speak.ExperienceEditor.Requests.SocialCenter.GetMessagesCount, Sitecore.Social.Client.Speak" />
      <request name="ExperienceEditor.Social.SocialCenter.GetDialogUrl" type="Sitecore.Social.Client.Speak.ExperienceEditor.Requests.SocialCenter.GetDialogUrl, Sitecore.Social.Client.Speak" />
    </sitecore.experienceeditor.speak.requests>
    <ui>
      <usings>
        <using patch:after="using[.='Sitecore.Xml']">Sitecore.Social.Client.Wizards.AddNetworkAccount</using>
        <using patch:after="using[.='Sitecore.Social.Client.Wizards.AddNetworkAccount']">Sitecore.Social.Client.Wizards.AddSocialMarketerAccount</using>
        <using patch:before="using[.='Sitecore.Social.Client.Wizards.AddNetworkAccount']">System</using>
      </usings>
      <references>
        <reference patch:after="reference[.='/bin/Sitecore.Client.dll']">/bin/Sitecore.Social.Client.dll</reference>
        <reference patch:before="reference[.='/bin/Sitecore.Social.Client.dll']">System.dll</reference>
      </references>
    </ui>
  </sitecore>
</configuration>