﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!--  SPEAK MVC COMMAND ROUTE PREFIX
            Defines the route prefix for commands. The full route is api/sitecore/{controller}/{action}
            Default: "api/sitecore/"
      -->
      <setting name="Speak.Mvc.CommandRoutePrefix" value="api/sitecore/"/>
    </settings>
    
    <pipelines>
      <initialize>
        <processor type="Sitecore.Mvc.Pipelines.Initialize.InitializeCommandRoute, Sitecore.Speak.Mvc" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Loader.InitializeRoutes, Sitecore.Mvc']"/>        
      </initialize>

      <mvc.renderPlaceholder>
        <processor type="Sitecore.Mvc.Pipelines.Response.RenderPlaceholder.RenderAddedContent, Sitecore.Speak.Mvc"/>
      </mvc.renderPlaceholder>
    </pipelines>
  </sitecore>
</configuration>