﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the tracking subsystem of the Sitecore Experience Database so that it schedules new online visits
and automation states for aggregation into the reporting database.

If you want to disable this functionality and prevent new visits and automation states from being aggregated into the reporting database,
you can rename this config file so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <automationManager>
      <providers>
        <add name="default">
        </add>
      </providers>
    </automationManager>

    <pipelines>
      <submitSessionContext>
        <processor type="Sitecore.Analytics.Aggregation.CommitSession.ProcessingPoolAdd, Sitecore.Analytics.Aggregation">
          <Pools hint="list">
            <live ref="aggregationProcessing/processingPools/live"/>
          </Pools>
        </processor>
      </submitSessionContext>
    </pipelines>
    
  </sitecore>
</configuration>
