﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <!-- ITEM BUCKET MANAGER
         The item bucket manager and provider classes 
    -->
    <bucketManager defaultProvider="default" enabled="true">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.Buckets.Managers.PipelineBasedBucketProvider, Sitecore.Buckets"/>
      </providers>
    </bucketManager>

    <!-- EMPTY BUCKET CLEANING JOB
         This job runs at the set interval time and removes item bucket folders that no longer contain any items.
         This could be cause by deleting an item from a bucket, which has no other items in it.
    -->
    <scheduling>
      <frequency>00:00:05</frequency>
      <!--<agent type="Sitecore.Buckets.Tasks.RemoveEmptyBucketFolders" method="Run" interval="00:00:10">
            <DatabaseName>master</DatabaseName>
          </agent>-->
    </scheduling>
    <databases>
      <database id="core" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <Engines.DataEngine.Commands.AddFromTemplatePrototype>
          <obj type="Sitecore.Buckets.Commands.AddFromTemplateCommand, Sitecore.Buckets" />
        </Engines.DataEngine.Commands.AddFromTemplatePrototype>
      </database>
      <database id="master" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <Engines.DataEngine.Commands.AddFromTemplatePrototype>
          <obj type="Sitecore.Buckets.Commands.AddFromTemplateCommand, Sitecore.Buckets" />
        </Engines.DataEngine.Commands.AddFromTemplatePrototype>
      </database>
      <database id="web" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <Engines.DataEngine.Commands.AddFromTemplatePrototype>
          <obj type="Sitecore.Buckets.Commands.AddFromTemplateCommand, Sitecore.Buckets" />
        </Engines.DataEngine.Commands.AddFromTemplatePrototype>
      </database>
    </databases>
    <events>
      <!-- Uncomment the below item:added event if you want Sitecore to automatically create item buckets when the number of children under an item gets too many. -->
      <!--<event name="item:added">
        <handler type="Sitecore.Buckets.Events.AutoBucket, Sitecore.Buckets" method="Execute"/>
      </event>-->
      <!-- Event hook before a item is getting bucketed
           Args: (ID) NewId, (string) ItemName, (ID) TemplateId, (Item) NewDestination -->
      <event name="item:bucketing:adding" />
      <!-- Event hook after a item is getting bucketed
           Args: (Item) Item -->
      <event name="item:bucketing:added" />
      <!-- Event hook before a cloned item is placed in an item bucket.   
           Args: (CopyItemArgs) Args -> (Item[]) Copies -->
      <event name="item:bucketing:cloning"/>
      <!-- Event hook after a cloned item is placed in an item bucket. 
           Args: (CopyItemArgs) Args -> (Item[]) Copies -->
      <event name="item:bucketing:cloned"/>
      <!-- Event hook before copying an item into an item bucket. 
           Args: (CopyItemArgs) Args -> (Item[])Copies -->
      <event name="item:bucketing:copying"/>
      <!-- Event hook after copying an item into an item bucket. 
           Args: (CopyItemArgs) Args -> (Item[])Copies -->
      <event name="item:bucketing:copied"/>
      <!-- Event hook after duplicating an item into an item bucket.
           Args: (ClientPipelineArgs) Args -> -->
      <event name="item:bucketing:duplicating"/>
      <!-- Event hook after duplicating an item into an item bucket.
           Args: (ClientPipelineArgs) Args -> -->
      <event name="item:bucketing:duplicated"/>
      <!-- Event hook before moving an item into an item bucket. 
           Args: (ClientPipelineArgs) Args -> -->
      <event name="item:bucketing:moving"/>
      <!-- Event hook after moving an item into an item bucket. 
           Args: (ClientPipelineArgs) Args -> -->
      <event name="item:bucketing:moved"/>
      <!-- Event hook before converting an item into an item bucket starts. 
           Args: (BucketArgs) Args -> (Item) Item, (string) DatabaseName -->
      <event name="item:bucketing:starting"/>
      <!-- Event hook after converting an item into an item bucket finishes. 
           Args: (BucketArgs) Args -> (Item) Item, (string) DatabaseName -->
      <event name="item:bucketing:ending"/>
      <!-- Event hook before converting an item bucket into an ordinary content item starts. 
           Args: (BucketArgs) Args -> (Item) Item, (string) DatabaseName -->
      <event name="item:unbucketing:starting"/>
      <!-- Event hook after converting an item bucket into an ordinary content item finishes. 
           Args: (BucketArgs) Args -> (Item) Item, (string) DatabaseName -->
      <event name="item:unbucketing:ending"/>
      <!-- Event hook before moving an item into an item bucket and from one database to another database. 
           Args: (EventArgs) Args -> (Item) Item -->
      <event name="item:transferred">
        <handler type="Sitecore.Buckets.Events.ItemTransferredEventHandler, Sitecore.Buckets" method="Execute"/>
      </event>
    </events>
    <authorization>
      <patch:attribute name="defaultProvider">custom</patch:attribute>
      <providers>
        <clear />
        <add name="custom" type="Sitecore.Buckets.Security.BucketAuthorizationProvider, Sitecore.Buckets" connectionStringName="core" embedAclInItems="true" />
      </providers>
    </authorization>
    <!-- Adds the ability to give users/roles permission to create an item bucket or convert an item bucket into an ordinary content item. -->
    <accessRights>
      <rights>
        <add patch:before="add[@name='*']" name="bucket:makebucket" comment="Create Bucket"   title="Create Bucket"   type="Sitecore.Buckets.Security.BucketAccessRight, Sitecore.Buckets" />
        <add patch:before="add[@name='*']" name="bucket:unmake"     comment="Revert Bucket" title="Revert Bucket" type="Sitecore.Buckets.Security.BucketAccessRight, Sitecore.Buckets" />
      </rights>
      <rules>
        <add patch:before="*[1]" prefix="bucket:" ancestor="{11111111-1111-1111-1111-111111111111}" comment="/sitecore" />
      </rules>
    </accessRights>
    <!-- ITEM BUCKET COMMANDS
         The commands that run the item bucket system.
    -->
    <commands>
      <command name="bucket:addtag"                              type="Sitecore.Buckets.Search.SearchOperations.AddTag, Sitecore.Buckets"/>
      <command name="bucket:applycampaignsgoalseventstoallitems" type="Sitecore.Buckets.Search.SearchOperations.ApplyCampaignsGoalsEventsToAllItems, Sitecore.Buckets"/>
      <command name="bucket:applyfieldvaluechangetoallitems"     type="Sitecore.Buckets.Search.SearchOperations.ApplyFieldValueChangeToAllItems, Sitecore.Buckets"/>
      <command name="bucket:applypresentationtoallitems"         type="Sitecore.Buckets.Search.SearchOperations.ApplyPresentationToItems, Sitecore.Buckets"/>
      <command name="bucket:applyprofilescoretoallitems"         type="Sitecore.Buckets.Search.SearchOperations.ApplyProfileCardsToAllItems, Sitecore.Buckets"/>
      <command name="bucket:applysecurityrule"                   type="Sitecore.Buckets.Search.SearchOperations.ApplySecurityRule, Sitecore.Buckets"/>
      <command name="bucket:cloneresultsto"                      type="Sitecore.Buckets.Search.SearchOperations.CloneToCommand, Sitecore.Buckets"/>
      <command name="bucket:copyresultsto"                       type="Sitecore.Buckets.Search.SearchOperations.CopyToCommand, Sitecore.Buckets"/>
      <command name="bucket:copytodatasourcequery"               type="Sitecore.Buckets.Search.SearchOperations.CopyToDatasourceQuery, Sitecore.Buckets"/>
      <command name="bucket:deleteresults"                       type="Sitecore.Buckets.Search.SearchOperations.DeleteCommand, Sitecore.Buckets"/>
      <command name="bucket:moveresultsto"                       type="Sitecore.Buckets.Search.SearchOperations.MoveToCommand, Sitecore.Buckets"/>
      <command name="bucket:publishitems"                        type="Sitecore.Buckets.Search.SearchOperations.PublishItems, Sitecore.Buckets"/>
      <command name="bucket:runconditionalrule"                  type="Sitecore.Buckets.Search.SearchOperations.RunConditionalRule, Sitecore.Buckets"/>
      <command name="bucket:searchandreplace"                    type="Sitecore.Buckets.Search.SearchOperations.Replace, Sitecore.Buckets"/>
      <command name="bucket:serializeitems"                      type="Sitecore.Buckets.Search.SearchOperations.SerializeItems, Sitecore.Buckets"/>
      <command name="bucket:usage"                               type="Sitecore.Buckets.Commands.BucketUsage, Sitecore.Buckets"/>
      <command name="contenteditor:launchblanktab"               type="Sitecore.Buckets.Commands.AddBlankSearch, Sitecore.Buckets"/>
      <command name="contenteditor:launchsilenttab"              type="Sitecore.Buckets.Commands.AddSilentTab, Sitecore.Buckets"/>
      <command name="contenteditor:launchtab"                    type="Sitecore.Buckets.Commands.AddTab, Sitecore.Buckets"/>
      <command name="contenteditor:togglebucketitems"            type="Sitecore.Buckets.Commands.ToggleBucket, Sitecore.Buckets"/>
      <command name="field:setastag"                             type="Sitecore.Buckets.Commands.SetFieldAsTag, Sitecore.Buckets"/>
      <command name="field:setsearchimagefield"                  type="Sitecore.Buckets.Commands.MarkImageFieldAsSearchResultsImage, Sitecore.Buckets"/>
      <command name="item:bucket"                                type="Sitecore.Buckets.Commands.MakeBucket, Sitecore.Buckets" />
      <command name="item:bucketable"                            type="Sitecore.Buckets.Commands.MakeItemBucketable, Sitecore.Buckets"/>
      <command name="item:bucketsearch"                          type="Sitecore.Buckets.Commands.Search, Sitecore.Buckets"/>
      <command name="item:gobacktoclosestparentbucket"           type="Sitecore.Buckets.Commands.GoToClosestBucketParent, Sitecore.Buckets"/>
      <command name="item:pastefromclipboard">
        <patch:attribute name="type">Sitecore.Buckets.Commands.PasteFromClipboardBuckets, Sitecore.Buckets</patch:attribute>
      </command>
      <command name="item:resetbucketable"                       type="Sitecore.Buckets.Commands.ResetBucketable, Sitecore.Buckets"/>
      <command name="item:syncbucket"                            type="Sitecore.Buckets.Commands.SyncBucket, Sitecore.Buckets" />
      <command name="item:unbucket"                              type="Sitecore.Buckets.Commands.UnMakeBucket, Sitecore.Buckets" />
      <command name="security:setsecurityrule"                   type="Sitecore.Buckets.Commands.SetSecurityRule, Sitecore.Buckets"/>
      <command name="template:bucketable"                        type="Sitecore.Buckets.Commands.MakeTemplateBucketable, Sitecore.Buckets"/>      
      <command name="template:launchstandardvalues"              type="Sitecore.Buckets.Commands.LaunchStandardValues, Sitecore.Buckets"/>      
    </commands>
    <pipelines>
      <!-- RESOLVE UI DOCUMENT MAPPER FACTORY RULES PIPELINE
           This pipeline is executed when you use the search UI. The default processor overrides the document mapper rules to ensure that
           the search results are instantiated as SitecoreUISearchResultItem objects. This is necessary for the default search views to
           display search results correctly.
      -->
      <buckets.resolveUIDocumentMapperFactoryRules>
        <processor type="Sitecore.Buckets.Pipelines.UI.Search.ResolveDocumentMapperFactoryRule.ResolveSitecoreUIRules, Sitecore.Buckets"/>
      </buckets.resolveUIDocumentMapperFactoryRules>

      <!-- ADD SEARCH TAB PIPELINE
           Pipeline for adding search tabs to items.
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.addSearchTabToItem>
        <processor type="Sitecore.Buckets.Pipelines.BucketUI.AddSearchTabToItem.AddSearchTab, Sitecore.Buckets"/>
      </buckets.addSearchTabToItem>
      
      <!-- CLONE ITEM INTO BUCKET PIPELINE
           Pipeline for cloning items into an item bucket.
           Arguments : (Item) Source, (Item) Target, (bool) Whether or not cloning was successful, (bool) Deep check.
      -->
      <buckets.cloneItemIntoBucket>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.CloneItem.RunCloning, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.CloneItem.ProcessParentReference, Sitecore.Buckets"/>
      </buckets.cloneItemIntoBucket>
      
      <!-- COPY ITEM INTO BUCKET PIPELINE
           Pipeline for copying items into an item bucket.
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.copyItemIntoBucket>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.CopyItem.RunCopying, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.CopyItem.ProcessParentReference, Sitecore.Buckets"/>
      </buckets.copyItemIntoBucket>
      
      <!-- CREATE BUCKET PIPELINE
           Pipeline for creating an item bucket.
           Arguments : (Item) Bucket Item, (Action<Item>) CallBack, (bool) Whether the item bucket was created or not.
      -->
      <buckets.createBucket>
        <processor type="Sitecore.Buckets.Pipelines.BucketOperations.CreateBucket.RunBucketingProcess, Sitecore.Buckets"/>
      </buckets.createBucket>
      
      <!-- FETCH FACET PIPELINE
           Pipeline for getting facets (grouping and categorization of search results) from a search.
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.getFacets>
        <processor type="Sitecore.Buckets.Pipelines.Search.GetFacets.RunFacets, Sitecore.Buckets"/>
      </buckets.getFacets>
      
      <!-- IS ITEM A BUCKET PIPELINE
           Pipeline for determining whether or not an item is an item bucket.
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.isBucket>
        <processor type="Sitecore.Buckets.Pipelines.BucketStatus.IsBucket.CheckIfItemIsABucket, Sitecore.Buckets"/>
      </buckets.isBucket>
      
      <!-- ITEM IN BUCKET CHECK PIPELINE
           Pipeline for checking whether or not an item is contained within an item bucket.
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.isItemContainedWithinBucket>
        <processor type="Sitecore.Buckets.Pipelines.BucketStatus.IsItemContainedWithinBucket.CheckIfItemsIsInBucket, Sitecore.Buckets"/>
      </buckets.isItemContainedWithinBucket>
      
      <!-- TEMPLATE BUCKETABLE PIPELINE
           Pipeline for determining whether or not an item is "bucketable".
           Arguments : (Item) The item to add a search tab to. (bool) Whether the tab was added or not.
      -->
      <buckets.isTemplateBucketable>
        <processor type="Sitecore.Buckets.Pipelines.BucketStatus.IsTemplateBucketable.RunTemplateCheck, Sitecore.Buckets"/>
      </buckets.isTemplateBucketable>
      
      <!-- MOVE ITEM INTO BUCKET PIPELINE
           Pipeline for moving items into or out of an item bucket. This pipeline also makes sure that the item being moved is “bucketable”. 
               If it is bucketable, it is automatically bucketed after it has been moved.
           Arguments : (Item) The item being moved. (bool) Whether the item was moved successfully or not.
      -->
      <buckets.moveItemIntoBucket>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.MoveItem.RunMoving, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.ItemOperations.MoveItem.ProcessParentReference, Sitecore.Buckets"/>
      </buckets.moveItemIntoBucket>
      
      <!-- UNBUCKETING PROCESS PIPELINE
               Pipeline for converting an item bucket into a content item.
           Arguments : (Item) The item bucket being converted into an ordinary content item. (bool) Whether the conversion was successful or not.
      -->
      <buckets.removeBucket>
        <processor type="Sitecore.Buckets.Pipelines.BucketOperations.RemoveBucket.RunUnbucketProcess, Sitecore.Buckets"/>
      </buckets.removeBucket>
      
      <!-- BUCKET SYNCING PIPELINE
           Pipeline for syncing an item bucket. This updates an item bucket after changing the bucketable value of templates, or if  
           the bucketing process was interrupted and needs to be resumed.
           Arguments : (Item) The item bucket being synced. (bool) Whether the item bucket was synced successfully or not.
      -->
      <buckets.syncBucket>
        <processor type="Sitecore.Buckets.Pipelines.BucketOperations.SyncBucket.RunSyncProcess, Sitecore.Buckets"/>
      </buckets.syncBucket>

      <!-- SEARCH DIALOG GLOBAL FILTERS PIPELINE
           Pipeline for adding default search filters to built-in Sitecore dialog boxes that use the search tab, for example, the Insert From Template dialog box.         
           This allows you to set default locations to search in, default templates to filter on, etc.
      -->
      <buckets.dialogSearchFilters>
        <processor type="Sitecore.Buckets.Pipelines.UI.DialogSearchFilters.SetDefaultSearchFilters, Sitecore.Buckets"/>
      </buckets.dialogSearchFilters>
      
      <!-- EXPAND ID-BASED SEARCH FILTERS
           Sitecore executes this pipeline for search filters when their Client Side Hook is set to "id". To display a user-friendly item name
           in the UI, the ExpandIds processor replaces "<ID>" with "<Item Display Name>|<ID>" in these search filters.
      -->
      <buckets.expandIdBasedSearchFilters>
        <processor type="Sitecore.Buckets.Pipelines.UI.ExpandIdBasedSearchFilters.ExpandIds, Sitecore.Buckets"/>
      </buckets.expandIdBasedSearchFilters>
      
      <!-- SEARCH RESULT DISPLAY PIPELINE
           Pipeline for dynamically setting the SearchResultItem properties  
      -->
      <buckets.fillItem>
        <processor type="Sitecore.Buckets.Pipelines.UI.FillItem.SetItemProperties, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.UI.FillItem.ShowFieldValuesInResults, Sitecore.Buckets"/>
      </buckets.fillItem>
      
      <!-- EXTENDED GETCONTENTEDITORWARNINGS PIPELINE
           This allows for warnings related to item buckets
      -->
      <getContentEditorWarnings>
        <processor type="Sitecore.Buckets.Pipelines.GetContentEditorWarnings.StructuredItemInBucket, Sitecore.Buckets" />
      </getContentEditorWarnings>

      <!-- This allows the pipeline to return examples of the items that are returned by search-based datasources. -->
      <getDatasourceExamples>
        <processor type="Sitecore.Buckets.Pipelines.GetDatasourceExamples.GetSearchBasedDatasourceExamples, Sitecore.Buckets"/>
      </getDatasourceExamples>
      
      <!-- EXTENDED TEMPLATE FIELD SOURCE RESOLVERS 
           This allows the custom Template Field sources to be resolved. 
      -->
      <getLookupSourceItems>
        <processor patch:before="*[@type='Sitecore.Pipelines.GetLookupSourceItems.ProcessQuerySource, Sitecore.Kernel']" type="Sitecore.Buckets.FieldTypes.CustomDataSource, Sitecore.Buckets"/>
      </getLookupSourceItems>
      
      <!-- DYNAMIC QUICK ACTION PIPELINE
           Pipeline for injecting Dynamic Quick Actions into the SitecoreItem object. Useful for content authoring.
      -->
      <buckets.dynamicQuickActions>
        <processor type="Sitecore.Buckets.Pipelines.UI.QuickActions.ShowCloneSource, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.UI.QuickActions.ShowTranslate, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.UI.QuickActions.ShowWorkflowCommands, Sitecore.Buckets"/>
        <processor type="Sitecore.Buckets.Pipelines.UI.QuickActions.ShowInsertOptions, Sitecore.Buckets"/>
      </buckets.dynamicQuickActions>
      
      <!-- DYNAMIC UI SEARCH FIELD PIPELINE
           Pipeline to Inject Dynamic Values into the SitecoreItem object. Useful for content authoring and delivery environment 
      -->
      <buckets.dynamicFields>
        <processor type="Sitecore.Buckets.Pipelines.UI.DynamicFields.FetchDynamicFieldValues, Sitecore.Buckets"/>
        <!-- Allows users to place "IsLocked" in their views and show whether an item is locked or not. -->
        <processor type="Sitecore.Buckets.Pipelines.UI.DynamicFields.IsLocked, Sitecore.Buckets"/>
        <!-- Will allow users to place "Tags" in their views and show the items tags from the __semantics field-->
        <processor type="Sitecore.Buckets.Pipelines.UI.DynamicFields.ItemTags, Sitecore.Buckets"/>
      </buckets.dynamicFields>
      
      <!-- TOKEN ENHANCEMENTS
           New resolvers for replacing the tokens for 
           $currentuser -> Returns the authenticated user name of the current user. 
           $allquery -> Returns a default query that lists all items i.e. text=*.
           $allmyworkflowitems -> Returns a default query that lists all of the current user's items that are in a workflow.
      -->
      <expandInitialFieldValue help="Processors should derive from Sitecore.Pipelines.ExpandInitialFieldValue.ExpandInitialFieldValueProcessor">
        <processor patch:after="*[@type='Sitecore.Pipelines.ExpandInitialFieldValue.ReplaceVariables, Sitecore.Kernel']" type="Sitecore.Buckets.Util.TokenReplacer, Sitecore.Buckets"/>
      </expandInitialFieldValue>

      <getQueryState>
        <processor type="Sitecore.Buckets.Pipelines.GetQueryState.CommandStateProcessor, Sitecore.Buckets"/>
      </getQueryState>

      <publish>
        <!-- Extending publish pipeline to always add bucket folders to the queue when a bucketed item is being published  -->
        <processor patch:after="processor[@type='Sitecore.Publishing.Pipelines.Publish.AddItemsToQueue, Sitecore.Kernel']" type="Sitecore.Buckets.Pipelines.Publish.AddBucketFoldersToQueue, Sitecore.Buckets" />
      </publish>

      <renderField>
        <processor patch:after="processor[@type='Sitecore.Pipelines.RenderField.GetLinkFieldValue, Sitecore.Kernel']" type="Sitecore.Buckets.Pipelines.RenderField.GetLinkFieldValue, Sitecore.Buckets"/>
      </renderField>
    </pipelines>
    <processors>
      <!-- LAUNCH SEARCH RESULT
           This pipeline is invoked when a user clicks a search result in the search UI. The pipeline opens the search result. It could,
           for example, open an item in a new tab in the Content Editor, open a file from the file system in a window, or open an external
           URL in a window.
           Arguments: (string) Datasource, (string) IndexableId, (CommandContext) Command
      -->
      <uiLaunchResult>
        <processor desc="sitecore"   type="Sitecore.Buckets.Pipelines.UI.LaunchResultPipeline.LaunchSitecoreResult, Sitecore.Buckets"/>
      </uiLaunchResult>   
      
      <!-- ITEM BUCKET PIPELINES
           Pipeline for creating an item bucket from the UI. 
      -->
      <uiBucketItems>
        <processor mode="on" type="Sitecore.Buckets.Pipelines.UI.BucketItems.CreateBucketProcessor, Sitecore.Buckets"  method="CreateBucket"/>
      </uiBucketItems>
      <uiUnBucketItems>
        <processor mode="on" type="Sitecore.Buckets.Pipelines.UI.UnBucket.UnbucketProcessor, Sitecore.Buckets"  method="Unbucket"/>
      </uiUnBucketItems>
      <uiCloneItems>
        <processor mode="on" patch:before="*[@method='Execute']" type="Sitecore.Buckets.Pipelines.UI.BucketItemClone, Sitecore.Buckets"  method="Execute"/>
      </uiCloneItems>
      <uiDragItemTo>
        <processor mode="on" patch:before="*[@method='Execute']" type="Sitecore.Buckets.Pipelines.UI.ItemDrag, Sitecore.Buckets"         method="Execute"/>
      </uiDragItemTo>
      <uiCopyItems>
        <processor mode="on" patch:before="*[@method='Execute']" type="Sitecore.Buckets.Pipelines.UI.ItemCopy, Sitecore.Buckets"         method="Execute"/>
      </uiCopyItems>
      <uiMoveItems>
        <processor mode="on" patch:instead="*[@method='Execute' and @type='Sitecore.Shell.Framework.Pipelines.MoveItems,Sitecore.Kernel']" type="Sitecore.Buckets.Pipelines.UI.ItemMove, Sitecore.Buckets"         method="Execute"/>
      </uiMoveItems>
      <uiDuplicateItem>
        <processor mode="on" patch:before="*[@method='Execute']" type="Sitecore.Buckets.Pipelines.UI.ItemDuplicate, Sitecore.Buckets"    method="Execute"/>
      </uiDuplicateItem>
      <uiDeleteItems>
        <processor mode="on" patch:before="*[@method='Confirm']" type="Sitecore.Buckets.Pipelines.UI.ItemDeleted, Sitecore.Buckets"      method="Execute"/>
      </uiDeleteItems>
    </processors>

    <controlSources>
      <source mode="on" namespace="Sitecore.Buckets.FieldTypes" assembly="Sitecore.Buckets" prefix="contentExtension"/>
      <source mode="on" namespace="Sitecore.Buckets.Controls" assembly="Sitecore.Buckets"/>
    </controlSources>

    <ui>
      <references>
        <reference id="Sitecore.Buckets">/bin/Sitecore.Buckets.dll</reference>
      </references>
    </ui>
    <!-- CONTENT TREE VIEW
         Use this view to patch in behaviour about how items should be rendered in the content tree.
     
         Example: You want a checkbox, similar to the Hidden Items checkbox, that determines if and how an item is
         rendered in the content tree.
    -->
    <dataviews>
      <dataview name="Master" >
        <patch:attribute name="assembly">Sitecore.Buckets</patch:attribute>
        <patch:attribute name="type">Sitecore.Buckets.Forms.BucketDataView</patch:attribute>
      </dataview>
    </dataviews>

    <settings>
      <!-- ITEM BUCKETS ENABLED
           This setting specifies whether or not the Item Buckets feature is enabled.
           Default value: true
      -->
      <setting name="BucketConfiguration.ItemBucketsEnabled" value="true"/>

      <!-- AUTO BUCKET TRIGGER COUNT
           If you enable the AutoBucket events, this setting specifies the maximum number of children that an item can have 
               before it is automatically converted into an item bucket.
      -->
      <setting name="BucketConfiguration.BucketTriggerCount" value="100"/>

      <!-- BUCKET FOLDER TEMPLATE
           If you want to change the template of the folder item that organizes all the hidden bucketable items, 
           you must change this setting to point to the GUID of the new folder item. We recommend that you use the default value.
      -->
      <setting name="BucketConfiguration.BucketTemplateId" value="{ADB6CA4F-03EF-4F47-B9AC-9CE2BA53FF97}" />

      <!-- DISPLAY SECURED ITEMS
           This setting determines what happens to results that are returned when the user does not have read access to them.
           Options include "hide" or "blur".
      -->
      <setting name="BucketConfiguration.SecuredItems" value="hide"/>

      <!-- MAXIMUM POSSIBLE FACETS LOADED
           This setting determines how many items are returned per facet search. If this limit is hit, 0 items are returned for that facet. 
               We recommend that to find a good balance between this number and the number of items that are in your content tree.
      -->
      <setting name="MaxFacets" value="10000000"/>

      <!-- BUCKET FOLDER PATH FORMAT
           This setting determines the folder structure that is created in the content tree. Edit this setting to change the folder structure.
           The format currently supports date formatting, names, for example, "Content Bucket" or blank. Blank creates a dummy called "Repository".
           USAGE: This is a universal resolver for every date format. You should only change it if you do not have a multilingual solution.
               
           RESERVED CHARACTERS: Do not use any of characters that are listed in the web.config file  in the "InvalidItemNameCharacters" setting. 
                                    The default list is :?&quot;&lt;&gt;|[]

      -->
      <setting name="BucketConfiguration.BucketFolderPath" value="yyyy\/MM\/dd\/HH\/mm"/>

      <!-- BUCKETING FOLDER PROVIDER
           This class determines the folder that the items are created in. This implements an interface called IDynamicBucketFolder and returns a 
           string that contains the folder path.
      -->
      <setting name="BucketConfiguration.DynamicBucketFolderPath" value="Sitecore.Buckets.Util.BucketFolderPathResolver, Sitecore.Buckets"/>

      <!-- EXCLUDE CONTEXT ITEM IN SEARCH
           This setting includes or excludes the current item from the search results.
      -->
      <setting name="BucketConfiguration.ExcludeContextItemFromResult" value="false"/>

      <!-- FORCE CLIENT LANGUAGE IN SEARCH
           This setting determines whether or not to restrict search results to the language currently used in the Sitecore client when searching 
           through the UI.
           Set to false to include search results in all languages. Set to true to only include search results in the current client language.
           Default value: false
      -->

      <setting name="BucketConfiguration.ForceClientLanguageInSearch" value="false"/>

      <!-- BUCKET CONFIGURATION - MAX DEGREE OF PARALLELISM
           This setting allows you to limit the number of threads used for creating, syncing, or reverting an item bucket.
           If the value is set to -1, there is no limit on the number of concurrently running operations.
           Default value: 2 
      -->
      <setting name="BucketConfiguration.MaxDegreeOfParallelism" value="2"/>

      <!-- INCLUDE STANDARD VALUES IN SEARCH AND REPLACE
           This setting determines whether or not the Template Standard Values are updated by the Search and Replace command.
      -->
      <setting name="BucketConfiguration.SearchAndReplaceIncludeStandardValues" value="true"/>

      <!-- BUCKET CONFIGURATION - SEARCH UI SERVICE PROVIDER
           This class allows you to override/substitute the web service that the search UI calls to retrieve information and perform various operations.
      -->
      <setting name="BucketConfiguration.SearchUIServiceProvider" value="Sitecore.Buckets.Search.Service.BucketClientService, Sitecore.Buckets"/>

      <!-- SUPPORTED SEARCH AND REPLACE FIELD TYPES
           Field types that are parsed and can be replaced by the Search and Replace command.
      -->
      <setting name="BucketConfiguration.SupportedSearchAndReplaceFields" value="rich text|single-line text|multi-line text|general link with search|general link|text" />

      <!-- ENABLE BUCKET SPECIFIC DEBUG IN LOGS
           If enabled, every query is written to the log file. Only have this turned on while you are debugging, switch it off in your production environment.
      -->
      <setting name="BucketConfiguration.EnableBucketDebug" value="true"/>

      <!-- DEFAULT RESULTS PER PAGE IN UI
           The number of results that are displayed per page in the search results by default.  -->
      <setting name="BucketConfiguration.DefaultNumberOfResultsPerPage" value="20"/>

      <!-- RESOLVE FACET VALUE TO FRIENDLY NAME
           If you are storing a field in the index that is being faceted on, it may be stored as an ID. This Setting
           when set to true, will try and resolve this to the friendly item name instead. 
           
           USAGE: In an environment with huge amounts of items (e.g. 1 Million), this will not scale properly.           
      -->
      <setting name="BucketConfiguration.ResolveFacetValueToFriendlyName" value="false"/>

      <!-- OPTIONAL IMPROVED BUCKETING PERFORMANCE
           Patching this setting from default 'true' to improve unbucketing performance -->
      <!--<setting name="FastQueryDescendantsDisabled">
         <patch:attribute name="value">true</patch:attribute>
      </setting>-->

      <!-- Enable FillDB Page
           This setting enables or disables the /sitecore/admin/filldb.aspx page. Always disable this page in production environments.
           You can use the FillDB page to insert large amounts of test content into Sitecore to test facets, new search types, as well as
           the performance and scalability of indexing and search in your solution.
           Default value: false
      -->
      <setting name="EnableFillDB" value="false" />
    </settings>
  </sitecore>
</configuration>