﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose
________

This include file enables and configures the Federated Experience Manager feature; a mechanism for tracking 
end user's activity on non-Sitecore sites in Sitecore's analytics system, and optionally delivering 
Sitecore-created content into external sites.

Description
___________

Once enabled, configuration can be created to enable tracking on one or more non-Sitecore sites via the 
Federated Experience Manager application on the launch pad, or under the Federated Experience Manager
node in the marketing centre.  Additionally a Javascript script tag must be placed on the pages on the
non-Sitecore sites that are to be tracked.

To disable this feature, change its extension to '.disabled'.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <settings>
      <!-- FXM BASE HOST DOMAIN
        When the Sitecore site and external sites are hosted on a common base domain but under different
        sub domains (and the sub domain isn't 'www'), then this setting should be set to the common base domain.  
        The following table best explains this:
        
        Site A                  Site B                    Base Host Domain
        shop.mysite.co.uk       jobs.mysite.co.uk         mysite.co.uk
        shop.mysite.co.uk       www.mysite.co.uk              -             (www is a special case and accounted for)
        www.mysite.co.uk        mysite.co.uk                  -
        shop.mysite.co.uk       mysite.co.uk                  -
        shop.mysite.co.uk       jobs.mysite.com               -
        mysite.co.uk            mysite.com                    -
      -->
      <setting name="FXM.BaseHostDomain" value="" />
      
      <!-- FXM P3P HEADER
        A 'P3P' HTTP header is required to enable cross domain requests to be permitted by all versions of Internet Explorer.  
        All FXM service responses include a P3P header, with a value that can be controlled via this configuration setting.  
        An appropriate value is provided by default, though there are potentially legal consequences attached to P3P values, 
        so Sitecore advises customers to carefully consider the P3P value for their business.
      -->
      <setting name="FXM.P3PHeader" value="CURa ADMa DEVa TAIi PSAi PSDi IVAi IVDi CONi HISa TELi OUR IND DSP CAO COR" />
      
      <!-- FXM PROTOCOL
        The protocol used when making the tracking calls by the FXM client Javascript back to the Sitecore instance.  This defaults
        to using the same protocol as was used to request the current page in the browser.  Alternative values are either 'http' or 'https'.
        Note that when tracking secure pages ('https'), this setting will require the Sitecore instance to also be available
        on https.  Changing this value to 'http', might yield browser warning messages to the user resembling "there are insecure
        elements on this page".
      -->
      <setting name="FXM.Protocol" value="//" />

      <!-- FXM HOSTNAME
        The host name used when making the tracking calls by the FXM client Javascript back to the Sitecore instance.  This defaults
        to using the same host name as was used to request the current page in the browser. This should be set to the DNS name assigned
        to the Content Delivery Server.
      -->
      <setting name="FXM.Hostname" value="" />
      
      <!-- FXM SHARE SESSIONS WHEN POSSIBLE
        This provides the option to record visits across multiple sites made in the same browser session, to be 
        recorded in the same analytics interaction.  This behavior is limited by the same restrictions as tracking 
        contacts across sites, so on Safari and IE 8 & 9 page visits cannot be recorded in the same interaction.  
        The analytics data model does not officially support visits to multiple sites within the same interaction, 
        so turning this setting on is not recommended.
      -->
      <setting name="FXM.ShareSessionsWhenPossible" value="false" />
    </settings>

    <siteManager>
      <providers>
        <add name="sitecore" type="Sitecore.Sites.SitecoreSiteProvider, Sitecore.Kernel" checkSecurity="false">
          <providers hint="raw:AddProviderReference">
            <reference patch:after="reference[@name='config']" name="fxm" />
          </providers>
        </add>
        <!-- FXM SiteProvider to ensure all components in Sitecore are given external sites in the list of currently configured sites, 
             used mainly for analytics reporting.-->
        <add patch:after="add[@name='config']" name="fxm" type="Sitecore.FXM.Sites.FxmSiteProvider, Sitecore.FXM" checkSecurity="false" />
      </providers>
    </siteManager>

    <linkManager>
      <providers>
        <add patch:after="add[@name='sitecore']" name="fxm" type="Sitecore.Links.LinkProvider, Sitecore.Kernel" alwaysIncludeServerUrl="true" />
      </providers>
    </linkManager>

    <!-- Allow the FXM tracking beacon to be called by non-local clients (i.e. the end user's we are tracking) -->
    <api>
      <services>
        <configuration type="Sitecore.Services.Infrastructure.Configuration.ServicesConfiguration, Sitecore.Services.Infrastructure">
          <allowedControllers hint="list:AddController">
            <allowedController desc="BeaconController">Sitecore.FXM.Service.Controllers.BeaconController, Sitecore.FXM.Service</allowedController>
            <!-- Deploy to CM server only -->
            <allowedController desc="DomainMatcherController">Sitecore.FXM.Service.Controllers.DomainMatcherController, Sitecore.FXM.Service</allowedController>
            <allowedController desc="ElementMatcherController">Sitecore.FXM.Service.Controllers.ElementMatcherController, Sitecore.FXM.Service</allowedController>
            <allowedController desc="ExperienceEditorComponentController">Sitecore.FXM.Service.Controllers.ExperienceEditorComponentController, Sitecore.FXM.Service</allowedController>
            <allowedController desc="ItemActionController">Sitecore.FXM.Service.Controllers.ItemActionController, Sitecore.FXM.Service</allowedController>
            <allowedController desc="PageMatcherController">Sitecore.FXM.Service.Controllers.PageMatcherController, Sitecore.FXM.Service</allowedController>
            <!-- -->
          </allowedControllers>
        </configuration>
      </services>
    </api>

    <processors>
      <saveUI>
        <processor type="Sitecore.FXM.Pipelines.SaveUI.RemoveDeletedClientActionsProcessor, Sitecore.FXM"
                          patch:before="processor[@type='Sitecore.FXM.Pipelines.SaveUI.RemoveDeletedElementReplacersProcessor, Sitecore.FXM']" />
                   
        <processor type="Sitecore.FXM.Pipelines.SaveUI.RemoveDeletedElementReplacersProcessor, Sitecore.FXM"
                          patch:before="processor[@type='Sitecore.FXM.Pipelines.SaveUI.AddElementReplacersProcessor, Sitecore.FXM']" />

        <processor type="Sitecore.FXM.Pipelines.SaveUI.AddElementReplacersProcessor, Sitecore.FXM"
                    patch:before="processor[@type='Sitecore.Pipelines.Save.ConvertLayoutField, Sitecore.Kernel']" />
      </saveUI>
    </processors>
      
    <pipelines>
      
      <!-- **** Kernel integration pipeline customizations **** -->
      
      <initialize>
        <!-- Maps a specific route for the Beacon Service to allow session state to be enabled as the DMS relies upon the existence of a session. -->
        <processor type="Sitecore.FXM.Service.Pipelines.EnableBeaconServiceSessionStateProcessor, Sitecore.FXM.Service"
                   patch:before="processor[@type='Sitecore.Services.Infrastructure.Sitecore.Pipelines.ServicesWebApiInitializer, Sitecore.Services.Infrastructure.Sitecore']" />
      </initialize>

      <getPlaceholderRenderings>
        <!-- Ensures that the 'HasPlaceholderSettings' flag is enabled for FXM Template types. -->
        <processor type="Sitecore.FXM.Pipelines.Rendering.EnableExternalPagePlaceholdersProcessor, Sitecore.FXM"
                   patch:before="processor[@type='Sitecore.Pipelines.GetPlaceholderRenderings.GetPredefinedRenderings, Sitecore.Kernel']" />
      </getPlaceholderRenderings>

      <getItemPersonalizationVisibility>
        <!-- Ensures that the 'edit profiles' dialog in the Marketing Centre is available for all FXM configuration items. -->
        <processor type="Sitecore.FXM.Tracking.MatcherPersonalizationVisibilityProcessor, Sitecore.FXM"
                   patch:after="processor[@type='Sitecore.Pipelines.GetItemPersonalizationVisibility.CheckSectionAvailability, Sitecore.Kernel']"/>
      </getItemPersonalizationVisibility>
      
      <getChromeData>
        <!-- Add to chrome data pipeline -->
        <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.GetChromeData.GetSelectorsChromeData, Sitecore.FXM.Client"/>
        <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.GetChromeData.GetPlaceholdersChromeData, Sitecore.FXM.Client"/>
        <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.GetChromeData.GetClientActionsChromeData, Sitecore.FXM.Client"/>
      </getChromeData>
      
      <executePageEditorAction>
        <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.ExecutePageEditorAction.TryGetFxmMvcWebControl, Sitecore.FXM.Client"
                   patch:after="processor[@type='Sitecore.Pipelines.ExecutePageEditorAction.InitActionHandler, Sitecore.ExperienceEditor']" />
      </executePageEditorAction>

      <mvc.renderPlaceholder>
        <processor type="Sitecore.FXM.Pipelines.Mvc.RenderPlaceholder.EnterFxmPlaceholderContext, Sitecore.FXM"
                   patch:before="processor[@type='Sitecore.Mvc.Pipelines.Response.RenderPlaceholder.EnterPlaceholderContext, Sitecore.Mvc']" />
      </mvc.renderPlaceholder>

      <group groupName="FXM" name="FXM">
        <pipelines>
          
          <!-- **** Tracking Request Pipelines **** -->
          
          <tracking.matchpages>
            <!-- Run when a page visit occurs on an tracked external site.  Finds all page filters that match the visit, and processes
                 any sub-page level matchers belonging to them.  Records any analytics events defined for these items.-->
            <processor type="Sitecore.FXM.Pipelines.Tracking.MatchPages.MatchPagesRuleProcessor, Sitecore.FXM"/>
            <processor type="Sitecore.FXM.Pipelines.Tracking.MatchPages.GlobalElementMatchersProcessor, Sitecore.FXM"/>
            <processor type="Sitecore.FXM.Pipelines.Tracking.MatchPages.ElementMatchersProcessor, Sitecore.FXM"/>
            <processor type="Sitecore.FXM.Pipelines.Tracking.MatchPages.RegisterEventsForMatchedPagesProcessor, Sitecore.FXM"/>
          </tracking.matchpages>

          <tracking.validate>
            <!-- Run on each tracking request to validate if the request is for a configured site. -->
            <processor type="Sitecore.FXM.Pipelines.Tracking.ValidateRequest.ValidateByWebEditProcessor, Sitecore.FXM"/>
            <processor type="Sitecore.FXM.Pipelines.Tracking.ValidateRequest.ValidateByDomainRuleProcessor, Sitecore.FXM"/>
          </tracking.validate>

          <tracking.trackpagevisit>
            <!-- Run on each page visit for a tracked external site to record the page visit in the DMS in the current session. -->
            <processor type="Sitecore.FXM.Pipelines.Tracking.BeforeEvent.InitializeTrackingCookieProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.BeforeEvent.InitializeExternalTrackingProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.InitializeContextSiteProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.TrackPageVisitProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.FixRequestUrlProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.SwitchUriForReferrerProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.SetInteractionLanguageProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TrackPageVisit.SaveDurationProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.AfterEvent.CleanupAnalyticsCookieInResponseProcessor, Sitecore.FXM" />
          </tracking.trackpagevisit>

          <tracking.triggerpageevent>
            <!-- Run when a page event is triggered by the client javascript to record the event in the DMS in the current session. -->
            <processor type="Sitecore.FXM.Pipelines.Tracking.BeforeEvent.InitializeTrackingCookieProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.BeforeEvent.InitializeExternalTrackingProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.BeforeEvent.EnsureCurrentPageIsTrackedProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.TriggerPageEvent.RunRegisterPageEventProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.AfterEvent.CleanupAnalyticsCookieInResponseProcessor, Sitecore.FXM" />
          </tracking.triggerpageevent>

          <tracking.registerpageevent>
            <!-- Performs the registration of a page event in the DMS. -->
            <processor type="Sitecore.FXM.Pipelines.Tracking.RegisterPageEvent.EnsureEventItemExistsProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.Tracking.RegisterPageEvent.RegisterPageEventProcessor, Sitecore.FXM" />
          </tracking.registerpageevent>

          <tracking.getitemrenderedcontent>
            <!-- Run when Sitecore content is requested by the FXM client javascript to be delivered and inserted into an external site. -->
            <processor type="Sitecore.FXM.Pipelines.Rendering.ItemContentRenderingProcessor, Sitecore.FXM" />
          </tracking.getitemrenderedcontent>

          <tracking.choosesessionidmanager>
            <!-- Run on each request to determine if which session Id manager should be used for the current request. -->
            <processor type="Sitecore.FXM.Pipelines.ChooseSessionIdManager.FXMSessionIdManagerProcessor, Sitecore.FXM" />
            <processor type="Sitecore.FXM.Pipelines.ChooseSessionIdManager.DefaultAspNetSessionIdManagerProcessor, Sitecore.FXM" />
          </tracking.choosesessionidmanager>

          <content.resolveItemUrl>
            <!-- The customisation of the LinkProvider; calculates the absolute Url for an item when displayed on an external site. -->
            <processor type="Sitecore.FXM.Pipelines.Item.ItemExternalSiteUrlProcessor, Sitecore.FXM" />
          </content.resolveItemUrl>

          <!-- **** External Site Proxy Pipelines - used in the Experience Editor UI **** -->
          
          <externalsiteproxy.buildrequest>
            <!-- Run on each request to the proxy, to determine a function that is later executed to perform the proxied request. -->
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.BuildRequest.DefaultBuildRequestProcessor, Sitecore.FXM" />
          </externalsiteproxy.buildrequest>

          <externalsiteproxy.processresponse>
            <!-- Run on receiving the response from the proxied request, to allow for the content to be altered in any way. -->
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessResponse.ProcessHtmlResponseProcessor, Sitecore.FXM" />
          </externalsiteproxy.processresponse>

          <externalsiteproxy.processhtmlresponse>
            <!-- Run on receiving HTML response content from a proxied request, to allow for modification of the document before returning in the proxy response. -->
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessHtmlResponse.HtmlAbsoluteTagAttributeProcessor, Sitecore.FXM">
              <TagName>*</TagName>
              <TagAttribute>src</TagAttribute>
            </processor>
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessHtmlResponse.HtmlAbsoluteTagAttributeProcessor, Sitecore.FXM">
              <TagName>link</TagName>
              <TagAttribute>href</TagAttribute>
            </processor>
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessHtmlResponse.HtmlProxiedTagAttributeProcessor, Sitecore.FXM">
              <TagName>a</TagName>
              <TagAttribute>href</TagAttribute>
            </processor>
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessHtmlResponse.HtmlProxiedTagAttributeProcessor, Sitecore.FXM">
              <TagName>area</TagName>
              <TagAttribute>href</TagAttribute>
            </processor>
            <processor type="Sitecore.FXM.Pipelines.ExternalSiteProxy.ProcessHtmlResponse.HtmlProxiedTagAttributeProcessor, Sitecore.FXM">
              <TagName>*</TagName>
              <TagAttribute>action</TagAttribute>
            </processor>
          </externalsiteproxy.processhtmlresponse>

          <!-- **** Experience Editor Integration **** -->
          <content.experienceeditor>
            <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.ExternalPage.GetExternalPageContentProcessor, Sitecore.FXM.Client" />
            <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.ExternalPage.UpdateBeaconScriptPathProcessor, Sitecore.FXM.Client" />
            <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.ExternalPage.InjectControlsProcessor, Sitecore.FXM.Client" />
            <processor type="Sitecore.FXM.Client.Pipelines.ExperienceEditor.ExternalPage.AddPlaceholderData, Sitecore.FXM.Client" />
          </content.experienceeditor>

        </pipelines>
      </group>
      
    </pipelines>
  </sitecore>
</configuration>
