﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include configuration file contains declarations of server requests for Sitecore Experience Editor. This configuration file goes together with the Sitecore.ExperienceEditor.config file.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <customHandlers>
      <handler trigger="/-/speak/request/v1/expeditor/" handler="sitecore_expeditor_speak_request.ashx" />
    </customHandlers>

    <sitecore.experienceeditor.speak.requests>
      <!-- Delete Start -->
      <request name="ExperienceEditor.Delete.CanDelete" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.CanDeleteRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Delete.CheckPermissions" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.CheckPermissionsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Delete.Confirm" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.ConfirmDeleteRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Delete.CheckCloneLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.CheckCloneLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Delete.CheckLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.CheckLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Delete.UncloneItems" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.UncloneItemsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Delete.ExecuteRequest" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Delete.ExecuteRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Delete End -->

      <!-- Rename Start -->
      <request name="ExperienceEditor.Rename.CanRename" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.CanRenameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Rename.CheckPermissions" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.CheckPermissionsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Rename.CheckShadows" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.CheckShadowsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Rename.GetCurrentName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.GetNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Rename.ValidateNewName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.ValidateNewNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Rename.CheckLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.CheckLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Rename" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.RenameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Rename.RepairLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Rename.RepairLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Rename End -->

      <!-- Close Start -->
      <request name="ExperienceEditor.Close" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Close.CloseRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Close End -->

      <!-- Logout Start -->
      <request name="ExperienceEditor.Logout.ClearCache" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Logout.ClearCacheRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Logout.ClearSession" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Logout.ClearSessionRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Logout.RemoveTicket" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Logout.RemoveTicketRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Logout" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Logout.LogoutRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Logout End -->


      <!-- Display Name Start -->
      <request name="ExperienceEditor.CanChangeDisplayName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.DisplayName.CanChangeDisplayNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.DisplayName.GetCurrentDisplayName"  type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.DisplayName.GetCurrentDisplayNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.ChangeDisplayName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.DisplayName.ChangeDisplayNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Display Name End -->

      <!-- Move Start -->
      <request name="ExperienceEditor.Move.CheckPermissions" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.CheckPermissionsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Move.CanMove" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.CanMoveItemRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Move.IsSameDatabases" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.IsSameDatabasesRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Move.HasShadows" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.CheckShadowsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Move.CheckLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.CheckLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Move" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.MoveRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Move.RepairLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Move.RepairLinksRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Move End -->

      <!-- Save Start -->
      <request name="ExperienceEditor.Save.CheckItemLock" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CheckItemLock, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.CheckRevision" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CheckRevision, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.Validators" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.Validators, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.ValidateFields" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.ValidateFields, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.HasWritePermission" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.HasWritePermission, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.CheckBaseTemplateFieldChange" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CheckBaseTemplateFieldChange, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.CheckTemplateFieldChange" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CheckTemplateFieldChange, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.CheckLinks" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CheckLinks, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Save.CallServerSavePipeline" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveItem.CallServerSavePipeline, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Save End -->

      <!-- LockItem Start -->
      <request name="ExperienceEditor.LockItem.CanToggleLock" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LockItem.CanToggleLockRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.LockItem.IsReadOnly" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LockItem.IsReadOnlyRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.LockItem.IsLockedByAnother" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LockItem.IsLockedByAnotherRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.LockItem.IsLocked" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LockItem.IsLocked, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.LockItem" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LockItem.ToggleLockRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- LockItem End -->

      <!-- MyItems Start -->
      <request name="ExperienceEditor.MyItems.Count" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.MyItems.MyItemsCountRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- MyItems End -->

      <!-- Publish Start -->
      <request name="ExperienceEditor.Publish.CanPublish" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Publish.CanPublishRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Publish.CheckWorkflow" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Publish.CheckWorkflowRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Publish End -->
      
      <!-- Publish Restrictions Start -->
      <request name="ExperienceEditor.PublishRestrictions.CanChange" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.PublishRestrictions.CanChangePublishRestrictions, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Publish Restrictions End -->

      <!-- Screenshot Start -->
      <request name="ExperienceEditor.GetScreenShotUrl" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Screenshots.ScreenshotsRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Screenshot End -->

      <!-- Insert Start -->
      <request name="ExperienceEditor.Insert.CanInsert" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Insert.CanInsertRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Insert.ValidateNewName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Insert.ValidateNewNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Insert" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Insert.InsertRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Insert End -->


      <!-- Langauge Start -->
      <request name="ExperienceEditor.Language.ChangeLanguage" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ChangeLanguage.ChangeLanguageRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Language.IconLabel" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ChangeLanguage.LanguageIconLabelRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Language.GetLanguageItems" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ChangeLanguage.GetLanguages, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Langauge End -->

      <!-- Device Start -->
      <request name="ExperienceEditor.Device.SelectSevice" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectDevice.SelectDeviceRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Device.IconLabel" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectDevice.DeviceIconLabelRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.Device.HasDeviceLayout" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectDevice.HasDeviceLayout, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Device End -->

      <!-- ToggleRegistryKey Start -->
      <request name="ExperienceEditor.ToggleRegistryKey.Get" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleRegistryKey.GetRegistryKeyRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.ToggleRegistryKey.Toggle" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleRegistryKey.ToggleRegistryKeyRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- ToggleRegistryKey End -->

      <!-- Preview Date Start -->
      <request name="ExperienceEditor.PreviewDate.AddDays"  type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.PreviewDate.AddDaysRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.PreviewDate.GetPreviewDateUrl"  type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.PreviewDate.GetPreviewDateUrlRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <request name="ExperienceEditor.PreviewDate.SetDateValue"  type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.PreviewDate.SetDateValueRequest, Sitecore.ExperienceEditor.Speak.Ribbon"/>
      <!-- Preview Date End -->

      <!-- ProfileCards Start -->
      <request name="ExperienceEditor.ProfileCardsDialog.Url" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.FormDialogUrl.ProfileCardsDialog, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.SelectProfileCardDialog.Url" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.FormDialogUrl.SelectProfileCardDialog, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- ProfileCards End -->

      <!-- Add component Start -->
      <request name="ExperienceEditor.CanAddComponent" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.AddRendering.CanAddRendering, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Add component End -->

      <!-- Select mode Start -->
      <request name="ExperienceEditor.Mode.SelectModeRequest" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectMode.SelectModeRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Mode.ModeIconLabel" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectMode.ModeIconLabelRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Mode.CanSelectMode" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SelectMode.CanSelectMode, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Select mode End -->

      <!-- Set edit mode Start -->
      <request name="ExperienceEditor.EditMode.CanEdit" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.EditMode.CanEditRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.EditMode.SelectEdit" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.EditMode.SelectEditRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Set edit mode End -->

      <!-- Layout Details Start -->
      <request name="ExperienceEditor.LayoutDetails.CanEdit" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LayoutDetails.CanEditLayoutDetailsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.LayoutDetails.SetValue" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LayoutDetails.SetLayoutDetailsRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Layout Details End -->

      <!-- Reset Layout Start -->
      <request name="ExperienceEditor.ResetLayout.IsEnabled" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ResetLayout.IsEnabled, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.ResetLayout.Execute" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ResetLayout.Execute, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Reset Layout End -->

      <!-- Enable Designing Start -->
      <request name="ExperienceEditor.EnableDesigning.CanDesign" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.EnableDesigning.CanDesign, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Enable Designing End -->

      <!-- Enable Editing Start -->
      <request name="ExperienceEditor.EnableEditing.CanEdit" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.EnableEditing.CanEdit, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Enable Editing End -->

      <!-- Enable Control Bar Start -->
      <request name="ExperienceEditor.ControlBar.CanEnable" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ControlBar.CanEnable, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Enable Control Bar End -->

      <!-- Breadcrumb Start -->
      <request name="ExperienceEditor.Breadcrumb.GetStructure" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Breadcrumb.GetBreadcrumbStructure, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Breadcrumb.GetChildItems" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Breadcrumb.GetChildItems, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Breadcrumb.EditItem" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Breadcrumb.EditItem, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Breadcrumb End -->

      <!-- Layout Presets Start -->
      <request name="ExperienceEditor.LayoutPresets.CanOpen" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LayoutPresets.CanOpenRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.LayoutPresets.GetDialogUrl" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LayoutPresets.GetDialogUrlRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.LayoutPresets.Execute" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LayoutPresets.ExecuteRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Layout Presets End -->

      <!-- Tracking Field Start -->
      <request name="ExperienceEditor.OpenTrackingField.CanOpen" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.OpenTrackingField.CanOpenRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.OpenTrackingField.GetDialogUrl" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.OpenTrackingField.GetDialogUrlRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.OpenTrackingField.Execute" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.OpenTrackingField.ExecuteRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Tracking Field End -->

      <!-- Search Start -->
      <request name="ExperienceEditor.Search.GetItemUrlRequest" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Search.GetItemUrlRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Search End -->

      <!-- Optimization Start -->
      <request name="ExperienceEditor.Analytics.MultiComponentTestingCountRequest" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Analytics.MultiComponentTestingCountRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Optimization End -->

      <!-- Common Start -->
      <request name="ExperienceEditor.Item.Notifications" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Common.GetItemNotifications, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Item.HasPresentation" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Common.CheckItemHasPresentation, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.Item.GetUri" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.Common.GetItemUri, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Common End -->
      
      <!-- LargeDropDownButton Start -->
      <request name="ExperienceEditor.LagreDropDownItem.GetChildItems" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.LargeDropDownButton.LargeDropDownButtonChildItems, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- LargeDropDownButton End -->
      
      <!-- Set Aliases Start -->
      <request name="ExperienceEditor.SetAliases.CanSetAliases" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SetAliases.CanSetAliases, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Set Aliases End -->
      
      <!-- Toggle Debug Requests Start -->
      <request name="ExperienceEditor.ToggleDebugRequests.CanToggleDebug" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleDebugRequests.CanToggleDebugRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.ToggleDebugRequests.ExecuteToggleProfile" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleDebugRequests.ExecuteToggleProfileRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.ToggleDebugRequests.ExecuteToggleTrace" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleDebugRequests.ExecuteToggleTraceRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.ToggleDebugRequests.ExecuteToggleBorders" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleDebugRequests.ExecuteToggleBordersRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.ToggleDebugRequests.ExecuteToggleInformation" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.ToggleDebugRequests.ExecuteToggleInformationRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Toggle Debug Requests End -->

      <!-- Download Debug Requests Start -->
      <request name="ExperienceEditor.DownloadDebugRequests.ExecuteDownloadProfile" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.DownloadDebugRequests.ExecuteDownloadProfileRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.DownloadDebugRequests.ExecuteDownloadTrace" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.DownloadDebugRequests.ExecuteDownloadTraceRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Download Debug Requests End -->
	   
      <!-- Save Debug Requests Start -->
      <request name="ExperienceEditor.SaveDebugRequests.GetProfileName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveDebugRequests.GetProfileNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.SaveDebugRequests.GetTraceName" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveDebugRequests.GetTraceNameRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.SaveDebugRequests.SaveProfile" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveDebugRequests.SaveProfileRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <request name="ExperienceEditor.SaveDebugRequests.SaveTrace" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.SaveDebugRequests.SaveTraceRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Save Debug Requests End -->

      <!-- ASP.NET Trace Start -->
      <request name="ExperienceEditor.AspNetTrace.CanOpen" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.AspNetTrace.CanOpenRequest, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- ASP.NET Trace End -->

      <!-- Workbox Start -->
      <request name="ExperienceEditor.Workbox.GetDialogTitle" type="Sitecore.ExperienceEditor.Speak.Ribbon.Requests.WorkBox.DialogTitle, Sitecore.ExperienceEditor.Speak.Ribbon" />
      <!-- Workbox End-->
      
    </sitecore.experienceeditor.speak.requests>
  </sitecore>
</configuration>




