﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures Marketing platfrom definition management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
    </pipelines>
    <marketingDefinitions>
      <asset>
        <definitionManager>
          <param desc="repository">
            <patch:attribute name="ref">marketingDefinitions/asset/repositories/item</patch:attribute>
          </param>
        </definitionManager>

        <repositories>
          <item type="Sitecore.Marketing.Definitions.MarketingAssets.Data.ItemDb.MarketingAssetDefinitionItemRepository, Sitecore.Marketing.Definitions.MarketingAssets.Repositories" singleInstance="true">
            <param desc="databaseName">master</param>
            <param desc="indexName">sitecore_marketing_asset_index_master</param>
          </item>
          <rdb type="Sitecore.Marketing.Definitions.MarketingAssets.Data.Rdb.MarketingAssetDefinitionRdbRepository, Sitecore.Marketing.Definitions.MarketingAssets.Repositories" singleInstance="true">
            <param desc="connectionStringName">reporting</param>
            <param desc="cache" type="Sitecore.Marketing.Definitions.Repository.RdbCache.DefinitionCache`1[[Sitecore.Marketing.Definitions.MarketingAssets.Data.MarketingAssetDefinitionRecord, Sitecore.Marketing]], Sitecore.Marketing">
              <param desc="name">marketing.rdb.asset</param>
              <param desc="maxSize">20MB</param>
            </param>
          </rdb>
        </repositories>
      </asset>
    </marketingDefinitions>

  </sitecore>
</configuration>