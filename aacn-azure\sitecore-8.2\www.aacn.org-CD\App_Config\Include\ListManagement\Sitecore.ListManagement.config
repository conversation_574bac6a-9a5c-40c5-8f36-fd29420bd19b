﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- SETTINGS -->
    <settings>
      <!--  CONTACT LIST INDEX NAME
            The name of the search index where contact lists are stored.
      -->
      <setting name="ListManagement.ContactListIndexName" value="sitecore_list_index" />

      <!--  LIST TEMPLATE ID
            The identifier of the template for the contact list items.
      -->
      <setting name="ListManagement.ContactListTemplateId" value="{8288D4EA-6576-486F-AAEC-4680329A2D5C}" />

      <!--  CONTACTS INDEX NAME
            The name of the search index where contacts are stored.
      -->
      <setting name="ListManagement.ContactsIndexName" value="sitecore_analytics_index" />

      <!--  DATABASE
            The name of the Database where content is stored.
      -->
      <setting name="ListManagement.Database" value="master" />

      <!--  LIST FOLDER TEMPLATE ID
            The identifier of the template for the sub-folders that could be put
            into the root to create content tree of the contact lists.
      -->
      <setting name="ListManagement.ListFolderTemplateId" value="{C4D09683-5EB3-4D8E-BF9E-13CB41C197B5}" />

      <!--  LISTS ROOT ID
            The identifier of the root item to put contact lists into.
      -->
      <setting name="ListManagement.ListsRootId" value="{BC799B34-8423-48AC-A2FE-D128E6300659}" />

      <!--  SEGMENTED LIST TEMPLATE ID
            The identifier of the template for the segmented list items.
      -->
      <setting name="ListManagement.SegmentedListTemplateId" value="{C0CAF698-8A42-4B66-9EAF-7D442B46F722}" />
    </settings>

    <!--  LIST MANAGER
          Performs CRUD operations on list and list associations.
    -->
    <contactListManager type="Sitecore.ListManagement.ContentSearch.ContactListManager, Sitecore.ListManagement.ContentSearch" singleInstance="true">
      <param desc="store" ref="contactListStore" />
    </contactListManager>

    <!--  SEGMENTED LIST MANAGER
    -->
    <segmentedListManager type="Sitecore.ListManagement.ContentSearch.SegmentedListManager, Sitecore.ListManagement.ContentSearch" singleInstance="true">
      <param desc="store" ref="segmentedListStore" />
    </segmentedListManager>

    <!--  CONTACT LIST STORE
          Provides set of operations on contact lists.
    -->
    <contactListStore type="Sitecore.ListManagement.ContentSearch.ContactListStore, Sitecore.ListManagement.ContentSearch" singleInstance="true" />

    <!--  LOCK REPOSITORY
          Provides set of operations with locks.
    -->
    <lockRepository type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.IdTableLockRepository, Sitecore.ListManagement.ContentSearch" singleInstance="true">
      <param name="prefix">Lock</param>
      <param name="exclusive">true</param>
    </lockRepository>

    <!--  USE REPOSITORY
          Provides set of operations with use locks.
    -->
    <useRepository type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.IdTableLockRepository, Sitecore.ListManagement.ContentSearch" singleInstance="true">
      <param name="prefix">InUse</param>
      <param name="exclusive">false</param>
    </useRepository>

    <!--  SEGMENTED LIST STORE
          Provides set of operations on segmented lists.
    -->
    <segmentedListStore type="Sitecore.ListManagement.ContentSearch.SegmentedListStore, Sitecore.ListManagement.ContentSearch" singleInstance="true" />

    <!--  PROCESS CONTACTS DATA PROVIDER
          Performs the communication with current data engine when processing contacts.
    -->
    <processContactsDataProvider type="Sitecore.ListManagement.Analytics.Data.ProcessContactsDataProvider, Sitecore.ListManagement.Analytics">
      <param ref="/sitecore/contactRepository" />
      <param ref="/sitecore/model/entities/contact/template" />
      <param name="operationManager" type="Sitecore.Analytics.Data.Bulk.Contact.ContactBulkUpdateManager, Sitecore.Analytics" />
      <param name="setManager" type="Sitecore.Analytics.Data.Bulk.Contact.KnownContactSetManager, Sitecore.Analytics" />
    </processContactsDataProvider>

    <!--  CSV READER
    -->
    <csvReader type="Sitecore.ListManagement.ContentSearch.Parsers.CsvReader, Sitecore.ListManagement.ContentSearch" singleInstance="true" />

    <!-- RULE ENGINE
    -->
    <ruleEngine type="Sitecore.ListManagement.ContentSearch.IndexedContactRuleEngine, Sitecore.ListManagement.ContentSearch">
      <param desc="ruleFactory" type="Sitecore.ContentSearch.Rules.QueryableRuleFactory, Sitecore.ContentSearch" />
      <param desc="ruleDatabaseName">master</param>
    </ruleEngine>

    <!-- PIPELINES -->
    <pipelines>

      <!-- ARE LIST SOURCES CHANGED
          The pipeline is responsible for checking whether list sources changed.
      -->
      <listManagement.areListSourcesChanged>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.AreListSourcesChanged.AreListSourcesChanged, Sitecore.ListManagement.ContentSearch"/>
      </listManagement.areListSourcesChanged>

      <!--  ASSOCIATE CONTACTS
            The pipeline is responsible for associating a set of contacts with a contact list.
      -->
      <listManagement.associateContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.GetContactListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.AssociateContact.AssociateContacts, Sitecore.ListManagement.ContentSearch">
          <ContactProcessingProvider ref="processContactsDataProvider" />
        </processor>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateContactListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateContactListBulkOperationsId, Sitecore.ListManagement.ContentSearch" />
      </listManagement.associateContacts>

      <!-- CHECK SECURITY
           The pipeline which checks security permissions.
      -->
      <listManagement.checkSecurity>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.CheckSecurity, Sitecore.ListManagement.ContentSearch" />
      </listManagement.checkSecurity>

      <!-- CLEAR SOURCES
           The pipeline which clears list sources.
      -->
      <listManagement.clearSources>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RemoveContactAssociations.RemoveContactListSources, Sitecore.ListManagement.ContentSearch" />
      </listManagement.clearSources>

      <!--  CONVERT LIST
            The pipeline is responsible for conversion of the segmented list into contact one.
      -->
      <listManagement.convertList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ConvertList.ConvertSegmentedListToContactList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.convertList>

      <!--  CREATE FOLDER
            The pipeline is responsible for creation of the new folder.
      -->
      <listManagement.createFolder>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.CreateFolder.CreateFolderItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.createFolder>

      <!--  CREATE LIST
            The pipeline is responsible for creating Contact List item from specified ContactList entity.
      -->
      <listManagement.createList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.CreateList.CreateContactList, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateSegmentedListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
      </listManagement.createList>

      <!--  CREATE CONTACT LIST FROM A CSV FILE
         The pipeline is responsible for creating contact list, importing contacts form a csv media item, and association the contacts and the list.
      -->
      <listManagement.createListFromCsv>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.CreateListFromCsv.CreateList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.createListFromCsv>

      <!--  DELETE FOLDER
            The pipeline is responsible for removing of the folder with all subfolders.
      -->
      <listManagement.deleteFolder>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.Folder.GetFolderItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.DeleteFolder.DeleteFolderItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.deleteFolder>

      <!--  DELETE LIST
            The pipeline is responsible for removing of the contact list.
      -->
      <listManagement.deleteList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.DeleteList.DeleteContactList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.deleteList>

      <!--  EXPORT CONTACTS
          The pipeline is responsible for exporting contacts associated with specified list.
      -->
      <listManagement.exportContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ExportContacts.GetContactRows, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ExportContacts.GetContactsStream, Sitecore.ListManagement.ContentSearch" />
      </listManagement.exportContacts>

      <!--  FIND LIST BY ID
            The pipeline is responsible for Contact List retrieval form items by specified id.
      -->
      <listManagement.findListById>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.FindList.FindContactList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.findListById>

      <!--  GET ALL LISTS
            The pipeline is responsible for retrieval of collection of ContactList entities.
      -->
      <listManagement.getAllLists>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetAllLists.GetAllContactLists, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getAllLists>

      <!--  GET ASSOCIATED CONTACTS
            The pipeline is responsible for getting contacts associated with the contact list.
      -->
      <listManagement.getAssociatedContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetAssociatedContacts.GetContactAssociations, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getAssociatedContacts>

      <!--  GET CONTACTS
            The pipeline is responsible for getting contacts associated with the list.
      -->
      <listManagement.getContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetAssociatedContacts.GetContactsDependingOnContactListType, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getContacts>

      <!-- GET LIST SOURCES
          The pipeline is responsible for getting sources of the list.
        -->
      <listManagement.getListSources>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetListSources.GetListSources, Sitecore.ListManagement.ContentSearch"/>
      </listManagement.getListSources>

      <!--  GET LIST LOCK
            The pipeline is responsible for retrieval a lock for a list.
      -->
      <listManagement.getLock>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.GetLock.GetLock, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getLock>
      
      <!--  GET LOCKED LISTS
            The pipeline is responsible for retrieval a list of locked list ids.
      -->
      <listManagement.getLockedLists>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.GetLockedLists.GetLockedLists, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getLockedLists>

      <!--  GET OUT OF LIST USE
          The pipeline is responsible for removing list from in use state.
      -->
      <listManagement.getOutOfListUse>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.GetOutOfListUse.GetOutOfListUse, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getOutOfListUse>

      <!--  GET SEGMENTED LIST CONTACTS
            The pipeline is responsible for getting contacts associated with the segmented list.
      -->
      <listManagement.getSegmentedListContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetAssociatedContacts.GetSegmentedListContacts, Sitecore.ListManagement.ContentSearch">
          <param ref="ruleEngine" />
        </processor>
      </listManagement.getSegmentedListContacts>

      <!--  GET USE LIST LOCK
            The pipeline is responsible for retrieval a use lock for a list.
      -->
      <listManagement.getUseLock>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.GetUseLock.GetUseLock, Sitecore.ListManagement.ContentSearch" />
      </listManagement.getUseLock>

      <!--  IMPORT MEDIA CONTACTS
            The pipeline is responsible for importing of the contacts from file stored in media library into the xDB.
      -->
      <listManagement.importMediaContacts>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.GetMediaStream, Sitecore.ListManagement.ContentSearch"/>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.GetContactsFromStream, Sitecore.ListManagement.ContentSearch">
          <param ref="csvReader" />
        </processor>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.RemoveMediaItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.ImportContacts, Sitecore.ListManagement.ContentSearch">
          <ContactProcessingProvider ref="processContactsDataProvider" />
        </processor>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.UpdateRecipientsCount, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.UpdateBulkOperationsId, Sitecore.ListManagement.ContentSearch" />
      </listManagement.importMediaContacts>

      <!--  IS LIST IN USE
          The pipeline is responsible for checking whether the list in use.
      -->
      <listManagement.isListInUse>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.IsListInUse.IsListInUse, Sitecore.ListManagement.ContentSearch" />
      </listManagement.isListInUse>

      <!--  IS LIST LOCKED
          The pipeline is responsible for checking whether the list locked.
      -->
      <listManagement.isListLocked>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.IsListLocked.IsListLocked, Sitecore.ListManagement.ContentSearch" />
      </listManagement.isListLocked>

      <!--  LOCK LIST
          The pipeline is responsible for locking the list.
      -->
      <listManagement.lockList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.LockList.LockList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.lockList>

      <!--  MOVE FOLDER
            The pipeline is responsible for moving of the folder.
      -->
      <listManagement.moveFolder>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.Folder.GetFolderItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.Folder.GetDestinationFolderItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.MoveItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.moveFolder>

      <!--  MOVE LIST
            The pipeline is responsible for moving List item into a destination folder.
      -->
      <listManagement.moveList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.MoveList.GetListItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.Folder.GetDestinationFolderItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.MoveItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.moveList>

      <!--  REMOVE CONTACT ASSOCIATIONS
            The pipeline is responsible for removing of all contact associations from a contact list.
      -->
      <listManagement.removeContactAssociations>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.GetContactListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RemoveContactAssociations.RemoveAssociationsFromIndex, Sitecore.ListManagement.ContentSearch">
          <ContactProcessingProvider ref="processContactsDataProvider" />
        </processor>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateContactListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateContactListBulkOperationsId, Sitecore.ListManagement.ContentSearch" />
      </listManagement.removeContactAssociations>

      <!--  REMOVE DUPLICATES
          The pipeline is responsible for removing duplicates in associated contacts.
      -->
      <listManagement.removeDuplicates>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RemoveDuplicates.RetrieveAssociatedContacts, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RemoveDuplicates.ResolveDuplicatedContacts, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RemoveDuplicates.RemoveDuplicatedAssociations, Sitecore.ListManagement.ContentSearch" />
      </listManagement.removeDuplicates>

      <!--  REMOVE MEDIA
            The pipeline is responsible for removing of the media item.
      -->
      <listManagement.removeMedia>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.ImportMediaContacts.RemoveMediaItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.removeMedia>

      <!--  RENAME FOLDER
            The pipeline is responsible for renaming of the folder.
      -->
      <listManagement.renameFolder>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.Folder.GetFolderItem, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.RenameFolder.RenameFolderItem, Sitecore.ListManagement.ContentSearch" />
      </listManagement.renameFolder>

      <!--  SET LIST IN USE
          The pipeline is responsible for marking the list as in use.
      -->
      <listManagement.setListInUse>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.SetListInUse.SetListInUse, Sitecore.ListManagement.ContentSearch" />
      </listManagement.setListInUse>

      <!--  UNLOCK LIST
          The pipeline is responsible for unlocking the locked list.
      -->
      <listManagement.unlockList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Locking.UnlockList.UnlockList, Sitecore.ListManagement.ContentSearch" />
      </listManagement.unlockList>

      <!--  UPDATE CONTACT ASSOCIATIONS
          The pipeline is responsible for updating contact associations after changes in list sources.
      -->
      <listManagement.updateContactAssociations>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateContactAssociations.CheckListSourceChanged, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateContactAssociations.DetectContactAssociationsChanges, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateContactAssociations.ApplyContactAssociationsChanges, Sitecore.ListManagement.ContentSearch" />
      </listManagement.updateContactAssociations>

      <!--  UPDATE LIST
            The pipeline is responsible for updating Contact List item according to the data provided by ContactList entity.
      -->
      <listManagement.updateList>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateList.CheckListType, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateList.UpdateContactList, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.Common.UpdateSegmentedListRecipientsCount, Sitecore.ListManagement.ContentSearch" />
      </listManagement.updateList>

      <!-- UPDATE SOURCE
          The pipeline is responsible for checking validity of the source and adjusting it if necessary.
      -->
      <listManagement.updateSource>
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.GetInitialList, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateSource.ValidateSource, Sitecore.ListManagement.ContentSearch" />
        <processor type="Sitecore.ListManagement.ContentSearch.Pipelines.UpdateSource.ClearSources, Sitecore.ListManagement.ContentSearch" />
      </listManagement.updateSource>

      <group groupName="analytics.bulk.contact">
        <pipelines>
          <updateFields>
            <processor type="Sitecore.ListManagement.Analytics.Data.Bulk.Contact.Pipelines.UpdateFields.UpdatePersonalInfo, Sitecore.ListManagement.Analytics" />
            <processor type="Sitecore.ListManagement.Analytics.Data.Bulk.Contact.Pipelines.UpdateFields.UpdateEmails, Sitecore.ListManagement.Analytics" />
            <processor type="Sitecore.ListManagement.Analytics.Data.Bulk.Contact.Pipelines.UpdateFields.ImportContactsTagsUpdateFieldsProcessor, Sitecore.ListManagement.Analytics" />
            <processor type="Sitecore.ListManagement.Analytics.Data.Bulk.Contact.Pipelines.UpdateFields.ProcessContactsUpdateContactTags, Sitecore.ListManagement.Analytics" />
            <processor type="Sitecore.ListManagement.Analytics.Data.Bulk.Contact.Pipelines.UpdateFields.ProcessContactsRemoveContactTags, Sitecore.ListManagement.Analytics" />
          </updateFields>

          <beforePersist>
          </beforePersist>

          <afterPersist>
          </afterPersist>
        </pipelines>
      </group>
      
    </pipelines>

    <scheduling>
      <agent type="Sitecore.ListManagement.Analytics.UnlockContactListsAgent, Sitecore.ListManagement.Analytics" interval="00:00:10">
        <param name="listManager" ref="contactListManager" />
        <param name="mapper" type="Sitecore.ListManagement.ContentSearch.BatchIdMapper, Sitecore.ListManagement.ContentSearch" />
        <param name="operationManager" type="Sitecore.Analytics.Data.Bulk.Contact.ContactBulkUpdateManager, Sitecore.Analytics" />
      </agent>
    </scheduling>
  </sitecore>
</configuration>