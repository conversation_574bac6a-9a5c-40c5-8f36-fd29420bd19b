﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <mvc.requestBegin>
        <processor type="Sitecore.Mvc.Analytics.Pipelines.MvcEvents.RequestBegin.StartTracking, Sitecore.Mvc.Analytics"/>
      </mvc.requestBegin>

      <mvc.getRenderer>
        <processor type="Sitecore.Mvc.Analytics.Pipelines.Response.GetRenderer.CustomizeRendering, Sitecore.Mvc.Analytics" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Response.GetRenderer.GetViewRenderer, Sitecore.Mvc']"/>
      </mvc.getRenderer>

      <mvc.customizeRendering>      
        <processor type="Sitecore.Mvc.Analytics.Pipelines.Response.CustomizeRendering.SelectVariation, Sitecore.Mvc.Analytics"/>        
        <processor type="Sitecore.Mvc.Analytics.Pipelines.Response.CustomizeRendering.Personalize, Sitecore.Mvc.Analytics"/>
      </mvc.customizeRendering>
    </pipelines>
  </sitecore>
</configuration>
