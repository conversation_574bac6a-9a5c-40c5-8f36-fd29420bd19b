﻿<!--
	Unicorn UI Configuration

	This file configures the Unicorn UI elements, including: Unicorn control panel (/unicorn.aspx), content editor commands (partial sync/reserialize), content editor warnings, and serialization conflict warning.
	
	This file should be removed when deploying to Content Delivery environments to remove all Unicorn UI elements, which are not needed in CD.

	http://github.com/kamsar/Unicorn
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<unicorn>
			<!-- 
				Authentication Provider
				Provides a security protocol to determine who has access to the Unicorn Control Panel
			-->
			<authenticationProvider type="Unicorn.ControlPanel.Security.ChapAuthenticationProvider, Unicorn">
        <SharedSecret>80370FAC7B56A985B23FE80345F537C27EC6BFE78FD78EF25B7CC6BBEDCE31FB</SharedSecret>
				<!--<SharedSecret></SharedSecret> Uncomment this line and provide a strong randomized shared secret here. At least 64 characters is recommended, for example https://www.grc.com/passwords.htm -->
				<!-- Authentication challenges are stored in a Sitecore database. You can select which database here. No item installs/templates are required. -->
				<ChallengeDatabase>web</ChallengeDatabase>
				<!-- 
					Writes the reason why failed automated tool authentications failed to the Sitecore logs.
					Will result in writing your shared secret to the logs as part of the signature base,
					so you can disable it if you wish.
				-->
				<WriteAuthFailuresToLog>false</WriteAuthFailuresToLog>
			</authenticationProvider>
			<!--
				Legacy provider: this provides Unicorn 2.x and 3.0.x behavior for the security token. Deprecated.
				<authenticationProvider type="Unicorn.ControlPanel.Security.LegacyAuthenticationProvider, Unicorn" />
			-->
		</unicorn>
		<processors>
			<saveUI>
				<!--
					This processor will compare the pre-save version of the item to what's serialized on disk
					and throw a warning box if they do not match. This helps prevent overwriting of data if you
					happen to forget to sync changes into Sitecore.

					All configurations are evaluated by the processor. Fields ignored by the FieldPredicate are ignored.
				-->
				<processor patch:before="*[@type='Sitecore.Pipelines.Save.Save, Sitecore.Kernel']" mode="on" type="Unicorn.UI.Pipelines.SaveUi.SerializationConflictProcessor, Unicorn"/>
			</saveUI>
		</processors>

		<pipelines>
			<httpRequestBegin>
				<!--
					This pipeline handler shows the Unicorn control panel. You can customize the URL the control panel lives at by changing the activationUrl.
					The activationUrl must be a URL that the Sitecore pipeline won't ignore (e.g. .aspx, .ashx, etc)
				-->
				<processor patch:after="*[@type='Sitecore.Pipelines.HttpRequest.UserResolver, Sitecore.Kernel']" type="Unicorn.ControlPanel.UnicornControlPanelPipelineProcessor">
					<param desc="activationUrl">/unicorn.aspx</param>
				</processor>
			</httpRequestBegin>
			<filterItem>
				<processor patch:before="*[@type='Sitecore.Pipelines.FilterItem.CheckIfFilteringIsActive, Sitecore.Kernel']" type="Unicorn.ControlPanel.CheckIfFilterDisablerIsActive, Unicorn" />
			</filterItem>
			<getContentEditorWarnings>
				<processor type="Unicorn.UI.Pipelines.GetContentEditorWarnings.SerializedWarning, Unicorn" />
			</getContentEditorWarnings>

			<!--
				This 'micro-pipeline' controls the rendering of the Unicorn control panel.
				This is handy because it allows you to add new verbs or alter the appearance of the control panel.
			-->
			<unicornControlPanelRequest>
				<processor type="Unicorn.ControlPanel.Pipelines.UnicornControlPanelRequest.ChallengeVerb, Unicorn" />
				<processor type="Unicorn.ControlPanel.Pipelines.UnicornControlPanelRequest.SyncVerb, Unicorn" />
				<processor type="Unicorn.ControlPanel.Pipelines.UnicornControlPanelRequest.ReserializeVerb, Unicorn" />
				<processor type="Unicorn.ControlPanel.Pipelines.UnicornControlPanelRequest.HandleAccessDenied, Unicorn" />
				<processor type="Unicorn.ControlPanel.Pipelines.UnicornControlPanelRequest.RenderControlPanel, Unicorn" />
			</unicornControlPanelRequest>
		</pipelines>

		<commands>
			<!--
				Replaces Sitecore admin serialization commands (on the Developer tab of the ribbon) with Unicorn ones.
				With these installed, if you serialize, load, or revert trees or items in the context of items that
				are included in Unicorn, regular Sitecore serialization will be replaced with Unicorn reserialization
				or Unicorn syncing of that item (or tree) instead. Note that "update" and "revert" are the same thing
				in the context of Unicorn - a sync.
			-->
			<command name="itemsync:dumpitem">
				<patch:attribute name="type">Unicorn.UI.Commands.UnicornDumpItemCommand, Unicorn</patch:attribute>
			</command>
			<command name="itemsync:dumptree">
				<patch:attribute name="type">Unicorn.UI.Commands.UnicornDumpTreeCommand, Unicorn</patch:attribute>
			</command>
			<command name="itemsync:loaditem">
				<patch:attribute name="type">Unicorn.UI.Commands.UnicornLoadItemCommand, Unicorn</patch:attribute>
			</command>
			<command name="itemsync:loadtree">
				<patch:attribute name="type">Unicorn.UI.Commands.UnicornLoadTreeCommand, Unicorn</patch:attribute>
			</command>
		</commands>
	</sitecore>
</configuration>
