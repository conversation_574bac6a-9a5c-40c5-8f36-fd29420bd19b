﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file contains configuration settings for the Sitecore Path Analyzer client interfaces.

This file should only be used within Sitecore instances functioning as CM (Content Management) instances. On all other
instance types (e.g. Content Delivery, Processing), this file can be removed or disabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- COMMANDS -->
    <commands>
      <command name="pathanalyzer:open-page-analyzer" type="Sitecore.PathAnalyzer.Client.Commands.OpenPageAnalyzer,Sitecore.PathAnalyzer.Client" />
    </commands>
    <!-- PIPELINES -->
    <pipelines>
      <getContentEditorWarnings>
        <processor type="Sitecore.PathAnalyzer.Client.Pipelines.GetContentEditorWarnings.TreeDefinitionDeployment,Sitecore.PathAnalyzer.Client" />
        <processor type="Sitecore.PathAnalyzer.Client.Pipelines.GetContentEditorWarnings.StartDateNotDefined,Sitecore.PathAnalyzer.Client" />
      </getContentEditorWarnings>
      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Applications/PathAnalyzer" deep="true" category="pathanalyzer" pattern="*.js,*.css" />
          </sources>
        </processor>
      </speak.client.resolveScript>
    </pipelines>
  </sitecore>
</configuration>
