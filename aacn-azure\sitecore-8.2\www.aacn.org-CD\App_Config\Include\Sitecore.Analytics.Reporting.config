﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the reporting subsystem of the Sitecore Experience Database (xDB). The reporting subsystem makes the
experience data available for use by Sitecore reporting applications.

If the current server does not need to be part reporting subsystem, you can rename this config file so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <customHandlers>
      <handler trigger="/~/analytics/reports" type="Sitecore.Analytics.Dashboard.ReportDataHandler, Sitecore.Analytics" />
    </customHandlers>

    <dataAdapterManager.reporting defaultProvider="default">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.Analytics.Reports.Data.DataAccess.DataAdapters.Sql.SqlServer.SqlServerDataAdapterProvider, Sitecore.Analytics"/>
      </providers>
    </dataAdapterManager.reporting>

    <pipelines>
      
      
      <registerReportingDataSource>
        <processor type="Sitecore.Analytics.Pipelines.RegisterReportingDataSource.RegisterReportingDataSourceProcessor, Sitecore.Analytics" >
        </processor>
      </registerReportingDataSource>

      
    </pipelines>

    <reporting>

      <cacheProvider type="Sitecore.Analytics.Reporting.Caching.ReportDataCacheInMemory, Sitecore.Analytics" singleInstance="true">
        <param key="maxCacheSize">50MB</param>
        <param key="defaultCacheExpiration">00:10:00</param>
      </cacheProvider>

      <collectionDataProvider type="Sitecore.Analytics.Data.DataAccess.MongoDb.MongoDbDataAdapterProvider, Sitecore.Analytics.MongoDb" />

      <dashboardReportQueryBuilder>
        <builder type="Sitecore.Analytics.Reporting.SqlDashboardReportQueryBuilder, Sitecore.Analytics.Sql" singleInstance="true"/>
      </dashboardReportQueryBuilder>
      
      <dataProvider type="Sitecore.Analytics.Reporting.ReportDataProvider, Sitecore.Analytics" singleInstance="true" cacheEnabled="false" >
        <datasources hint="list:RegisterDataSource">
          <add key="item" type="Sitecore.Analytics.Reporting.Datasources.ItemReportDataSource"/>

          <add key="collection" type="Sitecore.Analytics.Reporting.MongoDbReportDataSource, Sitecore.Analytics.MongoDB">
            <param desc="connectionStringName">analytics</param>
            <FiltersFactory type="Sitecore.Analytics.Reporting.Filters.ItemBasedFiltersFactory, Sitecore.Analytics">
              <param desc="definitionDatabaseName">master</param>
              <FiltersPath>/sitecore/system/Settings/Analytics/Vendor Specific Filters/MongoDb</FiltersPath>
            </FiltersFactory>
          </add>

          <add key="reporting" type="Sitecore.Analytics.Reporting.SqlReportDataSource, Sitecore.Analytics.Sql">
            <param desc="connectionStringName">reporting</param>
            <FiltersFactory type="Sitecore.Analytics.Reporting.Filters.ItemBasedFiltersFactory, Sitecore.Analytics">
              <param desc="definitionDatabaseName">master</param>
              <FiltersPath>/sitecore/system/Settings/Analytics/Vendor Specific Filters/MSSQL</FiltersPath>
            </FiltersFactory>
          </add>

          <add key="function" type="Sitecore.Analytics.Reports.StimulsoftIntegration.BuiltInFunctionsReportDataSource, Sitecore.Analytics" />
        </datasources>

        <InitializationPipelineName>registerReportingDataSource</InitializationPipelineName>
      </dataProvider>
      
    </reporting>

    <reportManager defaultProvider="default">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.Analytics.Reports.Data.Sql.SqlServer.SqlServerReportProvider, Sitecore.Analytics"/>
      </providers>
    </reportManager>
    
  </sitecore>
</configuration>