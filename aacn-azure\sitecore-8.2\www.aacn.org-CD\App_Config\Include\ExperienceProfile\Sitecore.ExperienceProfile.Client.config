﻿<?xml version="1.0" encoding="utf-8" ?>

<!--

Purpose: This include file configures Experience Profile client application.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <initialize>
        <!-- Creates client specific http endpoints. -->
        <processor type="Sitecore.Cintel.Client.Initialization.InitializeRoutes, Sitecore.Cintel.Client" patch:after="*[last()]" />
      </initialize>
    </pipelines>

    <experienceProfile>
      
      <!-- Provides a hook to modify view results before they are displayed by Experience Profile appliction. -->
      <resultTransformManager>

        <!-- If http request header "X-SC-CintelTransformerClientName" matches clientName element, then the result transformer provider is applied.
             Header value has to be:  X-SC-CintelTransformerClientName: speakClient -->
        <resultTransformProvider clientName="speakClient" type="Sitecore.Cintel.Endpoint.Transfomers.ResultTransformProvider" singleInstance="true">

          <!-- If http request header "X-SC-CintelTransfomerKey" matches a viewName element, then the result transformer is applied.
               For example, header value could read as:  X-SC-CintelTransfomerKey: visits -->
          <intelResultTransformers>
            <add viewName="visits" type="Sitecore.Cintel.Client.Transformers.Contact.VisitResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="goals" type="Sitecore.Cintel.Client.Transformers.Contact.GoalResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="campaigns" type="Sitecore.Cintel.Client.Transformers.Contact.CampaignResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="events" type="Sitecore.Cintel.Client.Transformers.Contact.EventResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="latest-statistics" type="Sitecore.Cintel.Client.Transformers.Contact.OverviewResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="latest-events" type="Sitecore.Cintel.Client.Transformers.Contact.LatestEventResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="recent-campaigns" type="Sitecore.Cintel.Client.Transformers.Contact.RecentCampaignsResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="best-pattern-matches" type="Sitecore.Cintel.Client.Transformers.Contact.BestPatternMatchesResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="visit-summary" type="Sitecore.Cintel.Client.Transformers.Contact.VisitSummaryResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="visit-pages" type="Sitecore.Cintel.Client.Transformers.Contact.VisitPagesResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="visit-internal-searches" type="Sitecore.Cintel.Client.Transformers.Contact.VisitInternalSearchesResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="profile-info" type="Sitecore.Cintel.Client.Transformers.Contact.ProfilingProfilesResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="external-keyword-summary" type="Sitecore.Cintel.Client.Transformers.Contact.KeywordResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="internal-keyword-summary" type="Sitecore.Cintel.Client.Transformers.Contact.InternalKeywordSummaryResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="paid-keyword-summary" type="Sitecore.Cintel.Client.Transformers.Contact.KeywordResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="external-keyword-detail" type="Sitecore.Cintel.Client.Transformers.Contact.KeywordDetailResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="internal-keyword-detail" type="Sitecore.Cintel.Client.Transformers.Contact.KeywordDetailResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="paid-keyword-detail" type="Sitecore.Cintel.Client.Transformers.Contact.KeywordDetailResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="latest-visitors" type="Sitecore.Cintel.Client.Transformers.Contact.LatestVisitorsResultTransformer, Sitecore.Cintel.Client"/>

            <add viewName="journey" type="Sitecore.Cintel.Client.Transformers.Contact.JourneyResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="journey-detail-outcome" type="Sitecore.Cintel.Client.Transformers.Contact.JourneyOutcomeDetailResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="journey-detail-online-interaction" type="Sitecore.Cintel.Client.Transformers.Contact.JourneyOnlineInteractionDetailResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="journey-detail-offline-interaction" type="Sitecore.Cintel.Client.Transformers.Contact.JourneyOfflineInteractionDetailResultTransformer, Sitecore.Cintel.Client"/>
            
            <add viewName="channel-interaction-distribution" type="Sitecore.Cintel.Client.Transformers.Contact.ChannelInteractionDistributionResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="channel-goal-distribution" type="Sitecore.Cintel.Client.Transformers.Contact.ChannelGoalDistributionResultTransformer, Sitecore.Cintel.Client"/>
            <add viewName="channel-summary" type="Sitecore.Cintel.Client.Transformers.Contact.ChannelSummaryResultTransformer, Sitecore.Cintel.Client"/>
            
            <add viewName="outcome-detail" type="Sitecore.Cintel.Client.Transformers.Contact.OutcomeDetailTransformer, Sitecore.Cintel.Client"/>
          
          </intelResultTransformers>

           <!-- If http request header "X-SC-CintelTransfomerKey" matches key element, then the result transformer is applied.
                Header value has to be:  X-SC-CintelTransfomerKey: default -->
          <contactSearchResultTransformer>
            <add key="default" type="Sitecore.Cintel.Client.Transformers.Contact.ContactSearchResultTransformer, Sitecore.Cintel.Client" />
          </contactSearchResultTransformer>

          <!-- If http request header "X-SC-CintelTransfomerKey" matches key element, then the result transformer is applied.
                Header value has to be:  X-SC-CintelTransfomerKey: default -->
          <contactDetailsTransformer>
            <add key="default" type="Sitecore.Cintel.Client.Transformers.Contact.ContactDetailsTransformer, Sitecore.Cintel.Client" />
          </contactDetailsTransformer>
		  
        </resultTransformProvider>
      </resultTransformManager>
    </experienceProfile>

    <pipelines>
      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Applications/ExperienceProfile" deep="true" category="experienceprofile" pattern="*.js" />
            <source folder="/sitecore/shell/client/Applications/ExperienceProfile" deep="true" category="cistubs" pattern="*.json" />
          </sources>
        </processor>
      </speak.client.resolveScript>


    </pipelines>
  </sitecore>
</configuration>
