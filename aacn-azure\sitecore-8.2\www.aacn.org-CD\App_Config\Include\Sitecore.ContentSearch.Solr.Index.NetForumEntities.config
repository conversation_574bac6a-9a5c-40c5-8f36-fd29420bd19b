﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <contentSearch>
      <configuration type="Sitecore.ContentSearch.ContentSearchConfiguration, Sitecore.ContentSearch">
        <indexes hint="list:AddIndex">
          <index id="sitecore_netforum_entities" type="Sitecore.ContentSearch.SolrProvider.SolrSearchIndex, Sitecore.ContentSearch.SolrProvider">
            <param desc="name">$(id)</param>
            <param desc="core">$(id)</param>
            <param desc="propertyStore" ref="contentSearch/indexConfigurations/databasePropertyStore" param1="$(id)" />
            <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration" />
            <strategies hint="list:AddStrategy">
              <strategy ref="contentSearch/indexConfigurations/indexUpdateStrategies/externalDatasourceAsyncNetForum" />
            </strategies>
            <locations hint="list:AddCrawler">
              <crawler type="Aacn.Business.ContentSearch.Indexing.Crawlers.NetForumEntityCrawler, Aacn.Business">
              </crawler>
            </locations>
          </index>
        </indexes>
      </configuration>
      <indexConfigurations>
        <defaultSolrIndexConfiguration type="Sitecore.ContentSearch.SolrProvider.SolrIndexConfiguration, Sitecore.ContentSearch.SolrProvider">
          <fieldMap type="Sitecore.ContentSearch.SolrProvider.SolrFieldMap, Sitecore.ContentSearch.SolrProvider">
            <typeMatches hint="raw:AddTypeMatch">
                <typeMatch patch:after="*[@typeName='double']"  typeName="decimal" type="System.Decimal" fieldNameFormat="{0}_s" settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
                <typeMatch patch:after="*[@typeName='double']"  typeName="productType" type="Aacn.DomainObjects.NetForum.Commerce.ProductType,Aacn.DomainObjects" fieldNameFormat="{0}_s" settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
                <typeMatch patch:after="*[@typeName='double']"  typeName="priceCollection" type="System.Collections.Generic.List`1[Aacn.DomainObjects.NetForum.Commerce.Price,Aacn.DomainObjects]" fieldNameFormat="{0}_sm" multiValued="true" settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
                <typeMatch patch:after="*[@typeName='double']"  typeName="price" type="Aacn.DomainObjects.NetForum.Commerce.Price,Aacn.DomainObjects" fieldNameFormat="{0}_s" settingType="Sitecore.ContentSearch.SolrProvider.SolrSearchFieldConfiguration, Sitecore.ContentSearch.SolrProvider" />
            </typeMatches>
            <fieldNames hint="raw:AddFieldByFieldName">
              <field fieldName="ProductType" returnType="string" />
              <field fieldName="ProductCategoryCode" returnType="string" />
              <field fieldName="ProductSubCategoryCode" returnType="string" />
              <field fieldName="NonMemberTerms" returnType="priceCollection" />
              <field fieldName="MemberPrices" returnType="priceCollection" />
              <field fieldName="NonMemberPrices" returnType="priceCollection" />
              <field fieldName="Prices" returnType="priceCollection" />
              <field fieldName="MemberTerms" returnType="priceCollection" />
              <field fieldName="ReviewCollection" returnType="string" />
              <field fieldName="ProductRelation" returnType="string" />
              <field fieldName="RelatedProducts" returnType="stringCollection" />
              <field fieldName="Thumbnails" returnType="stringCollection" />
              <field fieldName="Format" returnType="string" />
              <field fieldName="ThumbnailUrl" returnType="string" />
              <field fieldName="YearPublished" returnType="string" />
              <field fieldName="Attributes" returnType="string" />
              <field fieldName="SecondaryTopics" returnType="stringCollection" />
              <field fieldName="PrimaryTopic" returnType="string" />
              <field fieldName="EvalCode" returnType="string" />
              <field fieldName="TestCode" returnType="string" />
              <field fieldName="VideoUrl" returnType="string" />
              <field fieldName="DownloadUrl" returnType="string" />
              <field fieldName="EventTypeCode" returnType="string" />
              <field fieldName="Documents" returnType="stringCollection" />
              <field fieldName="SessionType" returnType="string" />
              <field fieldName="AbstractType" returnType="string" />
              <field fieldName="StartTime" returnType="string" />
              <field fieldName="EndTime" returnType="string" />
              <field fieldName="ExtendedSessions" returnType="stringCollection" />
              <field fieldName="Speakers" returnType="stringCollection" />
              <field fieldName="Presentations" returnType="stringCollection" />
              <field fieldName="Handouts" returnType="stringCollection" />
              <field fieldName="AreaOfPractice" returnType="stringCollection" />
              <field fieldName="ClinicalSystems" returnType="stringCollection" />
              <field fieldName="CertificationTopics" returnType="stringCollection" />
              <field fieldName="Levels" returnType="stringCollection" />
              <field fieldName="PatientPopulations" returnType="stringCollection" />
              <field fieldName="ProfessionalPractice" returnType="stringCollection" />
              <field fieldName="Roles" returnType="stringCollection" />
              <field fieldName="Documents" returnType="stringCollection" />
              <field fieldName="CerpCategory" returnType="string" />
              <field fieldName="ExtendedSpeakingAssignment" returnType="stringCollection" />
              <field fieldName="PrimaryLocation" returnType="string" />
              <field fieldName="MemberPrice" returnType="string" />
              <field fieldName="NonMemberPrice" returnType="string" />
              <field fieldName="PrimaryAddress" returnType="string" />
              <field fieldName="PrimaryPhone" returnType="string" />
              <field fieldName="PrimaryEmail" returnType="string" />
              <field fieldName="PrimaryWebsite" returnType="string" />
              <field fieldName="PrimaryFax" returnType="string" />
              <field fieldName="NationalMembership" returnType="string" />
              <field fieldName="ChapterMembership" returnType="string" />
              <field fieldName="Affiliations" returnType="stringCollection" />
              <field fieldName="TypeCode" returnType="string" />
              <field fieldName="CustomerType" returnType="string" />
              <field fieldName="Sponsors" returnType="stringCollection" />
              <field fieldName="SingleEventFee" returnType="string" />
              <field fieldName="SessionFee" returnType="stringCollection" />
              <field fieldName="ExtendedPrice" returnType="priceCollection" />
              <field fieldName="CategoryCode" returnType="string" />
              <field fieldName="State" returnType="string" />
              <field fieldName="ChapterType" returnType="string" />
              <field fieldName="NetworkOrChapter" returnType="string" />
              <field fieldName="Rooms" returnType="stringCollection" />
              <field fieldName="Sponsors" returnType="stringCollection" />
              <field fieldName="Keywords" returnType="stringCollection" />   
              <field fieldName="RegistrationFees" returnType="stringCollection" />
              <field fieldName="EventDocuments" returnType="stringCollection" />
              <field fieldName="SessionDocuments" returnType="stringCollection" />
              <field fieldName="ExhibitorCategory" returnType="string" />
              <field fieldName="SponsorName" returnType="string" />
              <field fieldName="SponsorLogoUrl" returnType="string" />
              <field fieldName="SponsoredByHeading" returnType="string" />
            </fieldNames>
          </fieldMap>
        </defaultSolrIndexConfiguration>
      </indexConfigurations>
    </contentSearch>
  </sitecore>
</configuration>