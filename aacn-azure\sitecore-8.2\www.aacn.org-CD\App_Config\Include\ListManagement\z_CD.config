﻿<?xml version="1.0" encoding="utf-8" ?>

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:set="http://www.sitecore.net/xmlconfig/set/">
  <sitecore>

    <contentSearch>
      <configuration>
        <indexes>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_testing_index">
            <patch:delete/>
          </index>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_suggested_test_index">
            <patch:delete/>
          </index>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_list_index">
            <patch:delete/>
          </index>
        </indexes>
      </configuration>
    </contentSearch>

    <ruleEngine>
      <param desc="ruleDatabaseName">webcd</param>
    </ruleEngine>

  </sitecore>
</configuration>