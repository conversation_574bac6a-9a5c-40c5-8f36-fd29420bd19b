﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the SOLR index that is used by the Social Connected module to search for messages.

You must enable this include file on Sitecore instances that use SOLR.

To enable this include file, rename it to have a ".config" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <indexConfigurations>
        <socialSolrIndexConfiguration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
          <exclude hint="list:ExcludeField">
            <socialaccount>{DD384E3C-63CC-4BB7-9AFA-7853D0ADE9D0}</socialaccount>
            <socialcampaign>{2A318490-5DF3-4ED5-AE00-7BCCB9E9E3ED}</socialcampaign>
            <socialcomments>{456C87EF-DEF3-4024-8A04-4B2F79084221}</socialcomments>
            <socialcreateddate>{4191FB9B-674B-413D-8CAF-93A3F36147FC}</socialcreateddate>
            <socialitemuri>{419F4E7E-58C0-454F-9310-CFC198DD645B}</socialitemuri>
            <sociallink>{1820BF83-7008-4979-8DFC-3841DF0C2CBD}</sociallink>
            <sociallinkdescription>{C34C770F-12DE-43D3-B6B5-60BEDB4050D8}</sociallinkdescription>
            <sociallinkimage>{663688E8-167B-44DE-9A8B-8C55664A8BF1}</sociallinkimage>
            <sociallinktitle>{6CB377BB-F091-455A-B070-3B6ED6834D33}</sociallinktitle>
            <socialmessage>{85DAC42D-B659-4186-9494-0B8A45B7AEA6}</socialmessage>
            <socialposteddate>{94924CBE-15C3-4FE1-AF5C-2D8B1B474697}</socialposteddate>
            <socialstatus>{AF85F026-6519-4DDB-9861-3882A017FF92}</socialstatus>
          </exclude>
          <fields hint="raw:AddComputedIndexField">
            <field fieldName="container" returnType="string">Sitecore.Social.Client.MessagePosting.UI.CustomFields.CustomContainerComputedField,Sitecore.Social.Client</field>
            <field fieldName="created date" returnType="datetime">Sitecore.Social.Client.MessagePosting.UI.CustomFields.CreatedDateComputedField,Sitecore.Social.Client</field>
            <field fieldName="final workflow state" returnType="bool">Sitecore.Social.Client.MessagePosting.UI.CustomFields.CustomFinalWorkflowStateComputedField,Sitecore.Social.Client</field>
            <field fieldName="posted date" returnType="datetime">Sitecore.Social.Client.MessagePosting.UI.CustomFields.PostedDateComputedField,Sitecore.Social.Client</field>
            <field fieldName="social account" returnType="id">Sitecore.Social.Client.MessagePosting.UI.CustomFields.CustomAccountComputedField,Sitecore.Social.Client</field>
            <field fieldName="workflow state" returnType="id">Sitecore.Social.Client.MessagePosting.UI.CustomFields.CustomWorkflowStateComputedField,Sitecore.Social.Client</field>            
          </fields>
        </socialSolrIndexConfiguration>
      </indexConfigurations>
    </social>
  </sitecore>
</configuration>