﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <!-- 
    This file contains a list of common file extensions and their corresponding MIME types 
    
    Notice that the extensions that are also (by default) defined in Sitecore's web.config 
    are commented out (such as "jpg", "gif", etc.). If you add additional extensions (including
    template information, etc.) to Sitecore's web.config file, you should comment them out below. 
    Otherwise the definitions in the web.config file might not work as expected. 
  -->
       
  <mediaType extensions="323"><mimeType>text/h323</mimeType></mediaType>
  <mediaType extensions="acx"><mimeType>application/internet-property-stream</mimeType></mediaType>
  <mediaType extensions="ai,eps,ps"><mimeType>application/postscript</mimeType></mediaType>
  <mediaType extensions="aif,aifc,aiff"><mimeType>audio/x-aiff</mimeType></mediaType>
  <mediaType extensions="asf,asr,asx"><mimeType>video/x-ms-asf</mimeType></mediaType>
  <mediaType extensions="au,snd"><mimeType>audio/basic</mimeType></mediaType>
  <!--
  <mediaType extensions="avi"><mimeType>video/x-msvideo</mimeType></mediaType>
  -->
  <mediaType extensions="axs"><mimeType>application/olescript</mimeType></mediaType>
  <mediaType extensions="bcpio"><mimeType>application/x-bcpio</mimeType></mediaType>
  <mediaType extensions="bin,class,dms,exe,lha,lzh"><mimeType>application/octet-stream</mimeType></mediaType>
  <!--
  <mediaType extensions="bmp,dib"><mimeType>image/bmp</mimeType></mediaType>
  -->
  <mediaType extensions="cat"><mimeType>application/vnd.ms-pkiseccat</mimeType></mediaType>
  <mediaType extensions="cdf"><mimeType>application/x-cdf</mimeType></mediaType>
  <mediaType extensions="cer,crt,der"><mimeType>application/x-x509-ca-cert</mimeType></mediaType>
  <mediaType extensions="clp"><mimeType>application/x-msclip</mimeType></mediaType>
  <mediaType extensions="cmx"><mimeType>image/x-cmx</mimeType></mediaType>
  <mediaType extensions="cod"><mimeType>image/cis-cod</mimeType></mediaType>
  <mediaType extensions="cpio"><mimeType>application/x-cpio</mimeType></mediaType>
  <mediaType extensions="crd"><mimeType>application/x-mscardfile</mimeType></mediaType>
  <mediaType extensions="crl"><mimeType>application/pkix-crl</mimeType></mediaType>
  <mediaType extensions="csh"><mimeType>application/x-csh</mimeType></mediaType>
  <mediaType extensions="css"><mimeType>text/css</mimeType></mediaType>
  <mediaType extensions="dcr,dir,dxr"><mimeType>application/x-director</mimeType></mediaType>
  <mediaType extensions="dll"><mimeType>application/x-msdownload</mimeType></mediaType>
  <!--
  <mediaType extensions="doc,dot"><mimeType>application/msword</mimeType></mediaType>
  <mediaType extensions="docx"><mimeType>application/vnd.openxmlformats-officedocument.wordprocessingml.document</mimeType></mediaType>
  -->
  <mediaType extensions="dvi"><mimeType>application/x-dvi</mimeType></mediaType>
  <mediaType extensions="etx"><mimeType>text/x-setext</mimeType></mediaType>
  <mediaType extensions="evy"><mimeType>application/envoy</mimeType></mediaType>
  <!--
  <mediaType extensions="f4v"><mimeType>video/x-f4v</mimeType></mediaType>
  -->
  <mediaType extensions="fif"><mimeType>application/fractals</mimeType></mediaType>
  <mediaType extensions="flr,vrml,wrl,wrz,xaf,xof"><mimeType>x-world/x-vrml</mimeType></mediaType>
  <!--
  <mediaType extensions="flv"><mimeType>video/x-flv</mimeType></mediaType>
  <mediaType extensions="gif"><mimeType>image/gif</mimeType></mediaType>
  -->
  <mediaType extensions="gtar"><mimeType>application/x-gtar</mimeType></mediaType>
  <mediaType extensions="gz"><mimeType>application/x-gzip</mimeType></mediaType>
  <mediaType extensions="hdf"><mimeType>application/x-hdf</mimeType></mediaType>
  <mediaType extensions="hlp"><mimeType>application/winhlp</mimeType></mediaType>
  <mediaType extensions="hqx"><mimeType>application/mac-binhex40</mimeType></mediaType>
  <mediaType extensions="hta"><mimeType>application/hta</mimeType></mediaType>
  <mediaType extensions="htc"><mimeType>text/x-component</mimeType></mediaType>
  <mediaType extensions="htm,html,stm"><mimeType>text/html</mimeType></mediaType>
  <mediaType extensions="htt"><mimeType>text/webviewhtml</mimeType></mediaType>
  <mediaType extensions="ico"><mimeType>image/x-icon</mimeType></mediaType>
  <mediaType extensions="ief"><mimeType>image/ief</mimeType></mediaType>
  <mediaType extensions="iii"><mimeType>application/x-iphone</mimeType></mediaType>
  <mediaType extensions="ins,isp"><mimeType>application/x-internet-signup</mimeType></mediaType>
  <mediaType extensions="jfif"><mimeType>image/pipeg</mimeType></mediaType>
  <!--
  <mediaType extensions="jpg,jpeg,jpe,jfif"><mimeType>image/jpeg</mimeType></mediaType>
  -->
  <mediaType extensions="js"><mimeType>application/x-javascript</mimeType></mediaType>
  <mediaType extensions="latex"><mimeType>application/x-latex</mimeType></mediaType>
  <mediaType extensions="lsf,lsx"><mimeType>video/x-la-asf</mimeType></mediaType>
  <mediaType extensions="m13,m14,mvb"><mimeType>application/x-msmediaview</mimeType></mediaType>
  <mediaType extensions="m3u"><mimeType>audio/x-mpegurl</mimeType></mediaType>
  <mediaType extensions="m4v"><mimeType>video/mp4</mimeType></mediaType>
  <mediaType extensions="man"><mimeType>application/x-troff-man</mimeType></mediaType>
  <mediaType extensions="mdb"><mimeType>application/x-msaccess</mimeType></mediaType>
  <mediaType extensions="me"><mimeType>application/x-troff-me</mimeType></mediaType>
  <mediaType extensions="mht,mhtml,nws"><mimeType>message/rfc822</mimeType></mediaType>
  <mediaType extensions="midi,mid,kar,rmi"><mimeType>audio/midi</mimeType></mediaType>
  <mediaType extensions="mny"><mimeType>application/x-msmoney</mimeType></mediaType>
  <!--
  <mediaType extensions="mov,qt"><mimeType>video/quicktime</mimeType></mediaType>
  -->
  <mediaType extensions="movie"><mimeType>video/x-sgi-movie</mimeType></mediaType>
  <!--
  <mediaType extensions="mp3"><mimeType>audio/mpeg</mimeType></mediaType>
  <mediaType extensions="mp4"><mimeType>video/mp4</mimeType></mediaType>
  <mediaType extensions="mpeg,mp2,mpa,mpe,mpg,mpv2"><mimeType>video/mpeg</mimeType></mediaType>
  -->
  <mediaType extensions="mpp"><mimeType>application/vnd.ms-project</mimeType></mediaType>
  <mediaType extensions="ms"><mimeType>application/x-troff-ms</mimeType></mediaType>
  <mediaType extensions="oda"><mimeType>application/oda</mimeType></mediaType>
  <mediaType extensions="ogg"><mimeType>video/ogg</mimeType></mediaType>
  <mediaType extensions="p10"><mimeType>application/pkcs10</mimeType></mediaType>
  <mediaType extensions="p12,pfx"><mimeType>application/x-pkcs12</mimeType></mediaType>
  <mediaType extensions="p7b,spc"><mimeType>application/x-pkcs7-certificates</mimeType></mediaType>
  <mediaType extensions="p7c,p7m"><mimeType>application/x-pkcs7-mime</mimeType></mediaType>
  <mediaType extensions="p7r"><mimeType>application/x-pkcs7-certreqresp</mimeType></mediaType>
  <mediaType extensions="p7s"><mimeType>application/x-pkcs7-signature</mimeType></mediaType>
  <mediaType extensions="pbm"><mimeType>image/x-portable-bitmap</mimeType></mediaType>
  <!--
  <mediaType extensions="pdf"><mimeType>application/pdf</mimeType></mediaType>
  -->
  <mediaType extensions="pgm"><mimeType>image/x-portable-graymap</mimeType></mediaType>
  <mediaType extensions="pko"><mimeType>application/ynd.ms-pkipko</mimeType></mediaType>
  <mediaType extensions="pma,pmc,pml,pmr,pmw"><mimeType>application/x-perfmon</mimeType></mediaType>
  <!--
  <mediaType extensions="png"><mimeType>image/png</mimeType></mediaType>
  -->
  <mediaType extensions="pnm"><mimeType>image/x-portable-anymap</mimeType></mediaType>
  <mediaType extensions="ppm"><mimeType>image/x-portable-pixmap</mimeType></mediaType>
  <mediaType extensions="ppt,pot,pps"><mimeType>application/vnd.ms-powerpoint</mimeType></mediaType>
  <mediaType extensions="prf"><mimeType>application/pics-rules</mimeType></mediaType>
  <mediaType extensions="pub"><mimeType>application/x-mspublisher</mimeType></mediaType>
  <mediaType extensions="ra,ram,rm"><mimeType>audio/x-pn-realaudio</mimeType></mediaType>
  <mediaType extensions="ras"><mimeType>image/x-cmu-raster</mimeType></mediaType>
  <mediaType extensions="rgb"><mimeType>image/x-rgb</mimeType></mediaType>
  <mediaType extensions="roff,t,tr"><mimeType>application/x-troff</mimeType></mediaType>
  <mediaType extensions="rtf"><mimeType>application/rtf</mimeType></mediaType>
  <mediaType extensions="rtx"><mimeType>text/richtext</mimeType></mediaType>
  <mediaType extensions="scd"><mimeType>application/x-msschedule</mimeType></mediaType>
  <mediaType extensions="sct"><mimeType>text/scriptlet</mimeType></mediaType>
  <mediaType extensions="setpay"><mimeType>application/set-payment-initiation</mimeType></mediaType>
  <mediaType extensions="setreg"><mimeType>application/set-registration-initiation</mimeType></mediaType>
  <mediaType extensions="sgml,sgm"><mimeType>text/sgml</mimeType></mediaType>
  <mediaType extensions="sh"><mimeType>application/x-sh</mimeType></mediaType>
  <mediaType extensions="shar"><mimeType>application/x-shar</mimeType></mediaType>
  <mediaType extensions="sit"><mimeType>application/x-stuffit</mimeType></mediaType>
  <mediaType extensions="spl"><mimeType>application/futuresplash</mimeType></mediaType>
  <mediaType extensions="src"><mimeType>application/x-wais-source</mimeType></mediaType>
  <mediaType extensions="sst"><mimeType>application/vnd.ms-pkicertstore</mimeType></mediaType>
  <mediaType extensions="stl"><mimeType>application/vnd.ms-pkistl</mimeType></mediaType>
  <mediaType extensions="sv4cpio"><mimeType>application/x-sv4cpio</mimeType></mediaType>
  <mediaType extensions="sv4crc"><mimeType>application/x-sv4crc</mimeType></mediaType>
  <!--
  <mediaType extensions="swf"><mimeType>application/x-shockwave-flash</mimeType></mediaType>
  -->
  <mediaType extensions="tar"><mimeType>application/x-tar</mimeType></mediaType>
  <mediaType extensions="tcl"><mimeType>application/x-tcl</mimeType></mediaType>
  <mediaType extensions="tex"><mimeType>application/x-tex</mimeType></mediaType>
  <mediaType extensions="texi,texinfo"><mimeType>application/x-texinfo</mimeType></mediaType>
  <mediaType extensions="tgz"><mimeType>application/x-compressed</mimeType></mediaType>
  <!--
  <mediaType extensions="tif,tiff"><mimeType>image/tiff</mimeType></mediaType>
  -->
  <mediaType extensions="trm"><mimeType>application/x-msterminal</mimeType></mediaType>
  <mediaType extensions="tsv"><mimeType>text/tab-separated-values</mimeType></mediaType>
  <mediaType extensions="txt,asc,bas,c,h"><mimeType>text/plain</mimeType></mediaType>
  <mediaType extensions="uls"><mimeType>text/iuls</mimeType></mediaType>
  <mediaType extensions="ustar"><mimeType>application/x-ustar</mimeType></mediaType>
  <mediaType extensions="vcf"><mimeType>text/x-vcard</mimeType></mediaType>
  <mediaType extensions="wav"><mimeType>audio/x-wav</mimeType></mediaType>
  <mediaType extensions="wcm,wdb,wks,wps"><mimeType>application/vnd.ms-works</mimeType></mediaType>
  <mediaType extensions="wmf"><mimeType>application/x-msmetafile</mimeType></mediaType>
  <!--
  <mediaType extensions="wmv"><mimeType>video/x-ms-wmv</mimeType></mediaType>
  -->
  <mediaType extensions="wri"><mimeType>application/x-mswrite</mimeType></mediaType>
  <mediaType extensions="xbm"><mimeType>image/x-xbitmap</mimeType></mediaType>
  <mediaType extensions="xls,xla,xlc,xlm,xlt,xlw"><mimeType>application/vnd.ms-excel</mimeType></mediaType>
  <mediaType extensions="xml"><mimeType>text/xml</mimeType></mediaType>
  <mediaType extensions="xpm"><mimeType>image/x-xpixmap</mimeType></mediaType>
  <mediaType extensions="xwd"><mimeType>image/x-xwindowdump</mimeType></mediaType>
  <mediaType extensions="z"><mimeType>application/x-compress</mimeType></mediaType>
  <!--
  <mediaType extensions="zip"><mimeType>application/zip</mimeType></mediaType>
  -->
</configuration>
