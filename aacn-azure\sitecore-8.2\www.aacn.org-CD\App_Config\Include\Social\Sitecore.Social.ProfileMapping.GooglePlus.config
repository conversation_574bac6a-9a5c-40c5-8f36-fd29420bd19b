﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the visitor's profile information that is requested when a visitor logs in to the website 
with their Google+ credentials.

Please read the Sitecore Social Connected documentation before changing the configuration.

You can enable or disable a field to extend or reduce the amount of visitor's profile information that is received from Google+.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <!-- This section configures social profile synchronization. 
           To include a specific field from social site in the user's profile set the 'enabled' property to 'true'. 
           To remove a specific field on the social site from the user's profile, set the 'enabled' property to 'false'.
 -->
      <profileKeyMappings>
        <!-- Configuration of profile synchronization with Google+. -->
        <network name="Google">
          <!-- 'Kind' field. -->
          <!-- Identifies this resource as a person. Value: "plus#person". -->
          <field enabled="true" originalKey="Kind" sitecoreKey="gg_Kind" text="Kind" />

          <!-- 'Id' field. -->
          <!-- The person's ID. -->
          <field enabled="true" originalKey="Id" mainNetworkDataPropertyName="Id" sitecoreKey="gg_Id" text="Id" />

          <!-- 'DisplayName' field. -->
          <!-- The person's name, suitable for display. -->
          <field enabled="true" originalKey="DisplayName" mainNetworkDataPropertyName="UserName" sitecoreKey="gg_DisplayName" text="Display Name" />

          <!-- 'Formatted name' field. -->
          <!-- The person's full name, including middle names, suffixes, etc. -->
          <field enabled="true" originalKey="Name.Formatted" sitecoreKey="gg_FormattedName" text="Formatted Name" />

          <!-- 'Family name' field. -->
          <!-- The person's family name (last name). -->
          <field enabled="true" originalKey="Name.FamilyName" sitecoreKey="gg_FamilyName" text="Family Name" />

          <!-- 'Given name' field. -->
          <!-- The person's given name (first name). -->
          <field enabled="true" originalKey="Name.GivenName" sitecoreKey="gg_GivenName" text="Given Name" />

          <!-- 'Middle name' field. -->
          <!-- The person's middle name. -->
          <field enabled="true" originalKey="Name.MiddleName" sitecoreKey="gg_MiddleName" text="Middle Name" />

          <!-- 'Name honorific prefix' field. -->
          <!-- The person's honorific prefixes (such as "Dr." or "Mrs."). -->
          <field enabled="true" originalKey="Name.HonorificPrefix" sitecoreKey="gg_NameHonorificPrefix" text="Name Honorific Prefix" />

          <!-- 'Name honorific suffix' field. -->
          <!-- The person's honorific suffixes (such as "Jr."). -->
          <field enabled="true" originalKey="Name.HonorificSuffix" sitecoreKey="gg_NameHonorificSuffix" text="Name Honorific Suffix" />

          <!-- 'Nickname' field. -->
          <!-- The person's nickname. -->
          <field enabled="true" originalKey="Nickname" sitecoreKey="gg_Nickname" text="Nickname" />

          <!-- 'Birthday' field. -->
          <!-- The person's date of birth, represented as YYYY-MM-DD. -->
          <field enabled="true" originalKey="Birthday" sitecoreKey="gg_Birthday" text="Birthday" />

          <!-- 'Gender' field. -->
          <!-- The person's gender. Possible values are:
              "male" - Male gender.
              "female" - Female gender. -->
          <field enabled="true" originalKey="Gender" sitecoreKey="gg_Gender" text="Gender" />

          <!-- 'Current location' field. -->
          <!-- The person's current location. -->
          <field enabled="true" originalKey="CurrentLocation" sitecoreKey="gg_CurrentLocation" text="Current Location" />

          <!-- 'Url' field. -->
          <!-- The URL of this person's profile. -->
          <field enabled="true" originalKey="Url" sitecoreKey="gg_Url" text="Url" />

          <!-- 'Image Url' field. -->
          <!-- The URL of the person's profile photo. To re-size the image and crop it to a square, append the query string ?sz=x, 
               where x is the dimension in pixels of each side. -->
          <field enabled="true" originalKey="Image.Url" sitecoreKey="gg_ImageUrl" text="Image Url" />

          <!-- 'About me' field. -->
          <!-- A short biography for this person. -->
          <field enabled="true" originalKey="AboutMe" sitecoreKey="gg_AboutMe" text="About Me" />

          <!-- 'RelationshipStatus' field. -->
          <!-- The person's relationship status. Possible values are:
            "single" - Person is single.
            "in_a_relationship" - Person is in a relationship.
            "engaged" - Person is engaged.
            "married" - Person is married.
            "its_complicated" - The relationship is complicated.
            "open_relationship" - Person is in an open relationship.
            "widowed" - Person is widowed.
            "in_domestic_partnership" - Person is in a domestic partnership.
            "in_civil_union" - Person is in a civil union. -->
          <field enabled="true" originalKey="RelationshipStatus" sitecoreKey="gg_RelationshipStatus" text="Relationship Status" />

          <!-- 'Urls' field. -->
          <!-- A list of URLs for this person. -->
          <field enabled="true" originalKey="Urls" sitecoreKey="gg_Urls" text="Urls" />

          <!-- 'Organizations' field. -->
          <!-- A list of current or past organizations with which this person is associated. -->
          <field enabled="true" originalKey="Organizations" sitecoreKey="gg_Organizations" text="Organizations" />

          <!-- 'PlacesLived' field. -->
          <!-- A list of places where this person has lived. -->
          <field enabled="true" originalKey="PlacesLived" sitecoreKey="gg_PlacesLived" text="Places Lived" />

          <!-- 'Tagline' field. -->
          <!-- The short description (tagline) of this person. -->
          <field enabled="true" originalKey="Tagline" sitecoreKey="gg_Tagline" text="Tag Line" />

          <!-- 'objectType' field. -->
          <!-- Type of person within Google+. Possible values include, but are not limited to, the following values:
              "person" - represents an actual person.
              "page" - represents a page. -->
          <field enabled="true" originalKey="ObjectType" sitecoreKey="gg_ObjectType" text="Object Type" />

          <!-- 'braggingRights' field. -->
          <!-- The "bragging rights" line of this person. -->
          <field enabled="true" originalKey="BraggingRights" sitecoreKey="gg_BraggingRights" text="Bragging Rights" />

          <!-- 'isPlusUser' field. -->
          <!-- Whether this user has signed up for Google+. -->
          <field enabled="true" originalKey="IsPlusUser" sitecoreKey="gg_IsPlusUser" text="Is signed up for Google+" />

          <!-- 'plusOneCount' field. -->
          <!-- If a Google+ Page, the number of people who have +1'd this page. -->
          <field enabled="true" originalKey="PlusOneCount" sitecoreKey="gg_PlusOneCount" text="Plus One Count" />

          <!-- 'circledByCount' field. -->
          <!-- If a Google+ Page and for followers who are visible, the number of people who have added this page to a circle. -->
          <field enabled="true" originalKey="CircledByCount" sitecoreKey="gg_CircledByCount" text="Circled By Count" />

          <!-- 'verified' field. -->
          <!-- Whether the person or Google+ Page has been verified. -->
          <field enabled="true" originalKey="Verified" sitecoreKey="gg_Verified" text="Person has been verified" />

          <!-- 'cover.layout' field. -->
          <!-- The layout of the cover art. Possible values include, but are not limited to, the following values:
              "banner" - One large image banner. -->
          <field enabled="true" originalKey="Cover.Layout" sitecoreKey="gg_CoverLayout" text="Cover Layout" />

          <!-- 'cover.coverPhoto.url' field. -->
          <!-- The URL of the image. -->
          <field enabled="true" originalKey="Cover.CoverPhoto.Url" sitecoreKey="gg_CoverPhotoUrl" text="Cover Photo Url" />

          <!-- 'cover.coverPhoto.height' field. -->
          <!-- The height of the image. -->
          <field enabled="true" originalKey="Cover.CoverPhoto.Height" sitecoreKey="gg_CoverPhotoHeight" text="Cover Photo Height" />

          <!-- 'cover.coverPhoto.width' field. -->
          <!-- The width of the image. -->
          <field enabled="true" originalKey="Cover.CoverPhoto.Width" sitecoreKey="gg_CoverPhotoWidth" text="Cover Photo Width" />

          <!-- 'cover.coverInfo.topImageOffset' field. -->
          <!-- The difference between the top position of the cover image and the actual displayed cover image. Only valid for banner layout. -->
          <field enabled="true" originalKey="Cover.CoverInfo.TopImageOffset" sitecoreKey="gg_CoverInfoTopImageOffset" text="Cover Info Top Image Offset" />

          <!-- 'cover.coverInfo.leftImageOffset' field. -->
          <!-- The difference between the left position of the cover image and the actual displayed cover image. Only valid for banner layout. -->
          <field enabled="true" originalKey="Cover.CoverInfo.LeftImageOffset" sitecoreKey="gg_CoverInfoLeftImageOffset" text="Cover Info Left Image Offset" />

        </network>
      </profileKeyMappings>
    </social>
  </sitecore>
</configuration>