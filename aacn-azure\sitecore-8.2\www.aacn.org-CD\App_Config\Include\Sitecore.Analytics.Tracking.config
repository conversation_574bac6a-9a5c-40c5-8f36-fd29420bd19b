﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose: This include file configures the tracking subsystem of the Sitecore Experience Database (xDB).
The tracking subsystem is responsible for tracking of online visitors. The system tracks personalization, goals,
campaigns, profile values, patterns, multivariate tests, etc.

If you want to disable this functionality and prevent online tracking of visitors, you can rename this config file
so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!--  ANALYTICS AUTO DETECT BOTS
            Enable auto detection of bots.
            Default: true
      -->
      <setting name="Analytics.AutoDetectBots" value="true" />

      <!--  ANALYTICS CAMPAIGN QUERY STRING KEY
            Specifies the key for triggering campaigns on the query string.
            Default: sc_camp
      -->
      <setting name="Analytics.CampaignQueryStringKey" value="sc_camp" />

      <!--  ANALYTICS CLUSTER NAME
            Domain name of the web cluster this instance belongs to. 
            Default: <None>
      -->
      <setting name="Analytics.ClusterName" value="" />

      <!--  <PERSON>ALY<PERSON>CS CONTACT MAXIMUM INTERACTIONS 
            Maximum number of interactions in contact history that can be loaded
            by personalization components on website pages.
            Default: 0
      -->
      <setting name="Analytics.ContactHistory.MaxInteractions" value="0" />

      <!--  ANALYTICS EVENT QUERY STRING KEY
            Specifies the key for triggering events on the query string.
            Default: sc_trk
      -->
      <setting name="Analytics.EventQueryStringKey" value="sc_trk" />
      
      <!--  ANALYTICS FORWARDED REQUEST HTTP HEADER
            Specifies the name of an HTTP header variable containing the IP address of the webclient.
            Only for use behind load-balancers that mask web client IP addresses from webservers.
            IMPORTANT: If this setting is used incorrectly, it allows IP address spoofing.
            Typical values are "X-Forwarded-For" and "X-Real-IP".
            Default value: "" (disabled)
      -->
      <setting name="Analytics.ForwardedRequestHttpHeader" value="" />

      <!--  ANALYTICS HOST NAME
            Global domain name of the Sitecore website. 
            Default: <None>
      -->
      <setting name="Analytics.HostName" value="" />

      <!--  ANALYTICS LONG RUNNING PAGE REQUEST
            Defines the threshold for long running pages in milliseconds.
            Default: 5000
      -->
      <setting name="Analytics.LongRunningRequest" value="5000" />

      <!--  ANALYTICS ROBOTS IGNORE ROBOTS
            Ignore requests and do not write information to the Analytics database when the visitor 
            classification identifies the visitor as a robot.
            Default: true
      -->
      <setting name="Analytics.Robots.IgnoreRobots" value="true" />

      <!--  ANALYTICS ROBOTS SESSION TIMEOUT
            The ASP.NET Session Timeout for auto detected robots. 
            When the automatic robot detection identifies a session as being a robot, the ASP.NET
            Session Timeout is set to this value (in minutes).
            Default: 1
      -->
      <setting name="Analytics.Robots.SessionTimeout" value="1" />

      <!--  ANALYTICS SAMPLING PERCENTAGE
            Specifies the percentage of sessions that analytics data should be collected for.
            Default: 100 (collect data for all sessions)
      -->
      <setting name="Analytics.Sampling.Percentage" value="100" />
    </settings>

    <events>
      <event name="media:request">
        <handler type="Sitecore.Analytics.Media.MediaRequestEventHandler, Sitecore.Analytics" method="OnMediaRequest"/>
      </event>
    </events>


    <pipelines>
      
      <commitSession>
      </commitSession>

      <createTracker>
        <processor type="Sitecore.Analytics.Pipelines.CreateTracker.GetTracker, Sitecore.Analytics"/>
      </createTracker>

      <createVisit>
        <processor type="Sitecore.Analytics.Pipelines.CreateVisits.InitializeWithRequestData, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CreateVisits.XForwardedFor, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CreateVisits.UpdateGeoIpData, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CreateVisits.ParseReferrer, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CreateVisits.SetTrafficType, Sitecore.Analytics" />
      </createVisit>

      <endAnalytics>
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.CheckPreconditions, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.SetRobotSessionTimeOut, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.NormalizeVisit, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.SaveDuration, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.EndTracking, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EndAnalytics.ReleaseContact, Sitecore.Analytics" />
      </endAnalytics>

      <ensureClassification>
        <processor type="Sitecore.Analytics.Pipelines.EnsureClassification.CheckPreconditions, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureClassification.UpdateGeoIpData, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureClassification.GetClassificators, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureClassification.EnsureClassification, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureClassification.UpdateContactClassification, Sitecore.Analytics"/>
      </ensureClassification>

      <ensureSessionContext>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.CheckPreconditions, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.EnsureContext, Sitecore.Analytics">
          <SessionContextManager ref="tracking/sessionContextManager"></SessionContextManager>
        </processor>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.EnsureDevice, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.EnsureContactId, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.LoadContact, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.CreateContact, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.ClusterCheck, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.EnsureSessionContext.SetDummySession, Sitecore.Analytics">
          <MaxPageIndexThreshold>1000</MaxPageIndexThreshold>
        </processor>
        
      </ensureSessionContext>

      <excludeRobots>
        <processor type="Sitecore.Analytics.Pipelines.ExcludeRobots.TryObtainCachedResult, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ExcludeRobots.CheckUserAgent, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ExcludeRobots.CheckIpAddress, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ExcludeRobots.AddResultToCache, Sitecore.Analytics"/>
      </excludeRobots>

      <httpRequestEnd>
        <processor type="Sitecore.Analytics.Pipelines.HttpRequest.EndAnalytics, Sitecore.Analytics" patch:before="processor[@type='Sitecore.Pipelines.PreprocessRequest.CheckIgnoreFlag, Sitecore.Kernel']"/>
      </httpRequestEnd>

      <httpRequestProcessed>
        <processor type="Sitecore.Analytics.Pipelines.HttpRequest.EndAnalytics, Sitecore.Analytics"/>
      </httpRequestProcessed>

      <initialize>
        <processor type="Sitecore.Analytics.Pipelines.Loader.InitializeAnalytics, Sitecore.Analytics" />
      </initialize>

      <initializeTracker>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.IsMediaRequest, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.CreateVisit, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.CreatePage, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.EnsureNextPageData, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.Robots, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.EnsureContactClassification, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.InitializeTracker.RunRules, Sitecore.Analytics"/>
      </initializeTracker>

      <registerPageEvent>
        <processor type="Sitecore.Analytics.Pipelines.RegisterPageEvent.RunPageEventRules, Sitecore.Analytics"/>
        <processor type="Sitecore.Automation.MarketingAutomation.Pipelines.RegisterPageEvent.RunAutomation, Sitecore.Analytics.Automation"/>
      </registerPageEvent>

      <renderLayout>
        <processor type="Sitecore.Analytics.Pipelines.HttpRequest.StartAnalytics, Sitecore.Analytics" patch:after="processor[@type='Sitecore.Pipelines.RenderLayout.SecurityCheck, Sitecore.Kernel']" />
      </renderLayout>

      <sessionEnd>
        <processor type="Sitecore.Analytics.Pipelines.SessionEnd.InitializeTracker, Sitecore.Analytics" patch:before="*" />
      </sessionEnd>

      <startAnalytics>
        <processor type="Sitecore.Analytics.Pipelines.StartAnalytics.CheckPreconditions, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartAnalytics.CreateTracker, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartAnalytics.StartTracking, Sitecore.Analytics" />
      </startAnalytics>

      <startTracking>
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.RaiseStartTracking, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.InitializeTracker, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.TrackerInitialized, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.UpdateGeoIpData, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.ProcessQueryStringCampaign, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.ProcessQueryStringPageEvent, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.ProcessQueryStringTriggers, Sitecore.Analytics">
          <triggers hint="raw:AddTrigger">
            <trigger querystring="sc_rss" eventname="RSS"/>
          </triggers>
        </processor>
        <processor type="Sitecore.Analytics.Pipelines.StartTracking.ProcessItem, Sitecore.Analytics"/>
      </startTracking>

      <submitSessionContext>
      </submitSessionContext>

      <transferSession>
        <processor type="Sitecore.Analytics.Pipelines.TransferSession.TransferSessionToDifferentCluster, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.TransferSession.RedirectCurrentSession, Sitecore.Analytics" />
      </transferSession>
    </pipelines>
    
    <submitQueue>
      <queue type="Sitecore.Analytics.Data.DataAccess.SubmitQueue.FileSubmitQueue, Sitecore.Analytics" singleInstance="true" />
    </submitQueue>
    
    <tracking>
      <sessionContextManager type="Sitecore.Analytics.Data.HttpSessionContextManager, Sitecore.Analytics"  singleInstance="true"/>

      <contactManager type="Sitecore.Analytics.Tracking.ContactManager, Sitecore.Analytics" singleInstance="true">
        <param desc="sharedSessionStateManager" ref="tracking/sharedSessionState/manager" />

        <!-- The default contact repository is a stub that prevents the contact manager from reading and writing to the collection database.
             This behavior is overridden by the Sitecore.Analytics.Tracking.Database.config file.
        -->

        <param desc="contactRepository" ref="tracking/nullContactRepository" />
      </contactManager>

      <sharedSessionState defaultProvider="InProc">
        <providers>
          <clear/>
          <add name="InProc" type="System.Web.SessionState.InProcSessionStateStore" />
        </providers>

        <manager type="Sitecore.Analytics.Tracking.SharedSessionState.SharedSessionStateManager, Sitecore.Analytics">
          <param desc="configuration" ref="tracking/sharedSessionState/config" />
        </manager>

        <config type="Sitecore.Analytics.Tracking.SharedSessionState.SharedSessionStateConfig, Sitecore.Analytics">
          <param desc="maxLockAge">5000</param>
        </config>
      </sharedSessionState>
    </tracking>

  </sitecore>
</configuration>
