<?xml version="1.0" encoding="utf-8" ?>

<!--

Purpose: This include file configures Experience Profile. The file is mandatory for Experience Profile to function correctly.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <initialize>
        
        <!-- Creates a set of http endpoints that expose functionality of Experience Profile. -->        
        <processor type="Sitecore.Cintel.Endpoint.Plumbing.InitializeRoutes, Sitecore.Cintel" patch:after="*[last()]" />
      </initialize>

      <!-- Adds a reporting data source which is specific to Experience Profile.
           Datasource receives a name of query pipeline which is then executed on the reporting server. -->
      <registerReportingDataSource>
        <processor type="Sitecore.Analytics.Pipelines.RegisterReportingDataSource.RegisterReportingDataSourceProcessor, Sitecore.Analytics" >
          <datasources hint="list:AddDatasource">
            <add key="ExperienceProfileQueryPipeline" type="Sitecore.Cintel.Reporting.ReportingServerDatasource.QueryPipelineDataSource, Sitecore.Cintel"/>
          </datasources>
        </processor>
      </registerReportingDataSource>
    </pipelines>

    <experienceProfile>
      
      <!-- IDs for default images stored in content tree. -->
      <defaultImages>
        <contactProfileImageId id="{76807981-28BA-49FF-BAA2-9DC1C40F2F73}" />
        <pageEventImageId id="{E535D1CA-B43D-40DF-AE02-11A609F19781}" />
        <patternCardImageId id="{8ABB6C19-F381-4469-88FB-567E7D141F44}" />

        <channelTypeImageId id="{FF2C71F4-7675-4619-8783-F65F1041D3A1}" />
        <channelImageId id="{FF2C71F4-7675-4619-8783-F65F1041D3A1}" />
        <outcomeImageId id="{9D8FB21E-6036-4BB9-935D-52D39ED9FB73}" />
      </defaultImages>
      
      <providers>
        
        <!-- Provides view pipelines. Pipelines are defined in Sitecore.ExperienceProfile.Reporting.config file. -->
        <viewProvider type="Sitecore.Cintel.Reporting.PipelineViewProvider" singleInstance="true" />

        <!-- Provides marketing definition items for profiles, channels, outcomes, etc. -->
        <cmsMasterDataService type="Sitecore.Cintel.CmsDataService.CmsMasterDataService, Sitecore.Cintel" singleInstance="true" />

        <!-- Provides a way to retrieve contact and contact facets. -->
        <contactService type="Sitecore.Cintel.ContactService.XdbContactService, Sitecore.Cintel" singleInstance="true" />
        
        <!-- Provides contact search functionality. -->
        <contactSearchProvider type="Sitecore.Cintel.ContactSearchProvider, Sitecore.Cintel" singleInstance="true" />
         
      </providers>
      
      <!-- Default contact facets used implementation of contactService. -->
      <defaultFacets>
        <PersonalInfo name="Personal" />
        <AddressList name="Addresses" />
        <EmailList name="Emails" />
        <PhoneNumberList name="Phone Numbers" />
        <Picture name="Picture" />
      </defaultFacets>

      <viewConfiguration>
        <!-- Indicates if channel names should include channel code, e.g. Online/Website/Brand website (41/70/01).
             The setting is used by views having channel name field. -->
        <channelDisplayNameIncludesCode value="true" />
        
        <!-- Specifies separator for in taxon uris, e.g. Online/Website/Brand website -->
        <taxonUriSeparator value="/" />
      </viewConfiguration>
      
      <!-- Name of Sitecore search index that is used for contact search. -->
      <searchIndex name="sitecore_analytics_index" />
      
    </experienceProfile>
  </sitecore>
</configuration>