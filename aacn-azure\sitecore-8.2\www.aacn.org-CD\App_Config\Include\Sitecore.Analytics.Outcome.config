﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the Outcome tracking and management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <initialize>
        <processor type="Sitecore.Analytics.Outcome.Pipelines.Initialize.RegisterDataModelExtensions, Sitecore.Analytics.Outcome" />
      </initialize>

      <submitContact>
        <processor type="Sitecore.Analytics.Outcome.Pipelines.SubmitContact.SaveOutcomesProcessor, Sitecore.Analytics.Outcome">
          <OutcomeManager ref="outcome/outcomeManager" />
        </processor>
      </submitContact>
    </pipelines>

    <outcome>
      <outcomeManager type="Sitecore.Analytics.Outcome.OutcomeManager, Sitecore.Analytics.Outcome" singleInstance="true">
        <param desc="repository" type="Sitecore.Analytics.Outcome.Data.MongoDbOutcomeRepository, Sitecore.Analytics.Outcome">
          <param name="connectionStringName">analytics</param>
        </param>
      </outcomeManager>


    </outcome>
  </sitecore>
</configuration>