resource "azurerm_storage_account" "storage" {
  name                     = var.storage_account_name
  resource_group_name      = var.resource_group_name
  location                 = var.location
  account_tier             = "Standard"
  account_replication_type = "LRS"
  tags = var.tags
  azure_files_authentication {
    directory_type = "AADDS"
  }
}

# resource "azurerm_storage_container" "tfstate" {
#   name                  = var.container_name
#   storage_account_name  = azurerm_storage_account.storage.name
#   container_access_type = "private"
# }

# resource "azurerm_storage_container" "containers" {
#   for_each             = toset(var.container_name)
#   name                 = each.value
#   storage_account_name = azurerm_storage_account.storage.name
#   container_access_type = "private"
# }
