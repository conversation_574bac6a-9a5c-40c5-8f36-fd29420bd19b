﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <overrideXmlControls>
      <override xmlControl="Sitecore.Shell.Applications.Media.MediaBrowser" with="/sitecore/client/applications/Dialogs/SelectMediaDialog" />
    </overrideXmlControls>

    <overrideDialogs>
      <override dialogUrl="/sitecore/shell/Applications/Dialogs/Internal%20link.aspx" with="/sitecore/client/applications/dialogs/InsertLinkViaTreeDialog" />
      <override dialogUrl="/sitecore/shell/Applications/Dialogs/Mail%20link.aspx" with="/sitecore/client/applications/dialogs/InsertEmailDialog" />
      <override dialogUrl="/sitecore/shell/Applications/Dialogs/Anchor%20link.aspx" with="/sitecore/client/applications/dialogs/InsertAnchorDialog" />
      <override dialogUrl="/sitecore/shell/Applications/Item%20browser.aspx" with="/sitecore/client/applications/dialogs/InsertSitecoreItemViaTreeDialog" />
      <override dialogUrl="/sitecore/shell/Applications/Control%20panel.aspx" with="/sitecore/client/Applications/ControlPanel" />
    </overrideDialogs>

    <settings>
     
    </settings>

 
    <pipelines>
      <!-- Http Request -->
      <preprocessRequest>
        <processor type="Sitecore.Pipelines.HttpRequest.OverrideDialogs, Sitecore.Speak.Applications" />
        <processor type="Sitecore.Pipelines.HttpRequest.OverrideXmlControl, Sitecore.Speak.Applications" />
      </preprocessRequest>

      <speak.logout argsType="Sitecore.Pipelines.Logout.LogoutArgs">
        <processor mode="on" type="Sitecore.Pipelines.Logout.ClearCache, Sitecore.Kernel"/>        
        <processor mode="on" type="Sitecore.Pipelines.Logout.ClearSession, Sitecore.Kernel"/>
        <processor mode="on" type="Sitecore.Pipelines.Logout.RemoveTicket, Sitecore.Kernel"/>        
      </speak.logout>
      
    </pipelines>

   
  </sitecore>
</configuration>