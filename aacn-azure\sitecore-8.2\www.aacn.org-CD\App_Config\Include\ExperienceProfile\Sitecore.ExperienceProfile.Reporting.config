﻿<?xml version="1.0" encoding="utf-8" ?>

<!--

Purpose: This include file configures views in Experience Profile.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      
      <!-- Defines pipelines for views.
           Each section contains a pipeline which typically contains steps for
           constructing result data table, querying data source, getting meta data from Master DB, sorting, paging.
           Views are always executed for a given contact, i.e. contact id is mandatory parameter. -->
      <group groupName="ExperienceProfileContactViews">
        <pipelines>

          <visits>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.ConstructVisitsDataTable, Sitecore.Cintel" />            
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>            
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.PopulateVisitsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.ApplyMasterDataToVisits, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </visits>

          
          <journey>
            <processor type="Sitecore.Cintel.Reporting.Contact.Journey.Processors.ConstructJourneyDataTable, Sitecore.Cintel"/>           
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>            
            <processor type="Sitecore.Cintel.Reporting.Contact.Journey.Processors.PopulateJourneyWithInteractionsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Journey.Processors.PopulateJourneyWithContactOutcomes, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Journey.Processors.PopulateEraChanges, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.Journey.Processors.PrepareOrdering, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </journey>

          
          <journey-detail-outcome>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOutcomeDetail.Processors.ConstructJourneyOutcomeDetailDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOutcomeDetail.Processors.PopulateJourneyOutcomeDetailWithOutcomeData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-channel</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOutcomeDetail.Processors.PopulateJourneyOutcomeDetailWithInteractionData, Sitecore.Cintel"/>
          </journey-detail-outcome>

          
          <journey-detail-online-interaction>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOnlineInteractionDetail.Processors.ConstructJourneyOnlineInteractionDetailDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOnlineInteractionDetail.Processors.PopulateJourneyOnlineInteractionWithXdbData, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOnlineInteractionDetail.Processors.ApplyMasterDataToJourneyOnlineInteractionDetail, Sitecore.Cintel" />
          </journey-detail-online-interaction>


          <journey-detail-offline-interaction>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOfflineInteractionDetail.Processors.ConstructJourneyOfflineInteractionDetailDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOfflineInteractionDetail.Processors.PopulateJourneyOfflineInteractionWithXdbData, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.JourneyOfflineInteractionDetail.Processors.ApplyMasterDataToJourneyOfflineInteractionDetail, Sitecore.Cintel" />
          </journey-detail-offline-interaction>


          <channel-summary>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelSummary.Processors.ConstructChannelListDataTable, Sitecore.Cintel"/>       
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelSummary.Processors.PopulateChannelListWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelSummary.Processors.ApplyMasterDataToChannels, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </channel-summary>

          
          <channel-goal-distribution>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelDistribution.Processors.ConstructChannelDistributionDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelDistribution.Views.GoalsForChannels.Processors.PopulateGoalsForChannelsWithXdbData, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
          </channel-goal-distribution>

          
          <channel-interaction-distribution>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelDistribution.Processors.ConstructChannelDistributionDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ChannelDistribution.Views.InteractionsForChannels.Processors.PopulateInteractionForChannelsWithXdbData, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
          </channel-interaction-distribution>

          
          <visit-summary>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.ConstructVisitsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.ConstructVisitsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.PopulateVisitsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Visit.Processors.ApplyMasterDataToVisits, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </visit-summary>

          
          <latest-statistics>
            <processor type="Sitecore.Cintel.Reporting.Contact.LatestStatistics.Processors.ConstructLatestStatisticsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">contact-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.LatestStatistics.Processors.PopulateContactStatistics, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.LatestStatistics.Processors.PopulateVisitStatistics, Sitecore.Cintel" />
          </latest-statistics>

          
          <visit-pages>
            <processor type="Sitecore.Cintel.Reporting.Contact.Page.Processors.ConstructVisitPagesDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">pages-by-visit-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Page.Processors.PopulateVisitPagesWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Page.Processors.ApplyMasterDataToVisitPages, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </visit-pages>

          
          <visit-goals>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.ConstructGoalsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">goals-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.PopulateGoalsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Views.GoalsForVisit.Processors.FilterGoalsOnVisitId, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.ApplyMasterDataToGoals, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </visit-goals>

          
          <internal-keyword-summary>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordSummary.Processors.ConstructInternalKeywordSummaryDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordSummary.Processors.PopulateInternalKeywordSummariesWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </internal-keyword-summary>

          
          <internal-keyword-detail>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.ConstructInternalKeywordDataTable,Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.PopulateInternalKeywordDetailsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.FilterInternalKeywordDetailsBasedOnSearchTerm, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.FilterInternalKeywordDetailsBasedOnInternalEventId, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </internal-keyword-detail>

          
          <visit-internal-searches>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.ConstructInternalKeywordDataTable,Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.PopulateInternalKeywordDetailsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.InternalKeywordDetail.Processors.FilterInternalKeywordDetailsBasedOnVisitId, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </visit-internal-searches>

          
          <paid-keyword-summary>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.ConstructExternalKeywordSummaryDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.PopulateExternalKeywordSummariesWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.FilterExternalKeywordSummariesBasedOnChannel, Sitecore.Cintel">
              <channels hint="list:AddChannel">
                <!--Online/PPC Advertising-->
                <channelId>{B55EC2C2-CD7A-4E03-B155-EEFDAE872B7D}</channelId>
                <channelId>{EC74A09A-7FA7-4A5A-874A-6B5CA9E05773}</channelId>
                <channelId>{67150678-B200-44BB-BBAE-1D7B901D0860}</channelId>
                <channelId>{145304B1-C2E0-4B88-A200-D5AA18206CF7}</channelId>
                <channelId>{B5234879-DFFC-47AF-8267-59D4D3DF6226}</channelId>
                <channelId>{0D600460-DA2E-41A6-955A-9253D2A063C2}</channelId>

                <!--Online/Display-->
                <channelId>{030BDC09-7720-4EC2-B778-EB0BC7FD13BC}</channelId>
                <channelId>{5783355A-F9E4-476E-BA9D-9620E3E452EB}</channelId>
                <channelId>{829FAEB1-1393-44DA-B04A-B5C01986610A}</channelId>
                <channelId>{A43E7EB6-8610-4DF7-BD6B-FEE899743D30}</channelId>
                <channelId>{4A0B6C13-EFC8-4A00-8A6A-B709A89B1E11}</channelId>
              </channels>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </paid-keyword-summary>

          
          <external-keyword-summary>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.ConstructExternalKeywordSummaryDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.PopulateExternalKeywordSummariesWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordSummary.Processors.FilterExternalKeywordSummariesBasedOnChannel, Sitecore.Cintel">
              <channels hint="list:AddChannel">
                <!--Online/Organic Search-->
                <channelId>{5854B151-1555-49FD-8F35-D40E446BEB3C}</channelId>
                <channelId>{B2747066-06F8-4E0B-9EA7-64D8859A119A}</channelId>
                <channelId>{B979A670-5AAF-4E63-94AC-C3C3E5BFBE84}</channelId>
                <!--Online/Direct/Search Engine Branded-->
                <channelId>{FB8FA660-0A07-4EE9-A9F4-930FC5D10AEC}</channelId>          
              </channels>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </external-keyword-summary>

          
          <paid-keyword-detail>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.ConstructExternalKeywordDetailsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.PopulateExternalKeywordDetailsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsBasedOnChannel, Sitecore.Cintel">
              <channels hint="list:AddChannel">
                <!--Online/PPC Advertising-->
                <channelId>{B55EC2C2-CD7A-4E03-B155-EEFDAE872B7D}</channelId>
                <channelId>{EC74A09A-7FA7-4A5A-874A-6B5CA9E05773}</channelId>
                <channelId>{67150678-B200-44BB-BBAE-1D7B901D0860}</channelId>
                <channelId>{145304B1-C2E0-4B88-A200-D5AA18206CF7}</channelId>
                <channelId>{B5234879-DFFC-47AF-8267-59D4D3DF6226}</channelId>
                <channelId>{0D600460-DA2E-41A6-955A-9253D2A063C2}</channelId>

                <!--Online/Display-->
                <channelId>{030BDC09-7720-4EC2-B778-EB0BC7FD13BC}</channelId>
                <channelId>{5783355A-F9E4-476E-BA9D-9620E3E452EB}</channelId>
                <channelId>{829FAEB1-1393-44DA-B04A-B5C01986610A}</channelId>
                <channelId>{A43E7EB6-8610-4DF7-BD6B-FEE899743D30}</channelId>
                <channelId>{4A0B6C13-EFC8-4A00-8A6A-B709A89B1E11}</channelId>
              </channels>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsForGivenChannel, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsBasedOnSource, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </paid-keyword-detail>

          
          <external-keyword-detail>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.ConstructExternalKeywordDetailsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visits-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.PopulateExternalKeywordDetailsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsBasedOnChannel, Sitecore.Cintel">
              <channels hint="list:AddChannel">
                <!--Online/Organic Search-->
                <channelId>{5854B151-1555-49FD-8F35-D40E446BEB3C}</channelId>
                <channelId>{B2747066-06F8-4E0B-9EA7-64D8859A119A}</channelId>
                <channelId>{B979A670-5AAF-4E63-94AC-C3C3E5BFBE84}</channelId>
                <!--Online/Direct/Search Engine Branded-->
                <channelId>{FB8FA660-0A07-4EE9-A9F4-930FC5D10AEC}</channelId>    
              </channels>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsForGivenChannel, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.ExternalKeywordDetail.Processors.FilterExternalKeywordDetailsBasedOnSource, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </external-keyword-detail>

          
          <campaigns>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.ConstructCampaignsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">campaigns-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.PopulateCampaignsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.ApplyMasterDataToCampaigns, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </campaigns>

          
          <recent-campaigns>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.ConstructCampaignsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">campaigns-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.PopulateCampaignsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Campaign.Processors.ApplyMasterDataToCampaigns, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </recent-campaigns>

          
          <goals>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.ConstructGoalsDataTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">goals-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.PopulateGoalsWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Goal.Processors.ApplyMasterDataToGoals, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </goals>

          
          <profile-pattern-matches>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfilePatternMatch.Processors.ConstructProfilePatternMatchTable, Sitecore.Cintel"/>

            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitPatternScores.PrepareProfileKeyMapping, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-pattern-scores</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfilePatternMatch.Processors.PopulateProfilePatternMatchesWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </profile-pattern-matches>

          
          <profile-info>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.ConstructProfileInfoTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitScoringProfiles.PrepareProfileMapping, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-scoring-profiles</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.FindMostRecentVisitPerProfileAndProjectToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.FindBestPatternMatchAndApplyToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.ApplyMasterDataToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </profile-info>

          
          <best-pattern-matches>
            <!-- This pipeline is intentionally identical to profile-info -->
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.ConstructProfileInfoTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitScoringProfiles.PrepareProfileMapping, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-scoring-profiles</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.FindMostRecentVisitPerProfileAndProjectToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.FindBestPatternMatchAndApplyToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileInfo.Processors.ApplyMasterDataToProfileInfo, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </best-pattern-matches>

          
          <profiling-profile-key-values>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitPatternScores.PrepareProfileKeyMapping, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-pattern-scores</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileKeyScore.Processors.ConstructProfileKeyScoreTable, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileKeyScore.Processors.PopulateProfileKeyScoresWithXdbData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.ProfileKeyScore.Processors.ApplyMasterDataToProfileKeyScores, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </profiling-profile-key-values>

          
          <automations>
            <processor type="Sitecore.Cintel.Reporting.Contact.Automation.Processors.ConstructAutomationsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.Automation.Processors.PopulateAutomationsWithContactsCurrentStates, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Automation.Processors.ApplyMasterDataToAutomations, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </automations>

          
          <latest-events>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.ConstructEventsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateEventsWithExternalKeywordsFromXdb, Sitecore.Cintel">
              <channels hint="list:SetExternalChannels">
                <!--Online/Organic Search-->
                <channelId>{5854B151-1555-49FD-8F35-D40E446BEB3C}</channelId>
                <channelId>{B2747066-06F8-4E0B-9EA7-64D8859A119A}</channelId>
                <channelId>{B979A670-5AAF-4E63-94AC-C3C3E5BFBE84}</channelId>
                <!--Online/Direct/Search Engine Branded-->
                <channelId>{FB8FA660-0A07-4EE9-A9F4-930FC5D10AEC}</channelId>
              </channels>
              
              <channels hint="list:SetPaidChannels">
                <!--Online/PPC Advertising-->
                <channelId>{B55EC2C2-CD7A-4E03-B155-EEFDAE872B7D}</channelId>
                <channelId>{EC74A09A-7FA7-4A5A-874A-6B5CA9E05773}</channelId>
                <channelId>{67150678-B200-44BB-BBAE-1D7B901D0860}</channelId>
                <channelId>{145304B1-C2E0-4B88-A200-D5AA18206CF7}</channelId>
                <channelId>{B5234879-DFFC-47AF-8267-59D4D3DF6226}</channelId>
                <channelId>{0D600460-DA2E-41A6-955A-9253D2A063C2}</channelId>

                <!--Online/Display-->
                <channelId>{030BDC09-7720-4EC2-B778-EB0BC7FD13BC}</channelId>
                <channelId>{5783355A-F9E4-476E-BA9D-9620E3E452EB}</channelId>
                <channelId>{829FAEB1-1393-44DA-B04A-B5C01986610A}</channelId>
                <channelId>{A43E7EB6-8610-4DF7-BD6B-FEE899743D30}</channelId>
                <channelId>{4A0B6C13-EFC8-4A00-8A6A-B709A89B1E11}</channelId>
              </channels>
            </processor>

            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithNoSearchHitsFoundEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithSearchEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateFormEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithPageEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Views.LatestEvents.Processors.FilterEventsToGetOnlyLatestEvents, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.UpdateAllEventsWithMasterData, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Views.LatestEvents.Processors.PopulateWithLatestOutcome, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Views.LatestEvents.Processors.InsertLatestVisitOnTop, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </latest-events>

          
          <events>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.ConstructEventsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ExecuteReportingServerDatasourceQuery, Sitecore.Cintel">
              <param desc="queryName">visit-events-query</param>
            </processor>

            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateEventsWithExternalKeywordsFromXdb, Sitecore.Cintel">
              <channels hint="list:SetExternalChannels">
                <!--Online/Organic Search-->
                <channelId>{5854B151-1555-49FD-8F35-D40E446BEB3C}</channelId>
                <channelId>{B2747066-06F8-4E0B-9EA7-64D8859A119A}</channelId>
                <channelId>{B979A670-5AAF-4E63-94AC-C3C3E5BFBE84}</channelId>
                <!--Online/Direct/Search Engine Branded-->
                <channelId>{FB8FA660-0A07-4EE9-A9F4-930FC5D10AEC}</channelId>
              </channels>

              <channels hint="list:SetPaidChannels">
                <!--Online/PPC Advertising-->
                <channelId>{B55EC2C2-CD7A-4E03-B155-EEFDAE872B7D}</channelId>
                <channelId>{EC74A09A-7FA7-4A5A-874A-6B5CA9E05773}</channelId>
                <channelId>{67150678-B200-44BB-BBAE-1D7B901D0860}</channelId>
                <channelId>{145304B1-C2E0-4B88-A200-D5AA18206CF7}</channelId>
                <channelId>{B5234879-DFFC-47AF-8267-59D4D3DF6226}</channelId>
                <channelId>{0D600460-DA2E-41A6-955A-9253D2A063C2}</channelId>

                <!--Online/Display-->
                <channelId>{030BDC09-7720-4EC2-B778-EB0BC7FD13BC}</channelId>
                <channelId>{5783355A-F9E4-476E-BA9D-9620E3E452EB}</channelId>
                <channelId>{829FAEB1-1393-44DA-B04A-B5C01986610A}</channelId>
                <channelId>{A43E7EB6-8610-4DF7-BD6B-FEE899743D30}</channelId>
                <channelId>{4A0B6C13-EFC8-4A00-8A6A-B709A89B1E11}</channelId>
              </channels>
            </processor>

            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithCampaignsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithNoSearchHitsFoundEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithSearchEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateFormEventsFromXdb, Sitecore.Cintel"/>

            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.PopulateAllEventsWithPageEventsFromXdb, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Views.AllEvents.Processors.FilterEventsToGetOnlySelectedPageEvents, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Contact.Event.Processors.UpdateAllEventsWithMasterData, Sitecore.Cintel"/>

            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </events>

          
          <outcome-detail>
            <processor type="Sitecore.Cintel.Reporting.Contact.OutcomeDetail.Processors.ConstructOutcomeDetailDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.OutcomeDetail.Processors.PopulateOutcomesFromXdb, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Contact.OutcomeDetail.Processors.ApplyMasterDataToOutcomeDetail, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </outcome-detail>

          
        </pipelines>
      </group>

      <!-- Defines data source queries used by view pipelines. -->
      <group groupName="ExperienceProfileContactDataSourceQueries">
        <pipelines>
          <contact-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.ContactSystemInfo.GetSingleContactSystemInfo, Sitecore.Cintel"/>
          </contact-query>
          <visit-pattern-scores>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitPatternScores.RetrievePatternScoresForInteraction, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitPatternScores.ProjectPatternScoresToRowBasedForm, Sitecore.Cintel"/>
          </visit-pattern-scores>
          <visit-scoring-profiles>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitScoringProfiles.RetrieveScoringProfiles, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitScoringProfiles.ProjectScoringProfilesToRowBasedForm, Sitecore.Cintel"/>
          </visit-scoring-profiles>
          <visit-events-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitEvents.GetPages, Sitecore.Cintel"/>
          </visit-events-query>
          <visits-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.Visits.GetVisitsWithLocations, Sitecore.Cintel"/>
          </visits-query>
          <campaigns-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.Campaigns.GetCampaigns, Sitecore.Cintel"/>
          </campaigns-query>
          <pages-by-visit-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.VisitPages.GetPages, Sitecore.Cintel"/>
          </pages-by-visit-query>
          <goals-query>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.Goals.GetGoals, Sitecore.Cintel"/>
          </goals-query>
          <visit-channel>
            <processor type="Sitecore.Cintel.Reporting.ReportingServerDatasource.Channel.GetChannelForVisit, Sitecore.Cintel" />
          </visit-channel>
        </pipelines>
      </group>

      
      <!-- Defines pipelines for aggregate views, i.e. views that show data for more than one particular contact. -->
      <group groupName="ExperienceProfileAggregateViews">
        <pipelines>
          <latest-visitors>
            <processor type="Sitecore.Cintel.Reporting.Aggregate.Visitors.Processors.ConstructVisitorsDataTable, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Aggregate.Visitors.Processors.QueryLatestVisitorsFromSearch, Sitecore.Cintel" />
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplySorting, Sitecore.Cintel"/>
            <processor type="Sitecore.Cintel.Reporting.Processors.ApplyPaging, Sitecore.Cintel"/>
          </latest-visitors>
        </pipelines>
      </group>
      
    </pipelines>
  </sitecore>
</configuration>