﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file contains declarations of server requests for Sitecore Experience Explorer.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <sitecore.experienceeditor.speak.requests>
      <!-- Explorer mode Start -->
      <request name="ExperienceExplorer.ExplorerMode.CanExplore" type="Sitecore.ExperienceExplorer.Web.Requests.CanExplore, Sitecore.ExperienceExplorer.Web" />
      <request name="ExperienceExplorer.ExplorerMode.Explore" type="Sitecore.ExperienceExplorer.Web.Requests.ExplorerMode, Sitecore.ExperienceExplorer.Web" />
      <!-- Explorer mode End -->
    </sitecore.experienceeditor.speak.requests>
  </sitecore>
</configuration>




