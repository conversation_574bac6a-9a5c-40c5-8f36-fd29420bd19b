﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <analyticsExcludeRobots>
      <!--List of user agent strings that are used by search engines. Requests with following user agent strings will be 
      ignored by Analytics.
      See http://iplists.com/ for updates
      Use # for comments
      -->
      <excludedUserAgents>
        AdsBot-Google (+http://www.google.com/adsbot.html)
        Googlebot-Image/1.0
        Googlebot/2.1 (+http://www.googlebot.com/bot.html)
        Googlebot/Test (+http://www.googlebot.com/bot.html)
        Googlebot/Test
        Mediapartners-Google/2.1 (+http://www.googlebot.com/bot.html)
        Mediapartners-Google/2.1
        Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)
        gsa-crawler (Enterprise; S4-E9LJ2B82FJJAA; <EMAIL>)
        Fast Crawler v X
        Fast Crawler v X(compatible; Konqueror/3.2; FreeBSD) (KHTML, like Gecko)
        Mozilla/4.0 (compatible; MSIE 5.0; Windows NT)
        Mozilla/4.0
        Mozilla/4.05 [en]
        Mozilla/5.0 (Slurp/cat; <EMAIL>; http://www.inktomi.com/slurp.html)
        Mozilla/5.0 (compatible; Yahoo! DE Slurp; http://help.yahoo.com/help/us/ysearch/slurp)
        Mozilla/5.0 (compatible; Yahoo! Slurp China; http://misc.yahoo.com.cn/help.html)
        Mozilla/5.0 (compatible; Yahoo! Slurp/3.0; http://help.yahoo.com/help/us/ysearch/slurp)
        Mozilla/5.0 (compatible; Yahoo! Slurp; http://help.yahoo.com/help/us/ysearch/slurp)
        Scooter/3.3
        Y!J-BSC/1.0 (http://help.yahoo.co.jp/help/jp/blog-search/)
        Yahoo! Mindset
        Yahoo-Blogs/v3.9 (compatible; Mozilla 4.0; MSIE 5.5; http://help.yahoo.com/help/us/ysearch/crawling/crawling-02.html )
        Yahoo-MMAudVid/1.0 (mms dash mmaudvidcrawler dash support at yahoo dash inc dot com)
        Yahoo-MMCrawler/3.x (mms dash mmcrawler dash support at yahoo dash inc dot com)
        YahooFeedSeeker/1.0 (compatible; Mozilla 4.0; MSIE 5.5; my.yahoo.com/s/publishers.html)
        YahooSeeker-Testing/v3.9 (compatible; Mozilla 4.0; MSIE 5.5; http://search.yahoo.com/)
        YahooSeeker/1.1 (compatible; Mozilla 4.0; MSIE 5.5; http://help.yahoo.com/help/us/shop/merchant/)
        YahooSeeker/1.2 (compatible; Mozilla 4.0; MSIE 5.5; yahooseeker at yahoo-inc dot com ; http://help.yahoo.com/help/us/shop/merchant/)
        YahooSeeker/CafeKelsa-dev (compatible; Konqueror/3.2; FreeBSD ;<EMAIL> ) (KHTML, like Gecko)
        YahooVideoSearch www.yahoo.com/
        YahooYSMcm/2.0.0
        slurp
        Lycos_Spider_(modspider)
        Infoseek SideWinder/2.0B (Linux 2.4 i686)
        Scooter/3.3Y!CrawlX
        123metaspider-Bot (Version: 1.04, powered by www.123metaspider.com)
        Mozilla/4.0 (ActiveTouristBot;http://www.activetourist.com)
        Mozilla/4.0 (JemmaTheTourist;http://www.activtourist.com <EMAIL>)
        Mozilla/4.0 (JemmaTheTourist;http://www.activtourist.com)
        Marvin v0.3
        Mozilla/4.0 (agadine3.0) www.agada.de
        123metaspider-Bot (Version: 1.04, powered by www.123metaspider.com)
        Mozilla/4.0 (ActiveTouristBot;http://www.activetourist.com)
        Mozilla/4.0 (JemmaTheTourist;http://www.activtourist.com <EMAIL>)
        Mozilla/4.0 (JemmaTheTourist;http://www.activtourist.com)
        Marvin v0.3
        Mozilla/4.0 (agadine3.0) www.agada.de
        Amfibibot/0.06 (Amfibi Web Search; http://www.amfibi.com; <EMAIL>)
        Amfibibot/0.07 (Amfibi Robot; http://www.amfibi.com; <EMAIL>)
        ano-kato.com v2.14 spider
        AnswerBus (http://www.answerbus.com/)
        Apexoo Spider 1.0
        Apexoo Spider (http://www.apexoo.com/spider/)
        Mozilla/2.0 (compatible; Ask Jeeves/Teoma; +http://sp.ask.com/docs/about/tech_crawling.html)
        Mozilla/2.0 (compatible; AskJeeves/Teoma)
        Mozilla/5.0 (compatible; Ask Jeeves/Teoma; +http://about.ask.com/en/docs/about/webmasters.shtml)
        teoma_agent1
        AtlocalBot/1.1 +(http://www.atlocal.com/local-web-site-owner.html)
        axadine/  (Axadine Crawler; http://www.axada.de/;  )
        Fred/0.01-dev (Fred; http://www.ay-up.com; <EMAIL>)
        Fred/0.05 (Fred; http://www.ay-up.com; <EMAIL>)
        BaiDuSpider
        Baiduspider+(+http://www.baidu.com/search/spider.htm)
        Baiduspider+(+http://www.baidu.com/search/spider_jp.html)
        Mozilla/5.0 (compatible; BecomeBot/2.0.3; +http://www.become.com/webmasters.html)
        Mozilla/5.0 (compatible; becomebot/2.0beta; +http://www.become.com/webmasters.html)
        Mozilla/5.0 (compatible; becomebot/1.23; +http://www.become.com/webmasters.html)
        Mozilla/4.7 [en](BecomeBot'at'exava.com)
        Mozilla/5.0 (compatible; BecomeBot/2.2.5; MSIE 6.0 compatible; +http://www.become.com/site_owners.html)
        BigCliqueBOT/1.03-dev (bigclicbot; http://www.bigclique.com; <EMAIL>)
        Bigsearch.ca/Nutch-0.9-dev (Bigsearch.ca Internet Spider; http://www.bigsearch.ca/; <EMAIL>)
        BilgiBot/1.0(beta) (http://www.bilgi.com/; bilgi at bilgi dot com)
        Bloglines Title Fetch/1.0 (http://www.bloglines.com)
        boitho.com-dc/0.4 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.5 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.52 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.57 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.58 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.60 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.66 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.71 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.72 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.75 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.81 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.82 ( http://www.boitho.com/dcbot.html )
        boitho.com-dc/0.83 ( http://www.boitho.com/dcbot.html )
        boitho.com-robot/1.1
        BTbot/0.3 (+http://www.btbot.com/btbot.html)
        btbot/0.4 ( http://www.btbot.com/btbot.html)
        Norbert the Spider(Burf.com)
        BurstFind Crawler 1.0/0.7.1 (Nutch; http://lucene.apache.org/nutch/bot.html; <EMAIL>)
        BurstFindCrawler/1.1 (crawler.burstfind.com; http://crawler.burstfind.com; <EMAIL>)
        RoboCrawl (www.canadiancontent.net)
        holmes/3.8 (morfeo.centrum.cz/bot)
        CipinetBot (http://www.cipinet.com/bot.html)
        Mozilla/5.0 (Clustered-Search-Bot/1.0; <EMAIL>; http://www.clush.com/)
        Clushbot/2.1 (+http://www.clush.com/bot.html)
        Clushbot/3.62-Laomedon (+http://www.clush.com/bot.html)
        CrawlConvera0.1 <EMAIL>
        ConveraCrawler/0.7 (+http://www.authoritativeweb.com/crawl)
        ConveraCrawler/0.9d (+http://www.authoritativeweb.com/crawl)
        Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; MSIECrawler)
        Crawlzilla/1.0 (Crawlzilla; http://www.crawlzilla.com; <EMAIL>)
        CydralSpider/1.8 (Cydral Web Image Search; http://www.cydral.com)
        Orbiter/T-2.0 (+http://www.dailyorbit.com/bot.htm)
        Orbiter (+http://www.dailyorbit.com/bot.htm)
        DeepIndex
        DeepIndex (www.deepindex.com)
        -DIE-KRAEHE- META-SEARCH-ENGINE/1.0 http://www.die-kraehe.com
        FlickBot 2.0 RPT-HTTPClient/0.3-3
        Drecombot/1.0 (http://career.drecom.jp/bot.html)
        Dumbot(version 0.1 beta - dumbfind.com)
        fscrawler/3.0 (www.e-dintorni.it;Windows NT 5.0)
        EgotoBot/4.8 (+http://www.egoto.com/about.htm)
        NextGenSearchBot 1 (for information visit http://www.eliyon.com/NextGenSearchBot)
        EngineBot/3.0
        arachna_enginebot/v3.0[http://www.theenginerooms.com]
        Speedy_Spider (http://www.entireweb.com)
        Speedy Spider (Beta/1.0; <EMAIL>)
        Speedy Spider (Entireweb; Beta/1.0; http://www.entireweb.com/about/search_tech/speedyspider/)
        Speedy Spider (http://www.entireweb.com/about/search_tech/speedy_spider/)
        envolk[ITS]spider/1.6(+http://www.envolk.com/envolkspider.html)
        envolk/1.7 (+http://www.envolk.com/envolkspiderinfo.php)
        e-SocietyRobot(http://www.yama.info.waseda.ac.jp/~yamana/es/)
        Evaal/0.7.1 (Evaal; http://search.evaal.com/bot.html; <EMAIL>)
        eseek-larbin_2.6.2 <EMAIL>
        exactseek-pagereaper-2.63 <EMAIL>
        ExactSeek_Spider
        Mozilla/4.0
        exactseek.com
        Exalead NG/MimeLive Client (convert/http/0.120)
        NG/2.0
        Exabot NG/MimeLive Client (convert/http/0.170)
        Mozilla/5.0 (compatible; Konqueror/3.4; Linux) KHTML/3.4.1 (like Gecko)
        Exabot/2.0
        Exabot-Test/1.0
        FastSearch Web Crawler for Verizon SuperPages (<EMAIL>)
        FAST Data Search Document Retriever/4.0 04072100 2004-07-21 07:54:10 UTC
        FAST MetaWeb Crawler (helpdesk at fastsearch dot com)
        Faxobot/1.0
        Filangy/1.01 (Filangy; http://www.filangy.com/filangyinfo.jsp?inc=robots.jsp; <EMAIL>)
        Firefly/1.0 (compatible; Mozilla 4.0; MSIE 5.5)
        Foelock.co.uk Search MyGo R2-H6-3
        Foelock.co.uk Search MyGo Uknow 7
        Mozilla/4.0 (compatible; MSIE 4.01; Windows 95; http://www.freefind.com/spider.html )
        FreeFind/1.0 (<EMAIL>)
        FyberSpider (+http://www.fybersearch.com/fyberspider.php)
        Mozilla/4.0 (compatible; MSIE 5.0; www.galaxy.com; www.ads-work.com; +http://www.galaxy.com/info/crawler.html)
        Mozilla/5.0 <EMAIL>
        genieBot (http://64.5.245.11/faq/faq.html)
        genieBot <EMAIL>
        Geobot/0.1 http://www.geobot.co.uk
        GeoBot/version 1.0
        GeonaBot 1.0; http://www.geona.com/
        Gigabot/1.0
        Gigabot/2.0
        GigabotSiteSearch/2.0 (sitesearch.gigablast.com)
        Gigabot/2.0 (gigablast.com)
        Gigabot/2.0/gigablast.com/spider.html
        Gigabot/3.0 (http://www.gigablast.com/spider.html)
        Ocelli/1.1 (http://www.globalspec.com)
        Ocelli/1.3 (http://www.globalspec.com/Ocelli)
        GoForIt.com
        GOFORITBOT ( http://www.goforit.com/about/ )
        GoldenCanBot (http://www.GoldenCan.com/botinfo.aspx)
        ichiro/1.0 (ichiro'at'nttr.co.jp)
        ichiro/2.0 (<EMAIL>)
        gazz/5.0 (<EMAIL>)
        ichiro/2.0 (http://help.goo.ne.jp/door/crawler.html)
        Homerbot: www.homerweb.com
        Honda-Search/0.7.2 (Nutch; http://lucene.apache.org/nutch/bot.html; <EMAIL>)
        i1searchbot/2.0 (i1search web crawler; http://www.i1search.com; <EMAIL>)
        IErachnid 3.0 http://uk.searchengineeurope.com
        Nutch/0.7 (igougocrawler; http://www.igougo.com/search/web.asp; <EMAIL>)
        Mozilla/3.0 (INGRID/3.0 MT; <EMAIL>; http://aanmelden.ilse.nl/?aanmeld_mode=webhints)
        iltrovatore-setaccio/1.2-dev (spidering; http://www.iltrovatore.it/aiuto/motore_di_ricerca.html; <EMAIL>)
        Iltrovatore-Setaccio/0.3-dev (Indexing; http://www.iltrovatore.it/bot.html; <EMAIL>)
        IlTrovatore-Setaccio/1.2 (It-bot;compatible;MSIE 6.0;Mozilla/4.0; http://www.iltrovatore.it/bot.html; <EMAIL>)
        IlTrovatore-Setaccio/1.2 (Italy search engine; http://www.iltrovatore.it/bot.html; <EMAIL>)
        InetSeekBot2005 (Version: 1.01, Powered by InetSeek.de)
        Seekbot/1.0 (http://www.seekbot.net/bot.html) HTTPFetcher/0.3
        Insitor.com, search and find world wide!
        Mozilla/5.0 (compatible; Najdi.si/3.1)
        Jetbot/1.0
        Jyxobot/1
        KazoomBot/0.06-dev (Kazoom; http://www.kazoom.ca/bot.html; <EMAIL>)
        EasyDL/3.04  http://keywen.com/Encyclopedia/Bot
        cfetch/1.0
        voyager/1.0
        kulokobot www.kuloko.com <EMAIL>
        kuloko-bot/0.2
        Linguee Bot (http://www.linguee.com/bot; <EMAIL>)
        Linknzbot (+ http://www.linknz.co.nz/robot.php)
        LocalBot/1.0 (+http://www.localbot.co.uk/)
        LocalcomBot/1.2 (+http://www.local.com/bot.htm)
        LocalcomBot/1.2.2 (+http://www.local.com/bot.htm)
        NetResearchServer(http://www.look.com)
        LookBot
        Look.com
        Seeker.lookseek.com
        Mozilla/4.0 (compatible; grub-client-1.2.1; Crawl your own stuff with http://grub.org)
        NutchCVS/0.05 (Nutch; http://www.nutch.org/docs/en/bot.html; <EMAIL>)MARTINI
        Metager2 (http://metager2.de/site/webmaster.php)
        Metaspinner/0.01 (Metaspinner; http://www.meta-spinner.de/; <EMAIL>/)
        HenryTheMiragoRobot
        HenryTheMiragoRobot (http://www.miragorobot.com/scripts/mrinfo.asp)
        Mirago-Test-Robot (http://www.miragorobot.com)
        citenikbot/0.4 (albert; http://www.citenik.co.uk/bot.html)
        MojeekBot/0.1 (galileo; http://www.mojeek.com/bot.html)
        MojeekBot/0.2 (archi; http://www.mojeek.com/bot.html)
        Mozilla/5.0 (compatible; MojeekBot/2.0; http://www.mojeek.com/bot.html)
        mozDex/0.04-dev (mozDex; http://www.mozdex.com/en/bot.html; <EMAIL>)
        mozDex/0.05-dev (mozDex; http://www.mozdex.com/bot.html; <EMAIL>)
        MSNBOT/0.1 (http://search.msn.com/msnbot.htm)
        Mozilla/4.0 (compatible; MSIE 6.0; Windows NT; MS Search 4.0 Robot)
        lanshanbot/1.0 ( http://search.msn.com/msnbot.htm)
        msnbot-Products/1.0 (+http://search.msn.com/msnbot.htm)
        msnbot-media/1.0 (+http://search.msn.com/msnbot.htm)
        msnbot/0.11 (+http://search.msn.com/msnbot.htm)
        msnbot/0.3 (+http://search.msn.com/msnbot.htm)
        msnbot/0.9 (+http://search.msn.com/msnbot.htm)
        msnbot/1.0 (+http://search.msn.com/msnbot.htm)
        Cowbot-0.1 (NHN Corp. / 2-3011-1954 / <EMAIL>)
        NaverBot-1.0 (NHN Corp. / +82-2-3011-1954 / <EMAIL>)
        Mozilla/4.0 (compatible; NaverBot/1.0; <EMAIL>)
        Mozilla/4.0 (compatible; NaverBot/1.0; http://help.naver.com/delete_main.asp)
        Yeti/0.01 (nhn/1noon, <EMAIL>, check robots.txt daily and follows it)
        Yeti/1.0 (+http://help.naver.com/robots/)
        Mozilla/4.0 (compatible; MSIE 5.0;NetNose-Crawler 2.0; A New Search Experience: http://www.netnose.com)
        NetWhatCrawler/0.06-dev (NetWhatCrawler from NetWhat.com; http://www.netwhat.com; <EMAIL>)
        NG-Search/0.90 (NG-SearchBot; http://www.ng-search.com;  )
        NokodoBot/1.2 (+http://nokodo.com/bot.htm)
        Mozilla/5.0 (compatible; Page2RSS/0.5; +http://page2rss.com/)
        noxtrumbot/1.0 (<EMAIL>)
        Nr1-Onlinebot (Version: 1.02, powered by www.nr1-online.de)
        NuSearch Spider www.nusearch.com
        Nutch
        NutchCVS/0.03-dev (Nutch; http://www.nutch.org/docs/bot.html; <EMAIL>)
        NutchCVS/0.03-dev (Nutch; http://www.nutch.org/docs/en/bot.html; <EMAIL>)
        NutchOrg/0.03-dev (Nutch; www.nutch.org/docs/bot.html; <EMAIL>)
        NutchCVS/0.05-dev (Nutch; http://www.nutch.org/docs/en/bot.html; <EMAIL>)
        ObjectsSearch/0.01 (ObjectsSearch; http://www.ObjectsSearch.com/bot.html; <EMAIL>)
        ObjectsSearch/0.02 (ObjectsSearch; http://www.ObjectsSearch.com/bot.html; <EMAIL>)
        ObjectsSearch/0.06 (ObjectsSearch; http://www.ObjectsSearch.com/bot.html; <EMAIL>)
        OmniExplorer_Bot/1.07 (+http://www.omni-explorer.com) Internet Categorizer
        OmniExplorer_Bot/1.09 (+http://www.omni-explorer.com) Internet Categorizer
        OmniExplorer_Bot/1.09 (+http://www.omni-explorer.com)
        OmniExplorer_Bot/1.10 (+http://www.omni-explorer.com) Jobs Crawler
        OmniExplorer_Bot/1.18 (+http://www.omni-explorer.com) Torrent Crawler
        OmniExplorer_Bot/2.3 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.57 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.67 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.69 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.70 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.71 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.73 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.78a (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.82 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.83 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.86b (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.91 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.92 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/2.93 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/3.06c (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/3.06d (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/4.32 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/5.24 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/6.46 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/6.47 (+http://www.omni-explorer.com) WorldIndexer
        OmniExplorer_Bot/6.57 (+http://www.omni-explorer.com) WorldIndexer
        Onet.pl SA, http://szukaj.onet.pl
        Mozilla/5.0 (compatible; OnetSzukaj/5.0; +http://szukaj.onet.pl)
        holmes/3.9 (OnetSzukaj/5.0; +http://szukaj.onet.pl)
        Gaisbot/3.0+(<EMAIL>;+http://gais.cs.ccu.edu.tw/robot.php)
        Openbot/3.0+(<EMAIL>;+http://www.openfind.com.tw/robot.html)
        Openfind data gatherer,Openbot/3.0+(<EMAIL>;+http://www.openfind.com.tw/robot.html)
        PEERbot www.peerbot.com
        BravoBrian SpiderEngine MarcoPolo
        psbot/0.1 (+http://www.picsearch.com/bot.html)
        http://www.picsearch.com/bot.html
        pipeLiner/0.10 (PipeLine Spider; http://www.pipeline-search.com/webmaster.html)
        Pompos/1.3 http://dir.com/pompos.html
        Pop.gr/6.1 Arachne(compatible; MSIE 6.0; Windows 98; Corus)
        Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; PrassoSunner 1.00; http://www.prasso.com;)
        ProloCrawler/1.0 (http://www.prolo.com)
        www.proodle.com
        MSIE (MSIE 5.23; Mac_PowerPC)
        QweeryBot v2.05 -- http://qweerybot.qweery.com
        QweeryBot/2.06 ( http://qweerybot.qweery.com)
        QuepasaCreep ( <EMAIL> )
        StackRambler/2.0 (MSIE incompatible)
        Blaiz-Bee/2.00.953 (+http://www.blaiz.net)
        RedCarpet/1.2 (http://www.redcarpet-inc.com/robots.html)
        RedKernel WWW-Spider 2/0 (+http://www-spider.redkernel-softwares.com/)
        <EMAIL>
        DoCoMo/1.0/N505i/c20/TB/W20H10 (compatible; RFCrawler-Mobile/1.0;
        DoCoMo/1.0/N505i/c20/TB/W20H10+(compatible;+RFCrawler-Mobile/1.0;
        DoCoMo/2.0+SH901iC(c100;TB;W24H12)
        DoCoMo/1.0/N505i/c20/TB/W20H10 (compatible; RFCrawler-Mobile/1.0;
        RufusBot (Rufus Web Miner; http://64.124.122.252/feedback.html)
        MaSagool/1.0 (MaSagool; http://sagool.jp/; <EMAIL>)
        Scrubby/2.2 (http://www.scrubtheweb.com/)
        Scrubby/2.1 (http://www.scrubtheweb.com/abs/meta-check.html)
        search.ch V1.4.2 (<EMAIL>; http://www.search.ch)
        search.ch V1.4.2 (http://www.search.ch)
        SearchIt.Bot 1.62 (http://www.searchit.com/)
        SearchIt-Bot/2.3.1 (Searchit.Com; http://www.searchit.com/;  )
        Mozilla/5.0 (compatible; Charlotte/1.1; http://www.searchme.com/support/)
        Searchmee! Spider v0.98a
        SearchSight/2.0 (http://SearchSight.com/)
        Searchspider/1.2 (SearchSpider; http://www.searchspider.com; <EMAIL>)
        searchuk.comBot 1.1 (http://searchuk.com/spider)
        searchuk.comBot 1.2 (http://searchuk.com/spider)
        WireBot/1.0 (SearchWire.co.uk)
        SeekOn Spider 1.9(+http://www.seekon.com/spider.html)
        Seekbot/1.0 (http://www.seekbot.net/bot.html) HTTPFetcher/2.2
        Semager/1.0 (http://www.semager.de)
        Sensis Web Crawler (search_comments\at\sensis\dot\com\dot\au)
        Sensis.com.au Web Crawler (search_comments\at\sensis\dot\com\dot\au)
        SeznamBot/1.0 (+http://fulltext.seznam.cz/)
        ShopWiki/1.0 ( +http://www.shopwiki.com/wiki/Help:Bot)
        asterias/2.0
        Mozilla/4.0 (compatible; MSIE 6.0 compatible; Asterias Crawler v4; +http://www.singingfish.com/help/spider.html; <EMAIL>); SpiderThread Revision: 1.9
        Reaper [2.03.10-031204] (http://www.sitesearch.ca/reaper/)
        Helix/1.2 (+http://www.sitesearch.ca/helix/)
        SitiDi.net/SitiDiBot/1.0 (+Have Good Day)
        SMEALSearch-Bot
        snap.com beta crawler v0
        Snapbot/1.0
        semanticdiscovery/0.2(http://www.semanticdiscovery.com/sd/robot.html)
        semanticdiscovery/0.4(http://www.semanticdiscovery.com/sd/robot.html
        sogou spider
        Sogou web spider/4.0(+http://www.sogou.com/docs/help/webmasters.htm#07)
        sohu-search
        sohu agent
        libwww-perl/5.45
        sproose/0.1-alpha (sproose crawler; http://www.sproose.com/bot.html; <EMAIL>)
        sproose/0.1 (sproose bot; http://www.sproose.com/bot.html; <EMAIL>)
        Spider_Monkey/7.06 (SpiderMonkey.ca info at http://SpiderMonkey.ca /sm.shtml)
        SquigglebotBot/1.0 http://squigglebot.com
        http://www.sygol.com
        Szukacz/1.5 (robot; www.szukacz.pl/jakdzialarobot.html; <EMAIL>)
        NMG Spider/0.3 (szukanko.com)
        TerrawizBot/1.0 (+http://www.terrawiz.com/bot.html)
        T-H-U-N-D-E-R-S-T-O-N-E
        Mozilla/4.0 (compatible; T-H-U-N-D-E-R-S-T-O-N-E)
        Mozilla/4.0 (GeorgeTheTouristBot;http://www.touristlinks.net)
        TygoBot
        TygoProwler (http://www.tygo.com/)
        uk-Searcher(HTTP://WWW.UK-SEARCHER.CO.UK)
        uksearchpages.co.uk
        Mackster( http://www.ukwizz.com )
        UKWizz/Nutch-0.8.1 (UKWizz Nutch crawler; http://www.ukwizz.com/)
        search.updated.com/0.06 (search.updated.com; http://www.updated.com/docucmentation/bot.html; <EMAIL>)
        updated/0.1beta (updated.com; http://www.updated.com; <EMAIL>)
        updated/0.1-alpha (updated crawler; http://www.updated.com; <EMAIL>)
        VMBot/0.7.2 (VMBot; http://www.VerticalMatch.com/; <EMAIL>)
        VisBot/2.0+(Visvo.com+Crawler)
        Mozilla/4.0 (compatible; MSIE 5.0; Windows 95) VoilaBot BETA 1.2 (http://www.voila.com/)
        appie 1.1 (www.walhello.com)
        webcrawl.net
        WebGather 3.0
        WebIndexer/1-dev (Web Indexer; mailto://<EMAIL>; <EMAIL>)
        webrover/0.1 (+http://www.webrover.de)
        WebSearch.COM.AU/3.0.1 (The Australian Search Engine; http://WebSearch.COM.AU; <EMAIL>)
        MSIE 6.0
        ScollSpider/2.0 (+http://www.webwobot.com/ScollSpider.php)
        WhatchaBot10 (http://www.whatchaseek.com)
        Mozilla/5.0 (compatible; Vagabondo/2.1; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)
        Bilbo
        Mozilla/4.0 (compatible;  Vagabondo/2.3; webcrawler at wise-guys dot nl; http://webagent.wise-guys.nl/)
        Mozilla/4.0 compatible ZyBorg/1.0 (<EMAIL>; http://www.WISEnutbot.com)
        Mozilla/4.0 compatible ZyBorg/1.0 Dead Link Checker (<EMAIL>; http://www.WISEnutbot.com)
        WorldLight
        WWWeasel Robot v1.00 (http://wwweasel.de)
        Wotbox/alpha0.5.1 (<EMAIL>; http://www.wotbox.com)   Java/1.4.1_02
        wwwster/1.4 (Beta, mailto:<EMAIL>)
        xirq/0.1-dev (xirq; http://www.xirq.com; <EMAIL>)
        YadowsCrawler (http://www.yadows.com)
        Yandex/1.01.001 (compatible; Win16; H)
        Yandex/1.01.001 (compatible; Win16; I)
        Yandex/1.01.001 (compatible; Win16; P)
        Yandex/1.03.000 (compatible; Win16; M)
        Yandex/1.03.003 (compatible; Win16; D)
        Yandex/1.03.003 (compatible; Win16; D)
        Yandex/2.01.000 (compatible; Win16; Dyatel; C)
        Yandex/2.01.000 (compatible; Win16; Dyatel; D)
        Yandex/2.01.000 (compatible; Win16; Dyatel; N)
        Yandex/2.01.000 (compatible; Win16; Dyatel; Z)
        Yanga WorldSearch Bot v1.1/beta (http://www.yanga.co.uk/)
        YodaoBot/1.0 (http://www.yodao.com/help/webmaster/spider/; )
        Mozilla/5.0 (compatible; YoudaoBot/1.0; http://www.youdao.com/help/webmaster/spider/; )
        Gulper Web Bot 0.2.4 (www.ecsl.cs.sunysb.edu/~maxim/cgi-bin/Link/GulperBot)
        Zao/0.1 (http://www.kototoi.org/zao/)
        Zao/0.2 (http://www.kototoi.org/zao/)
        Zao-Crawler
        zibber-v0.1(www.zibb.com/crawler/)
        ZIPPPCVS/0.24 (ZipppBot/.24; http://www.zippp.net; <EMAIL>)
        ZipppBot/0.11 (ZipppBot; http://www.zippp.net; <EMAIL>)
        NextGenSearchBot 1 (for information visit http://about.zoominfo.com/PublicSite/NextGenSearchBot.asp)
        ia_archiver
        http://www.almaden.ibm.com/cs/crawler
        Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)
        curl/7.9.1 (win32) libcurl 7.9.1
        dtSearchSpider
        Mozilla/4.7 (compatible; http://eidetica.com/spider)
        e-SocietyRobot(http://www.yama.info.waseda.ac.jp/~yamana/es/)
        Mozilla/4.0 (fantomBrowser)
        Mozilla/4.0 (stealthBrowser)
        Mozilla/4.0 (cloakBrowser)
        Mozilla/4.0 (fantomCrew Browser)
        multiBlocker browser - IP blocker for Spam, Fraud + Snoop Protection
        multiBlocker browser
        Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; Girafabot; girafabot at girafa dot com; http://www.girafa.com)
        Googlebot/2.1 (+http://www.googlebot.com/bot.html)
        Mozilla/4.0 (compatible; MSIE 6.0; Googlebot/2.1 (+http://www.googlebot.com/bot.html); .NET CLR 1.0.3705)
        IncyWincy data gatherer(<EMAIL>,http://www.loopimprovements.com/robot.html)
        Mozilla/3.0 (compatible; Indy Library)
        Sqworm/2.9.72-BETA (beta_release; 20010821-737; i686-pc-linux-gnu)
        larbin (or some variations on larbin)
        Mozilla/4.7 (compatible; Intelliseek; http://www.intelliseek.com)
        sitecheck.internetseer.com
        larbin
        LNSpiderguy
        LinkWalker
        Lite Bot 0916b
        MarkWatch/1.0
        flunky
        Microsoft URL Control - 5.01.4319
        NAMEPROTECT
        Mozilla/4.7
        RPT-HTTPClient/0.3-3
        NPBot-1/2.0 (http://www.nameprotect.com/botinfo.html)
        aipbot/1.0 (aipbot; http://www.aipbot.com; <EMAIL>)
        NetMechanic V2.0
        WebFilter Robot 1.0
        PicaLoader 1.0
        Robozilla/1.0
        RPT-HTTPClient/0.3-3
        ScoutAbout
        TurnitinBot/1.4 http://www.turnitin.com/robot/crawlerinfo.html
        SlySearch
        WebZIP/4.0 (http://www.spidersoft.com)
        Teleport Pro/1.28
        (Teradex Mapper; <EMAIL>; http://www.teradex.com)
        tivraSpider/1.0 (<EMAIL>)
        libwww-perl/5.47
        UbiCrawler/v0.3beta (http://ubi.imc.pi.cnr.it/projects/ubicrawler/)
        Webclipping.com
        webrank
        websquash.com ( Add Url Robot )
        lwp-trivial/1.34
        AESOP_com_SpiderMan
        ozilla/4.7 (compatible; Whizbang)
        TECOMAC-Crawler/0.3
        X-Crawler
        cosmos/0.8)_(<EMAIL>)
        Mozilla/4.05
        Mozilla/4.0(compatible; Zealbot 1.0)
        Zeus 60359 Webster Pro V2.9 Win32
      </excludedUserAgents>
      <!--List of ip addresses of search engines. Requests from following addresses will be ignored by Analytics.
    Supported values and formates:
    IP address, e.g. ********;
    IP range, e.g. ******** - ********0;
    Subnet, e.g. 10.2.3.*
    Use # for comments
    See http://iplists.com/ for updates
    -->
      <excludedIPAddresses>
      </excludedIPAddresses>
    </analyticsExcludeRobots>
  </sitecore>
</configuration>