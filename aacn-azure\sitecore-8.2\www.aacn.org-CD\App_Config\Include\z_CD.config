﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:set="http://www.sitecore.net/xmlconfig/set/">
  <sitecore>
    <events>
      <event name="item:added">
        <handler type="Aacn.SitecoreExtensions.Events.ItemEventHandler,Aacn.SitecoreExtensions" method="OnItemAdded" >
          <Database>master</Database>
          <patch:delete/>
        </handler>
      </event>
    </events>

    <IDTable>
      <param connectionStringName="master" set:connectionStringName="webcd" />
    </IDTable>

    <databases>
      <!--Remove the 'master' database which is not required in CD environments-->
      <database id="master">
        <patch:delete/>
      </database>
    </databases>

    <search>
      <configuration>
        <indexes>
          <index id="system">
            <!-- Remove the crawlers from the system index -->
            <locations>
              <patch:delete/>
            </locations>
          </index>
        </indexes>
      </configuration>
    </search>

    <hooks>
      <hook type="Sitecore.Marketing.Taxonomy.Hooks.DeployInitializer, Sitecore.Marketing.Taxonomy">
        <patch:delete />
      </hook>
    </hooks>
    
    <eventing>
      <eventQueue>
        <processingInterval>00:00:10</processingInterval>
      </eventQueue>
    </eventing>

    <scheduling>
      <agent type="Sitecore.Tasks.CleanupEventQueue, Sitecore.Kernel">
        <patch:attribute name="interval">00:00:00</patch:attribute>
      </agent>
      <!-- CD servers do not publish so remove the publishing agent -->
      <agent type="Sitecore.Tasks.PublishAgent">
        <patch:delete/>
      </agent>
      <!-- CD servers do not need Email Report agent -->
      <agent type="Sitecore.Analytics.Tasks.EmailReportsTask, Sitecore.Analytics">
        <patch:delete/>
      </agent>
      <!-- CD servers do not need Content Search agent for master index -->
      <agent type="Sitecore.ContentSearch.Tasks.Optimize">
        <patch:delete/>
      </agent>
      <!-- CD servers do not process scheduled tasks on core DB agent -->
      <agent type="Sitecore.Tasks.DatabaseAgent" name="Core_Database_Agent">
        <patch:delete/>
      </agent>
      <!-- CD servers do not process scheduled tasks on master DB agent -->
      <agent type="Sitecore.Tasks.DatabaseAgent" name="Master_Database_Agent">
        <patch:delete/>
      </agent>
      <!-- CD servers do not clean up files for master DB agent -->
      <agent type="Sitecore.Tasks.CleanupFDAObsoleteMediaData">
        <databases hint="raw:AddDatabase">
          <database name="master">
            <patch:delete/>
          </database>
        </databases>
      </agent>
      <!-- CD servers do not need XCore extension for publishing -->
      <agent type="XCore.SitecoreExtensions.Tasks.ScheduledPublishing,XCore.SitecoreExtensions">
        <patch:delete/>
      </agent>

      <agent type="Sitecore.Analytics.Tasks.UpdateReportsSummaryTask, Sitecore.Analytics" >
        <patch:delete/>
      </agent>
    </scheduling>

    <sites>
      <!-- Adjust the content database for the shell as master no longer exists -->
      <site name="shell">
        <patch:attribute name="content">webcd</patch:attribute>
      </site>
      <!-- Adjust the content database for the modules-shell site as master no longer exists -->
      <site name="modules_shell">
        <patch:attribute name="content">webcd</patch:attribute>
      </site>
    </sites>

    <settings>
      <setting name="Analytics.ForwardedRequestHttpHeader">
        <patch:attribute name="value">X-Forwarded-For</patch:attribute>
      </setting>
      <setting name="Aacn.Journals.ContextDatabase">
        <patch:delete/>
      </setting>
      <setting name="ExperienceAnalytics.Api.MasterDatabase">
        <patch:delete/>
      </setting>
      <setting name="ListManagement.Database" value="webcd" patch:instead="setting[@name='ListManagement.Database']"/>
      <setting name="Analytics.DefaultDefinitionDatabase" value="webcd"/>
      <!--<setting name="Social.Messages.SearchIndex.Master" value="social_messages_web" patch:instead="setting[@name='Social.Messages.SearchIndex.Master']"/>-->
    </settings>

    <taxonomy>
      <repositories>
        <itemTaxonomyRepository>
          <param desc="databaseName">webcd</param>
        </itemTaxonomyRepository>
      </repositories>
    </taxonomy>

    <reporting>
      <dataProvider>
        <datasources>
          <add key="collection">
            <FiltersFactory>
              <param desc="definitionDatabaseName">webcd</param>
            </FiltersFactory>
          </add>
          <add key="reporting">
            <FiltersFactory>
              <param desc="definitionDatabaseName">webcd</param>
            </FiltersFactory>
          </add>
        </datasources>
      </dataProvider>
    </reporting>

    <contentSearch>
      <configuration>
        <indexes>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_globalsearch_master_index">
            <patch:delete/>
          </index>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_master_index">
            <patch:delete/>
          </index>
          <!-- Remove indexes based on the master DB -->
          <index id="sitecore_marketing_asset_index_master">
            <patch:delete/>
          </index>
        </indexes>
      </configuration>

      <indexConfigurations>
        <indexUpdateStrategies>
          <!-- Remove interval-based index rebuild for master DB on CD -->
          <intervalAsyncMaster type="Sitecore.ContentSearch.Maintenance.Strategies.IntervalAsynchronousStrategy, Sitecore.ContentSearch">
            <patch:delete/>
          </intervalAsyncMaster>
          <!-- Remove SynchronousStrategy for CD -->
          <syncMaster type="Sitecore.ContentSearch.Maintenance.Strategies.SynchronousStrategy, Sitecore.ContentSearch">
            <patch:delete/>
          </syncMaster>
        </indexUpdateStrategies>
      </indexConfigurations>
    </contentSearch>

    <marketingDefinitions>
      <campaign>
        <repositories>
          <item>
            <param desc="databaseName">webcd</param>
          </item>
        </repositories>
      </campaign>
      <asset>
        <repositories>
          <item>
            <param desc="databaseName">webcd</param>
            <param desc="indexName">sitecore_marketing_asset_index_web</param>
          </item>
        </repositories>
      </asset>
      <goal>
        <repositories>
          <item>
            <param desc="databaseName">webcd</param>
          </item>
        </repositories>
      </goal>
      <outcome>
        <repositories>
          <item>
            <param desc="databaseName">webcd</param>
          </item>
        </repositories>
      </outcome>
    </marketingDefinitions>

  </sitecore>
</configuration>