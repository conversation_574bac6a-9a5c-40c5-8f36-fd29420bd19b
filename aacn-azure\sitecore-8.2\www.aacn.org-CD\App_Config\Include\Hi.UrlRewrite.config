﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>


    <settings>

      <!-- IgnorePages setting contain paths which should be ignored by redirect module. Use '|' to divide the paths. -->
      <setting name="Hi.UrlRewrite.IgnoreUrlPrefixes" value="/sitecore" />

      <!-- Root node in the sitecore tree where searching for rewrite folders begins. -->
      <setting name="Hi.UrlRewrite.RewriteFolderSearchRoot" value="/sitecore" />

      <!-- Cache size for storing the rules -->
      <setting name="Hi.UrlRewrite.CacheSize" value="10MB" />

      <!-- Log file name -->
      <setting name="Hi.UrlRewrite.LogFileEnabled" value="true" />

      <!-- Log file name -->
      <setting name="Hi.UrlRewrite.LogFileName" value="$(dataFolder)/logs/UrlRewrite.log.{date}.txt" />

      <!-- Log file level -->
      <setting name="Hi.UrlRewrite.LogFileLevel" value="INFO" />

    </settings>

    <pipelines>
      <httpRequestBegin>
        <processor type="Hi.UrlRewrite.Processing.InboundRewriteProcessor, Hi.UrlRewrite" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']" />
      </httpRequestBegin>
    </pipelines>

    <events>

      <event name="item:saved">
        <handler type="Hi.UrlRewrite.Processing.UrlRewriteItemEventHandler, Hi.UrlRewrite" method="OnItemSaved" />
      </event>
      <event name="item:saved:remote">
        <handler type="Hi.UrlRewrite.Processing.UrlRewriteItemEventHandler, Hi.UrlRewrite" method="OnItemSavedRemote" />
      </event>

      <event name="item:deleted">
        <handler type="Hi.UrlRewrite.Processing.UrlRewriteItemEventHandler, Hi.UrlRewrite" method="OnItemDeleted" />
      </event>
      <event name="item:deleted:remote">
        <handler type="Hi.UrlRewrite.Processing.UrlRewriteItemEventHandler, Hi.UrlRewrite" method="OnItemDeletedRemote" />
      </event>

    </events>

  </sitecore>

</configuration>
