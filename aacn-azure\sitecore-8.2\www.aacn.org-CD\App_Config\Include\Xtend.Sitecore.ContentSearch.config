﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <contentSearch>
      <configuration>
        <indexes>
          <index id="sitecore_master_index">
            <patch:attribute name="type">Sitecore.ContentSearch.SolrProvider.SwitchOnRebuildSolrSearchIndex,Sitecore.ContentSearch.SolrProvider</patch:attribute>
            <param patch:after="*[@desc='core']" desc="rebuildcore">$(id)_rebuild</param>
            <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
              <fields hint="raw:AddComputedIndexField">
                <field fieldName="datasourceitems" returnType="text">Sitecore.ContentTesting.ContentSearch.ComputedIndexFields.TestDataSources, Sitecore.ContentTesting</field>
              </fields>
            </configuration>
          </index>
          <index id="sitecore_web_index">
            <patch:attribute name="type">Sitecore.ContentSearch.SolrProvider.SwitchOnRebuildSolrSearchIndex,Sitecore.ContentSearch.SolrProvider</patch:attribute>
            <param patch:after="*[@desc='core']" desc="rebuildcore">$(id)_rebuild</param>
            <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
              <fields hint="raw:AddComputedIndexField">
                <field fieldName="datasourceitems" returnType="text">Sitecore.ContentTesting.ContentSearch.ComputedIndexFields.TestDataSources, Sitecore.ContentTesting</field>
              </fields>
            </configuration>
          </index>
          <index id="sitecore_webcd_index">
            <patch:attribute name="type">Sitecore.ContentSearch.SolrProvider.SwitchOnRebuildSolrSearchIndex,Sitecore.ContentSearch.SolrProvider</patch:attribute>
            <param patch:after="*[@desc='core']" desc="rebuildcore">$(id)_rebuild</param>
            <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
              <fields hint="raw:AddComputedIndexField">
                <field fieldName="datasourceitems" returnType="text">Sitecore.ContentTesting.ContentSearch.ComputedIndexFields.TestDataSources, Sitecore.ContentTesting</field>
              </fields>
            </configuration>
          </index>
          <index id="sitecore_core_index">
            <patch:attribute name="type">Sitecore.ContentSearch.SolrProvider.SwitchOnRebuildSolrSearchIndex,Sitecore.ContentSearch.SolrProvider</patch:attribute>
            <param patch:after="*[@desc='core']" desc="rebuildcore">$(id)_rebuild</param>
          </index>
          <index id="sitecore_netforum_entities">
            <patch:attribute name="type">Sitecore.ContentSearch.SolrProvider.SwitchOnRebuildSolrSearchIndex,Sitecore.ContentSearch.SolrProvider</patch:attribute>
            <param patch:after="*[@desc='core']" desc="rebuildcore">$(id)_rebuild</param>
          </index>
          <index id="sitecore_testing_index" type="Sitecore.ContentSearch.SolrProvider.SolrSearchIndex, Sitecore.ContentSearch.SolrProvider">
            <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
              <fields hint="raw:AddComputedIndexField">
                <field fieldName="datasourceitems">
                  <patch:attribute name="returnType">text</patch:attribute>
                </field>
              </fields>
            </configuration>
          </index>
        </indexes>
      </configuration>
      <indexConfigurations>
        <defaultSolrIndexConfiguration>
          <fields hint="raw:AddComputedIndexField">
            <field fieldName="urllink"             returnType="string">Aacn.Business.ContentSearch.ComputedFields.UrlLink,Aacn.Business</field>
            <field fieldName="iscontent"           returnType="bool">Aacn.Business.ContentSearch.ComputedFields.IsContent,Aacn.Business</field>
            <field fieldName="pagecontent"         returnType="text">Aacn.Business.ContentSearch.ComputedFields.PageContent,Aacn.Business</field>
            <field fieldName="referenceitems"      returnType="text">Aacn.Business.ContentSearch.ComputedFields.ReferenceItems,Aacn.Business</field>
            <field fieldName="attachmentcontent"   returnType="text" type="Aacn.Business.ContentSearch.ComputedFields.AttachmentContent,Aacn.Business">
              <mediaIndexing ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration/mediaIndexing"/>
            </field>
            <field fieldName="autosuggest"         returnType="text">Aacn.Business.ContentSearch.ComputedFields.AutoSuggest,Aacn.Business</field>
            <field fieldName="areaofpractice"      returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.AreaOfPracticeNames,Aacn.Business</field>
            <field fieldName="levels"              returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.LevelNames,Aacn.Business</field>
            <field fieldName="roles"               returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.RoleNames,Aacn.Business</field>
            <field fieldName="patientpopulations"  returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.PatientPopulationNames,Aacn.Business</field>
            <field fieldName="primarytopic"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.PrimaryTopicName,Aacn.Business</field>
            <field fieldName="secondarytopics"              returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.SecondaryTopicNames,Aacn.Business</field>
            <field fieldName="contentcategory"     returnType="guid">Aacn.Business.ContentSearch.ComputedFields.ContentCategory,Aacn.Business</field>
            <field fieldName="sectionname"     returnType="string">Aacn.Business.ContentSearch.ComputedFields.SectionName,Aacn.Business</field>
            <field fieldName="ancestors"     returnType="guidCollection">Aacn.Business.ContentSearch.ComputedFields.Ancestors,Aacn.Business</field>
            <field fieldName="journalpublicationcode"     returnType="string">Aacn.Business.ContentSearch.ComputedFields.JournalPublicationCode,Aacn.Business</field>
            <field fieldName="journalpublicationissue"     returnType="int">Aacn.Business.ContentSearch.ComputedFields.JournalPublicationIssue,Aacn.Business</field>
            <field fieldName="journalpublicationvolume"     returnType="int">Aacn.Business.ContentSearch.ComputedFields.JournalPublicationVolume,Aacn.Business</field>
            <field fieldName="globalsearchcontentcategory"  returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.GlobalSearchContentCategory,Aacn.Business</field>
            <field fieldName="certificationprogramtypename"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationProgramTypeName,Aacn.Business</field>
            <field fieldName="certificationpatientpopulationname"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationPatientPopulationName,Aacn.Business</field>
            <field fieldName="certificationcategoryname"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationCategoryName,Aacn.Business</field>
            <field fieldName="certificationsubtitle"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationSubtitle,Aacn.Business</field>
            <field fieldName="certificationcode"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationCode,Aacn.Business</field>
            <field fieldName="certificationeligibility"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationEligibility,Aacn.Business</field>
            <field fieldName="certificationroles"              returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.CertificationRoles,Aacn.Business</field>
            <field fieldName="certificationterm"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationTermPeriod,Aacn.Business</field>
            <field fieldName="certificationsummary"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationSummary,Aacn.Business</field>
            <field fieldName="certificationrenewalurl"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationRenewalUrl,Aacn.Business</field>
            <field fieldName="group"              returnType="string">Aacn.Business.ContentSearch.ComputedFields.CertificationGroup,Aacn.Business</field>
            <field fieldName="csihospital"     returnType="string">Aacn.Business.ContentSearch.ComputedFields.CSIHospital,Aacn.Business</field>
            <field fieldName="csiregion"     returnType="string">Aacn.Business.ContentSearch.ComputedFields.CSIRegion,Aacn.Business</field>
            <field fieldName="thumbnailurl"                       returnType="string">Aacn.Business.ContentSearch.ComputedFields.ThumbnailUrl,Aacn.Business</field>
          </fields>
          <fieldMap>
            <fieldTypes hint="raw:AddFieldByFieldTypeName">
              <fieldType fieldTypeName="image with cropping" returnType="text" />
            </fieldTypes>
            <fieldNames>
              <field fieldName="iscontent" returnType="bool" />
              <field fieldName="urllink" returnType="string" />
              <field fieldName="pagecontent" returnType="text" />
              <field fieldName="referenceitems" returnType="text" />
              <field fieldName="attachmentcontent" returnType="text" />
              
              <!-- Sitecore is failing to determine field type correctly for the following fields -->
              <field fieldName="roles" returnType="guidCollection" />
              <field fieldName="area_of_practice" returnType="guidCollection" />
              <field fieldName="level" returnType="guidCollection" />
              <field fieldName="tags" returnType="guidCollection" />
              <field fieldName="tag" returnType="guidCollection" />
              <field fieldName="patient_population" returnType="guidCollection" />

              <field fieldName="contentcategory" returnType="guid" />
              <field fieldName="sectionname" returnType="string" />
            </fieldNames>
          </fieldMap>
          <fieldReaders type="Sitecore.ContentSearch.FieldReaders.FieldReaderMap, Sitecore.ContentSearch">
            <mapFieldByTypeName hint="raw:AddFieldReaderByFieldTypeName">
              <fieldReader fieldTypeName="image with cropping"  fieldReaderType="Sitecore.ContentSearch.FieldReaders.ImageFieldReader, Sitecore.ContentSearch" />
            </mapFieldByTypeName>
          </fieldReaders>
        </defaultSolrIndexConfiguration>
        <indexUpdateStrategies>
          <intervalAsyncNetForum type="Aacn.Business.ContentSearch.Indexing.Strategies.ExternalDatasourceIntervalAsynchronousStrategy, Aacn.Business">
            <param desc="database">netforum</param>
            <param desc="interval">00:00:00</param>            
          </intervalAsyncNetForum>
          <externalDatasourceAsyncNetForum type="Aacn.Business.ContentSearch.Indexing.Strategies.ExternalDatasourceSyncStrategy, Aacn.Business">
            <param desc="database">netforum</param>
          </externalDatasourceAsyncNetForum>
        </indexUpdateStrategies>
      </indexConfigurations>
    </contentSearch>
  </sitecore>
</configuration>