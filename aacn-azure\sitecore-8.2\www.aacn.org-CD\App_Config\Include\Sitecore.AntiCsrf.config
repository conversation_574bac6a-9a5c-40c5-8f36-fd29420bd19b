﻿<?xml version="1.0"?>
<configuration>
  <sitecore>
    <AntiCsrf cookieName="__CSRFCOOKIE" formFieldName="__CSRFTOKEN" detectionResult="RaiseException" errorPage="" enabled="true">
      <rules>
        <rule name="WFFM">
          <urlPrefix>/sitecore/shell</urlPrefix>
          <ignore contains="/sitecore/shell/Applications/Modules/Web Forms for Marketers/Form Reports"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Forms.Shell.UI.Dialogs.LookupRecords.aspx"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Forms.Shell.UI.Dialogs.ListItemsEditor.aspx"/>
        </rule>
        <rule name="shell">
          <urlPrefix>/sitecore/shell</urlPrefix>
          <ignore contains="Content Manager/Execute"/>
          <ignore contains="FlashUpload/Advanced/UploadTarget"/>
          <ignore contains="FlashUpload/Attach/AttachTarget"/>
          <ignore contains="WebEdit/Palette"/>
          <ignore contains="WebEdit/WebEditRibbon"/>          
          <ignore contains="InstantSearch"/>
          <ignore contains="WordOCX/Load"/>
          <ignore contains="Applications/Archives/Archive"/>
          <ignore contains="Applications/Archives/Recycle Bin"/>
          <ignore contains="Applications/Security/User Manager"/>
          <ignore contains="Applications/Security/Role Manager"/>
          <ignore contains="Applications/Security/Domain Manager"/>
          <ignore contains="Applications/Analytics/ReportRunner/Report"/>
        </rule>
        <rule name="shell-desktop">
          <urlPrefix>/sitecore/shell/default</urlPrefix>
          <ignore contains="xmlcontrol=Gallery.Components"/>
          <ignore contains="xmlcontrol=CopyVisitorToAnotherPlan"/>
          <ignore contains="xmlcontrol=MoveVisitorToAnotherState"/>
        </rule>
        <rule name="xaml-controls">
          <urlPrefix>/sitecore/shell/~/xaml/</urlPrefix>
          <ignore contains="/sitecore/shell/~/xaml/Installer.AddSecuritySourceDialog"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.SelectAccount"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.SelectAccount"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.SelectRoles"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.EditManagedDomains"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.RoleManager.ViewMembers"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.Security.RoleManager.ViewParentRoles"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.WebEdit.Dialogs.LockedItems"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.MarketingAutomation.Dialogs.SelectStateVisitor.aspx"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.MarketingAutomation.Dialogs.ActionEditors.AddToVisitProfile.aspx"/>
          <ignore contains="/sitecore/shell/~/xaml/Sitecore.Shell.Applications.MarketingAutomation.Dialogs.ActionEditors.SubtractFromVisitProfile.aspx"/>
        </rule>
      </rules>
    </AntiCsrf>
  </sitecore>
</configuration>