﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose: This include file enables and configures the content testing features of Sitecore.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!-- CONTENT TESTING - AUTOMATIC CONTENT TESTING - ENABLED
           Determines whether automatic content testing features should be enabled.
           Default value: true
      -->
      <setting name="ContentTesting.AutomaticContentTesting.Enabled" value="true" />

      <!-- CONTENT TESTING - CACHING - ACTIVE TEST CACHE MAXSIZE
           Specifies the maximum size of the ActiveTestCache cache.
           Default value: 512KB
      -->
      <setting name="ContentTesting.Caching.ActiveTestCache.MaxSize" value="512KB" />

      <!-- CONTENT TESTING - COMMAND ROUTE PREFIX
           The prefix used for controller routes.
           Default value: sitecore/shell/api/ct/
      -->
      <setting name="ContentTesting.CommandRoutePrefix" value="sitecore/shell/api/ct/"/>

      <!-- CONTENT TESTING - DEFAULT CONFIDENCE LEVEL           
           The confidence level used when creating new content tests.
           Default value: 95
      -->
      <setting name="ContentTesting.DefaultConfidenceLevel" value="95"/>

      <!-- CONTENT TESTING - DEFAULT TRAFFIC ALLOCATION
           The content test traffic allocation as a percentage.
           Default value: 100
      -->
      <setting name="ContentTesting.DefaultTrafficAllocation" value="100"/>

      <!-- CONTENT TESTING - END TEST RULESET
           The path to the ruleset that determines whether a test can finish automatically.
           Default value: /sitecore/system/Settings/Analytics/Testing/End Test Behavior
      -->
      <setting name="ContentTesting.EndTestRuleset" value="/sitecore/system/Settings/Analytics/Testing/End Test Behavior"/>

      <!-- CONTENT TESTING - GENERATE SCREENSHOTS 
           Determines when screenshots should be generated.
           Must be one of ( all | limited | none )
           Default value: all
      -->
      <setting name="ContentTesting.GenerateScreenshots" value="all"/>
     
      <!-- CONTENT TESTING - MAXIMUM CONTENT TEST DURATION
           The maximum duration in days of a content test.
           
           Default value: 14
      -->
      <setting name="ContentTesting.MaximumContentTestDuration" value ="14"/>

      <!-- CONTENT TESTING - MAXIMUM MV TEST DURATION
           The maximum duration in days of a multivariate content test.
           Default value: 90
      -->
      <setting name="ContentTesting.MaximumOptimizationTestDuration" value ="90"/>

      <!-- CONTENT TESTING - MINIMUM DURATION
           The minimum duration in days of a content test.
           Default value: 7
      -->
      <setting name="ContentTesting.MinimumDuration" value ="7"/>

      <!-- CONTENT TESTING - PHANTOM JS - ENABLE DISK CACHE
           Determines whether the PhantomJS tool should use disk caching for web resources.
           Default value: true
      -->
      <setting name="ContentTesting.PhantomJS.EnableDiskCache" value ="false"/>

      <!-- CONTENT TESTING - PHANTOM JS - ENABLE JAVASCRIPT
           Determines whether the PhantomJS tool should allow JavaScript on pages.
           Default value: true
      -->
      <setting name="ContentTesting.PhantomJS.EnableJavaScript" value ="true"/>

      <!-- CONTENT TESTING - PHANTOM JS - EXECUTABLE PATH
           The path to the PhantomJS tool that is used to generate screenshots of pages.
           Default value: $(dataFolder)/tools/phantomjs/phantomjs.exe
      -->
      <setting name="ContentTesting.PhantomJS.ExecutablePath" value ="$(dataFolder)/tools/phantomjs/phantomjs.exe"/>

      <!-- CONTENT TESTING - PHANTOM JS - LOAD IMAGES
           Determines whether the PhantomJS tool should load images on pages.
           Default value: true
      -->
      <setting name="ContentTesting.PhantomJS.LoadImages" value ="true"/>

      <!-- CONTENT TESTING - PHANTOM JS - LOCK NAVIGATION
           Determines whether the PhantomJS tool will load content from other URLs including iFrames.
           Default value: true
      -->
      <setting name="ContentTesting.PhantomJS.LockNavigation" value ="true"/>

      <!-- CONTENT TESTING - PHANTOM JS - TIMEOUT
           The timeout in milliseconds used when executing calls to PhantomJS
           Default value: 60000
      -->
      <setting name="ContentTesting.PhantomJS.Timeout" value ="60000"/>

      <!-- CONTENT TESTING - TEST EXTENSION PERIOD
           The number of days that the test can be extended when no winner is found.
           Default value: 14
      -->
      <setting name="ContentTesting.TestExtensionPeriod" value="14"/>

      <!-- CONTENT TESTING - TEST STRATEGY
           The default content test strategy.
           Default value: /sitecore/system/Settings/Analytics/Multivariate Test Strategies/Subgroup Round Robin Sticky
      -->
      <setting name="ContentTesting.TestStrategy" value="/sitecore/system/Settings/Analytics/Multivariate Test Strategies/Subgroup Round Robin Sticky"/>

      <!-- CONTENT TESTING - TESTING INDEX NAME
           The default testing index name.
           Default value: sitecore_testing_index
      -->
      <setting name="ContentTesting.TestingIndexName" value ="sitecore_testing_index"/>
      
      <!-- CONTENT TESTING - SUGGESTED TEST INDEX NAME
           The default suggested test index name.
           Default value: sitecore_suggested_test_index
      -->
      <setting name="ContentTesting.SuggestedTestIndexName" value="sitecore_suggested_test_index"/>
    </settings>

    <commands>
      <command name="optimization:componentperformance" type="Sitecore.ContentTesting.Commands.ComponentPerformance, Sitecore.ContentTesting" />
      <command name="test:summary" type="Sitecore.ContentTesting.Commands.TestSummary, Sitecore.ContentTesting" />
    </commands>

    <!-- Email templates used for test notifications -->
    <emailTemplateCreators>
      <template name="WinnerFound" type="Sitecore.ContentTesting.Notifications.Creators.WinnerFoundTemplateCreator, Sitecore.ContentTesting">
        <templateFile>/sitecore/shell/Applications/Notification/EmailTemplates/WinnerFound.html</templateFile>
      </template>
      <template name="NoWinner" type="Sitecore.ContentTesting.Notifications.Creators.NoWinnerTemplateCreator, Sitecore.ContentTesting">
        <templateFile>/sitecore/shell/Applications/Notification/EmailTemplates/NoWinner.html</templateFile>
      </template>
      <template name="Rollback" type="Sitecore.ContentTesting.Notifications.Creators.RollbackTemplateCreator, Sitecore.ContentTesting">
        <templateFile>/sitecore/shell/Applications/Notification/EmailTemplates/RollbackPrevious.html</templateFile>
      </template>
    </emailTemplateCreators>

    <events>
      <!-- PRE-EMPTIVE SCREENSHOT GENERATION - LOCAL
           Enable the following event handler to generate screenshot files as items change thus reducing wait time
           within the UI when screenshots are displayed.
           This handler only works on CM instances. Never enable it on any other Sitecore instance.
      -->
      <!--<event name="item:saved">
        <handler type="Sitecore.ContentTesting.Events.GenerateScreenshot, Sitecore.ContentTesting" method="OnItemSaved">
          <excludeFields hint="list:ExcludeFieldFromComparison">
            <created>__created</created>
            <createdby>__created by</createdby>
            <updated>__updated</updated>
            <updatedby>__updated by</updatedby>
            <revision>__revision</revision>
            <sortorder>__sortorder</sortorder>
            <validfrom>__valid from</validfrom>
            <workflow>__workflow</workflow>
            <workflowstate>__workflow state</workflowstate>
            <lock>__lock</lock>
            <pageleveltestsetdefinition>__Page Level Test Set Definition</pageleveltestsetdefinition>
          </excludeFields>
        </handler>
      </event>-->
      <!-- PRE-EMPTIVE SCREENSHOT GENERATION - REMOTE
           Enable the following event handler to generate screenshot files as remote items change thus reducing wait time
           within the UI when screenshots are displayed.
           This handler only works on CM instances. Never enable it on any other Sitecore instance.
      -->
      <!--<event name="item:saved:remote">
        <handler type="Sitecore.ContentTesting.Events.GenerateScreenshot, Sitecore.ContentTesting" method="OnRemoteItemSaved">
          <excludeFields hint="list:ExcludeFieldFromComparison">
            <created>__created</created>
            <createdby>__created by</createdby>
            <updated>__updated</updated>
            <updatedby>__updated by</updatedby>
            <revision>__revision</revision>
            <sortorder>__sortorder</sortorder>
            <validfrom>__valid from</validfrom>
            <workflow>__workflow</workflow>
            <workflowstate>__workflow state</workflowstate>
            <lock>__lock</lock>
            <pageleveltestsetdefinition>__Page Level Test Set Definition</pageleveltestsetdefinition>
          </excludeFields>
        </handler>
      </event>-->
      <!-- ACTIVE TEST CACHE CLEARER
           This event handler clears data from the ActiveTest cache
      -->
	  
	  <event name="item:bucketing:starting">
		<handler type="Sitecore.ContentTesting.Events.PauseIndexingOnBucketing, Sitecore.ContentTesting" method="PauseIndexing"/>
	  </event>
	  
	  <event name="item:bucketing:ending">
		<handler type="Sitecore.ContentTesting.Events.PauseIndexingOnBucketing, Sitecore.ContentTesting" method="ResumeIndexing"/>
	  </event>
	  
	  <event name="item:unbucketing:starting">
		<handler type="Sitecore.ContentTesting.Events.PauseIndexingOnBucketing, Sitecore.ContentTesting" method="PauseIndexing"/>
	  </event>
	  
	  <event name="item:unbucketing:ending">
		<handler type="Sitecore.ContentTesting.Events.PauseIndexingOnBucketing, Sitecore.ContentTesting" method="ResumeIndexing"/>
	  </event>
	  
      <event name="publish:end">
        <handler type="Sitecore.ContentTesting.Events.ActiveTestCacheClearer, Sitecore.ContentTesting" method="Process"/>
      </event>
      
      <event name="publish:end:remote">
        <handler type="Sitecore.ContentTesting.Events.ActiveTestCacheClearer, Sitecore.ContentTesting" method="Process"/>
      </event>      
      
    </events>

    <!-- Behaviours used when forcing a test winner -->
    <forceWinnerBehaviors>
      <behavior name="New" type="Sitecore.ContentTesting.Rules.ForceWinnerBehavior.ForceNewExperienceWinner, Sitecore.ContentTesting"/>
      <behavior name="Original" type="Sitecore.ContentTesting.Rules.ForceWinnerBehavior.ForceOriginalExperienceWinner, Sitecore.ContentTesting"/>
    </forceWinnerBehaviors>

    <scheduling>
      <agent type="Sitecore.Tasks.CleanupAgent" method="Run">
        <files hint="raw:AddCommand">
          <remove folder="$(tempFolder)/screenshots">
            <patch:attribute name="maxAge">7.00:00:00</patch:attribute>
          </remove>
        </files>
      </agent>
    </scheduling>

    <pipelines>
      <!-- CALCULATE TEST SCORE
           Calculates the score for the test
      -->
      <calculateTestScore>
        <processor type="Sitecore.ContentTesting.Pipelines.CalculateTestScore.GetAveragePageViews, Sitecore.ContentTesting">
          <QueryDays>30</QueryDays>
        </processor>
        <processor type="Sitecore.ContentTesting.Pipelines.CalculateTestScore.CalculateBestEffect, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.CalculateTestScore.CalculateTestScore, Sitecore.ContentTesting"/>
      </calculateTestScore>

      <!-- DETERMINE TEST EXPOSURE
           Determine if a user should be exposed to a test
      -->
      <determineTestExposure>
        <processor type="Sitecore.ContentTesting.Pipelines.DetermineTestExposure.ModularTrafficAllocation, Sitecore.ContentTesting" />
      </determineTestExposure>

      <!-- DETERMINE TEST OWNER
           Identifies the user who owns a test
      -->
      <determineTestOwner>
        <processor type="Sitecore.ContentTesting.Pipelines.DetermineTestOwner.GetFromWorkflowHistory, Sitecore.ContentTesting">
          <TransitionalWorkflowStates hint="list:AddTransitionalWorkflowState">
            <WorkflowState>/sitecore/system/Workflows/Sample Workflow/Draft</WorkflowState>
          </TransitionalWorkflowStates>
          <FirstTransition>true</FirstTransition>
        </processor>
        <processor type="Sitecore.ContentTesting.Pipelines.DetermineTestOwner.GetFromStatistics, Sitecore.ContentTesting"/>
      </determineTestOwner>

      <!-- GENERATE VARIANT DESCRIPTION
           Generate a user friendly description for the variant in the experience.
      -->
      <generateVariantDescription>
        <processor type="Sitecore.ContentTesting.Pipelines.GenerateVariantDescription.GetPageVersionTestDescription, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GenerateVariantDescription.GetComponentTestDescription, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GenerateVariantDescription.GetPersonalizationTestDescription, Sitecore.ContentTesting"/>
      </generateVariantDescription>

      <getChromeData>
        <processor type="Sitecore.ContentTesting.Pipelines.GetChromeData.GetOptimizationRenderingChromeData, Sitecore.ContentTesting" patch:after="processor[@type='Sitecore.Pipelines.GetChromeData.GetRenderingChromeData, Sitecore.Kernel']"/>
      </getChromeData>

      <getContentEditorWarnings>
        <processor type="Sitecore.ContentTesting.Pipelines.GetContentEditorWarnings.GetContentTestingWarnings, Sitecore.ContentTesting" />
      </getContentEditorWarnings>

      <!-- GET CURRENT TEST COMBINATION
           Returns a test combination to expose to the current visitor.
      -->
      <getCurrentTestCombination>
        <processor type="Sitecore.ContentTesting.Pipelines.GetCurrentTestCombination.GetLatestTestingItemVersion, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetCurrentTestCombination.LoadTestSet, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetCurrentTestCombination.GetTestStrategyCombination, Sitecore.ContentTesting"/>
      </getCurrentTestCombination>

      <!-- GET ITEM WARNINGS FOR TEST
           Inspect an item being added to a page version test and raise issues that could prevent if from acting within the test
      -->
      <getItemWarningsForTest>
        <processor type="Sitecore.ContentTesting.Pipelines.GetItemWarningsForTest.PublishingRestrictions, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetItemWarningsForTest.Workflow, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetItemWarningsForTest.Presentation, Sitecore.ContentTesting"/>
      </getItemWarningsForTest>

      <getPageEditorNotifications>
        <processor type="Sitecore.ContentTesting.Pipelines.GetPageEditorNotifications.GetContentTestingNotifications, Sitecore.ContentTesting"/>
      </getPageEditorNotifications>

      <!-- GET SCREEN SHOT FOR URL
           Generate a screenshot for a page
      -->
      <getScreenShotForURL>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.GenerateFilename, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.CheckCachedImage, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.CheckDisabler, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.LoadUrl, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.RenderScripts, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.WriteScriptToDisk, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.GenerateScreenShot, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetScreenShotForURL.DeleteScript, Sitecore.ContentTesting"/>
      </getScreenShotForURL>

      <!-- GET TEST CANDIDATE VARIANT DESCRIPTION
           Generate a user friendly description for a test candidate variant
      -->
      <getTestCandidateVariantDescription>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidateVariantDescription.TestType, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidateVariantDescription.Path, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidateVariantDescription.Rendering, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidateVariantDescription.Rules, Sitecore.ContentTesting" />
      </getTestCandidateVariantDescription>

      <!-- GET TEST OBJECTIVES
           Locate options to list as objectives of a test
      -->
      <getTestObjectives>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestObjectives.GetDefaults,Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestObjectives.GetGoals,Sitecore.ContentTesting"/>
      </getTestObjectives>
      
      <!-- SESSIOND END
           Performs cleanup on session end
      -->
      <sessionEnd>
        <processor type="Sitecore.ContentTesting.Pipelines.SessionEnd.CleanupPreservedWorkflowArgs, Sitecore.ContentTesting"/>
      </sessionEnd>

      <!-- GET TEST CANDIDATES
           Inspect an item and identify test candidates for it
      -->
      <getTestCandidates>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidates.GetComponentTestCandidiates, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidates.GetPersonalizationTestCandidates, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.GetTestCandidates.GetPageVersionTestCandidates, Sitecore.ContentTesting">
          <IgnoreRuleChanges>True</IgnoreRuleChanges>
          <IgnoreComponentTestChanges>True</IgnoreComponentTestChanges>
          <excludeFields hint="list:ExcludeFieldFromComparison">
            <created>__created</created>
            <createdby>__created by</createdby>
            <updated>__updated</updated>
            <updatedby>__updated by</updatedby>
            <revision>__revision</revision>
            <sortorder>__sortorder</sortorder>
            <validfrom>__valid from</validfrom>
            <workflow>__workflow</workflow>
            <workflowstate>__workflow state</workflowstate>
            <lock>__lock</lock>
            <pageleveltestsetdefinition>__Page Level Test Set Definition</pageleveltestsetdefinition>
          </excludeFields>
        </processor>
      </getTestCandidates>

      <!-- GET TESTS
           Inspect an item and identify all tests for it
      -->
      <getTests>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTests.GetPageLevelTest, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTests.GetComponentTests, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.GetTests.GetPersonalizationTests, Sitecore.ContentTesting"/>
      </getTests>

      <httpRequestBegin>
        <processor type="Sitecore.ContentTesting.Pipelines.HttpRequest.SetReadOnlySessionStateForScreenshot, Sitecore.ContentTesting"/>
      </httpRequestBegin>

      <initialize>
        <processor type="Sitecore.ContentTesting.Pipelines.Initialize.RegisterContentTestingCommandRoute, Sitecore.ContentTesting" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Loader.InitializeRoutes, Sitecore.Mvc']" />
        <processor type="Sitecore.Pipelines.Initialize.PrecompileSpeakViews, Sitecore.Speak.Client" use="ContentTesting">
          <Paths>/sitecore/shell/client/Applications/ContentTesting</Paths>
        </processor>
      </initialize>

      <insertRenderings>
        <processor type="Sitecore.ContentTesting.Pipelines.InsertRenderings.GetLatestTestingItem, Sitecore.ContentTesting" patch:after="processor[@type='Sitecore.Pipelines.InsertRenderings.Processors.GetItem, Sitecore.Kernel']"/>
      </insertRenderings>
      
      <itemWebApiSearch>
        <processor patch:after="processor[@type='Sitecore.ItemWebApi.Pipelines.Search.FilterItems, Sitecore.Speak.ItemWebApi']" type="Sitecore.ContentTesting.Pipelines.ItemWebApiSearch.FilterLatestVersion, Sitecore.ContentTesting" />
      </itemWebApiSearch>

      <publishVersion>
        <processor type="Sitecore.ContentTesting.Pipelines.PublishVersion.PublishTestingVersions, Sitecore.ContentTesting" patch:before="processor[@type='Sitecore.Publishing.Pipelines.PublishVersion.Processors.RemoveOtherVersions, Sitecore.Kernel']"/>
      </publishVersion>

      <renderField>
        <processor type="Sitecore.ContentTesting.Pipelines.RenderField.HighlightContentChange, Sitecore.ContentTesting" patch:after="processor[@type='Sitecore.Pipelines.RenderField.AddBeforeAndAfterValues, Sitecore.Kernel']"/>
      </renderField>

      <renderLayout>
        <processor type="Sitecore.ContentTesting.Pipelines.RenderLayout.EvaluateTestExposure, Sitecore.ContentTesting" patch:before="processor[@type='Sitecore.Analytics.Pipelines.RenderLayout.PageLevelTestItemResolver, Sitecore.Analytics']" />
      </renderLayout>

      <!-- SAVE TEST OUTCOME
           Process and save outcome data of the test
      -->
      <saveTestOutcome>
        <processor type="Sitecore.ContentTesting.Pipelines.SaveTestOutcome.AddSummaryNotification, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.SaveTestOutcome.GenerateTestOutcome, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.SaveTestOutcome.DetermineTestOwner, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.SaveTestOutcome.WriteTestOutcome, Sitecore.ContentTesting" />
      </saveTestOutcome>
      <!-- START TEST
           Starts the test
      -->
      <startTest>
        <processor type="Sitecore.ContentTesting.Pipelines.StartTest.DeployPageTestDependencies, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.StartTest.DeployComponentTestDependencies, Sitecore.ContentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.StartTest.DeployTest, Sitecore.ContentTesting"/>
      </startTest>

      <!-- STOP TEST
           Stops the test
      -->
      <stopTest>
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.StoreWinnerCombination, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.ProcessTestWorkflow, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.CreateWinnerVersion, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.SetupWinnerComponents, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.RemovePoorPerformingRules, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.PublishWinner, Sitecore.ContentTesting" />
        <processor type="Sitecore.ContentTesting.Pipelines.StopTest.RunClustering, Sitecore.ContentTesting"/>
      </stopTest>

      <!-- SUGGEST TESTS
           Inspect an item and suggest tests which may be run against it
      -->
      <suggestTests>
        <processor type="Sitecore.ContentTesting.Pipelines.SuggestTests.HasNeverBeenTested, Sitecore.ContentTesting"/>
      </suggestTests>
      
      <validateStopAction>
        <processor type="Sitecore.ContentTesting.Pipelines.ValidateStopAction.ValidateTestOwner, Sitecore.COntentTesting"/>
        <processor type="Sitecore.ContentTesting.Pipelines.ValidateStopAction.ValidatePageTestTemplate, SItecore.ContentTesting"/>
      </validateStopAction>

      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Applications/ContentTesting" deep="true" category="contenttesting" pattern="*.js,*.css" />
            <source folder="/sitecore/shell/client/Sitecore/ContentTesting" deep="true" category="contenttesting" pattern="*.js,*.css" />
          </sources>
        </processor>
      </speak.client.resolveScript>

      <group name="itemProvider" groupName="itemProvider">
        <pipelines>
          <getItem>
            <processor type="Sitecore.ContentTesting.Pipelines.ItemProvider.GetItem.GetItemUnderTestProcessor, Sitecore.ContentTesting" />
          </getItem>
        </pipelines>
      </group>
    </pipelines>

    <sitecore.experienceeditor.speak.requests>
      <request name="Optimization.ActiveTests.Count" type="Sitecore.ContentTesting.Requests.ExperienceEditor.ActiveTestsCountRequest, Sitecore.ContentTesting"/>
      <request name="Optimization.ActiveItemTests.Count" type="Sitecore.ContentTesting.Requests.ExperienceEditor.ActiveItemTestsCountRequest, Sitecore.ContentTesting"/>
      <request name="Optimization.ActiveItemTest" type="Sitecore.ContentTesting.Requests.ExperienceEditor.ItemHasActiveTestRequest, Sitecore.ContentTesting"/>
      <request name="Optimization.HistoricalTests.Count" type="Sitecore.ContentTesting.Requests.ExperienceEditor.HistoricalTestsCountRequest, Sitecore.ContentTesting"/>
      <request name="Optimization.SuggestedTests.Count" type="Sitecore.ContentTesting.Requests.ExperienceEditor.SuggestedTestsCountRequest, Sitecore.ContentTesting"/>
      <request name="Optimization.SuggestedTests.Count" type="Sitecore.ContentTesting.Requests.ExperienceEditor.SuggestedTestsCountRequest, Sitecore.ContentTesting"/>
      <request name="ContentTesting.PersonalizationGallery.CanExecute" type="Sitecore.ContentTesting.Requests.ExperienceEditor.PersonalizationGalleryState, Sitecore.ContentTesting"/>
      <request name="OptimizationView.Toogle.GetUrl" type="Sitecore.ContentTesting.Requests.ExperienceEditor.ToogleOptimizationViewRequest, Sitecore.ContentTesting"/>
    </sitecore.experienceeditor.speak.requests>

    <!-- Content Testing Factory configuration
         The types listed here are used by the ContentTestingFactory. Override these settings to override the individual factories.
    -->
    <contentTesting>
      <!-- Concrete implementation types -->
      <contentTestingFactory type="Sitecore.ContentTesting.ContentTestingFactory, Sitecore.ContentTesting"/>

      <!-- Content Test Performance Factory implementation type-->
      <contentTestPerformanceFactory type="Sitecore.ContentTesting.Reports.ContentTestPerformanceFactory, Sitecore.ContentTesting"/>

      <!-- Content Test Store implementation type -->
      <contentTestStore type="Sitecore.ContentTesting.Data.SitecoreContentTestStore, Sitecore.ContentTesting"/>

      <forceWinnerBehaviorFactory type="Sitecore.ContentTesting.Rules.ForceWinnerBehavior.ForcedWinnerBehaviorFactory, Sitecore.ContentTesting">
        <forceWinnerBehaviors hint="raw:AddBehaviorRaw">
          <new name="New" type="Sitecore.ContentTesting.Rules.ForceWinnerBehavior.ForceNewExperienceWinner, Sitecore.ContentTesting"/>
          <original name="Original" type="Sitecore.ContentTesting.Rules.ForceWinnerBehavior.ForceOriginalExperienceWinner, Sitecore.ContentTesting"/>
        </forceWinnerBehaviors>
      </forceWinnerBehaviorFactory>

      <testingSearch type="Sitecore.ContentTesting.ContentSearch.TestingSearch, Sitecore.ContentTesting"/>
    </contentTesting>
  </sitecore>
</configuration>