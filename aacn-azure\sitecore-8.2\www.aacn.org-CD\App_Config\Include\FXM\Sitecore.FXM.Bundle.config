﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose
________

This include file configures request bundles that are generated once during an application instance and cached for
the duration of it's lifetime.  

Description
___________

It provides two extension points that hook into either the Sitecore application startup
or the first request made for the bundle.  In order to create and register a bundle, a pipeline 
must be included with a name that matches the bundle name.  Some helpful processors already exist
to perform some common javascript and css tasks.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <settings>
      <!-- BUNDLE BASE PATH
        All created bundles will be available at $(bundle.basepath)/$(bundlename)
        Must start with "~/"
      -->
      <setting name="Bundle.BasePath" value="~/bundle"/>
      
      <!-- BUNDLE PIPELINE NAME PREFIX
        A bundle pipeline is execute to create and register each bundle.  The name of that pipeline is expected to 
        be <PipelineNamePrefix><bundle name>.
      -->
      <setting name="Bundle.PipelineNamePrefix" value="bundle."/>
    </settings>
    
    <bundling defaultProvider="default" enabled="true">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.FXM.Bundling.BundleProvider, Sitecore.FXM"/>
      </providers>
    </bundling>
    
    <!-- **** Bundles Registered on Application startup **** -->
    
    <!-- (Though not requried by FXM, this could be achieved with the following config. -->
    <!--<hooks>
      <hook type="Sitecore.FXM.Hooks.BundleRegistrarHook, Sitecore.FXM">        
        <bundles hint="list:AddBundle">          
          <bundle>##bundle_name##</bundle>
        </bundles>
      </hook>
    </hooks>-->

    <pipelines>

      <!-- **** Bundles registered when they are first request **** -->
      
      <httpRequestBegin>
        <!-- Register bundles on their first request -->
        <processor patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.IgnoreList, Sitecore.Kernel']"
                   type="Sitecore.FXM.Pipelines.HttpRequest.OnRequestBundleRegistrarProcessor, Sitecore.FXM">
          <bundles hint="list:AddBundle">
            <!-- The FXM client javascript bundle - see registration pipeline below.  It requires the current Sitecore instance host name
                 when creating the bundle, so must be generated as part of a Sitecore request. -->
            <bundle>beacon</bundle>
          </bundles>
        </processor>

        <!-- Ensure registered bundles are ignored by http request pipeline -->
        <processor patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.IgnoreList, Sitecore.Kernel']"
                   type="Sitecore.FXM.Pipelines.HttpRequest.IgnoreBundleEntries, Sitecore.FXM">
        </processor>
      </httpRequestBegin>
      
      <httpRequestEnd>
        <!-- Ensure registered bundles are ignored by http request pipeline -->
        <processor patch:before="*[1]"
                   type="Sitecore.FXM.Pipelines.HttpRequest.IgnoreBundleEntries, Sitecore.FXM">
        </processor>
      </httpRequestEnd>
      
      <!-- **** Bundle Registration Pipelines **** -->
      <!--  Note: pipeline names must be prefixed with $(Bundle.PipelineNamePrefix) - see setting above. -->
      
      <bundle.beacon>
        <!-- Registers the FXM client Javascript bundle -->
        <processor type="Sitecore.FXM.Pipelines.Bundle.BundleFileProcessor, Sitecore.FXM">
          <path>sitecore/shell/client/services/assets/lib/beaconApi.js</path>
        </processor>
        <processor type="Sitecore.FXM.BeaconLoaderProcessor, Sitecore.FXM" />
        <processor type="Sitecore.FXM.Pipelines.Bundle.BundleRegisterProcessor, Sitecore.FXM"/>
      </bundle.beacon>

      <!-- Other bundle processors currently available for use. -->
      <!--<processor type="Sitecore.FXM.Pipelines.Bundle.BundleJsMinifyProcessor, Sitecore.FXM" />-->
      <!--<processor type="Sitecore.FXM.Pipelines.Bundle.CssMinifyProcessor, Sitecore.FXM" />-->

    </pipelines>

  </sitecore>
</configuration>