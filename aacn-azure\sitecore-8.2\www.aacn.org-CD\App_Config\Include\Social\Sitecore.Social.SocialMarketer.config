<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures a plugin for the Social Connected module that allows content authors to create social marketer messages.

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <api>
        <domainMessageBuilders>
          <builder networkName="Social Marketer"
                   builderType="Sitecore.Social.SocialMarketer.Client.Builders.SocialMarketerDomainMessageBuilder, Sitecore.Social.SocialMarketer.Client" />
        </domainMessageBuilders>
        <messageDataBuilders>
          <builder messageItemBaseType="Sitecore.Social.SocialMarketer.MessagePosting.Items.SocialMarketerMessageItem, Sitecore.Social.SocialMarketer"
                   messageDataType="Sitecore.Social.SocialMarketer.Api.Model.SocialMarketerMessageData, Sitecore.Social.SocialMarketer.Api"
                   builderType="Sitecore.Social.SocialMarketer.Api.Builders.SocialMarketerMessageDataBuilder, Sitecore.Social.SocialMarketer.Api" />
        </messageDataBuilders>
      </api>
      <networks>
        <network name="Social Marketer" ItemId="{30B51145-D0E0-42FA-8BDF-9EFD4E91C84B}" icon="social_marketer">
          <items>
            <message type="Sitecore.Social.SocialMarketer.MessagePosting.Items.SocialMarketerMessageItem, Sitecore.Social.SocialMarketer"
                     Renderer="Sitecore.Social.SocialMarketer.Client.MessagePosting.Renderers.SocialMarketerMessageRenderer, Sitecore.Social.SocialMarketer.Client"
                     MessageTemplateId="{06751E29-D91B-423C-A2E1-E54AFD4F8F0B}"
                     TextLimit="65536"/>
          </items>
          <messagePosting>
            <campaigns rootCampaignItemId="{031FA6AD-D948-4F1A-B97B-FC2D00AE630C}" channelId="{1DA15267-B0DB-4BE1-B44F-E57C2EEB8A6B}">
              <campaign postingConfiguration="ContentPosting" itemId="{CB1DDA9C-8DB1-4E6F-A9B3-12622FE82C74}"/>
            </campaigns>
          </messagePosting>
          <providers>
            <provider type="Sitecore.Social.SocialMarketer.Networks.Providers.SocialMarketerProvider, Sitecore.Social.SocialMarketer"/>
          </providers>
        </network>
      </networks>
    </social>
  </sitecore>
</configuration>