﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the marketing taxonomy management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <commands>
      <command name="marketingtaxonomy:deploy" type="Sitecore.Marketing.Taxonomy.Shell.Commands.Taxonomies.Deploy, Sitecore.Marketing.Taxonomy" />
    </commands>

    <hooks>
      <!-- Initializes all taxonomy managers -->
      <hook type="Sitecore.Marketing.Taxonomy.Hooks.Initializer, Sitecore.Marketing.Taxonomy" />
      <!-- Initializes the deploy manager -->
      <hook type="Sitecore.Marketing.Taxonomy.Hooks.DeployInitializer, Sitecore.Marketing.Taxonomy" />
    </hooks>

    <taxonomy>
      <!-- Known Managers -->
      <knownManagers>
        <campaignGroupTaxonomyManager type="Sitecore.Marketing.Taxonomy.CampaignGroupTaxonomyManager, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <param desc="repository" ref="taxonomy/repositories/itemTaxonomyRepository" />
          <param desc="mapper" ref="taxonomy/mappers/taxonomyTypeMapper" />
          <param desc="cacheSize">20MB</param>
        </campaignGroupTaxonomyManager>

        <channelTaxonomyManager type="Sitecore.Marketing.Taxonomy.ChannelTaxonomyManager, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <param desc="repository" ref="taxonomy/repositories/itemTaxonomyRepository" />
          <param desc="mapper" ref="taxonomy/mappers/taxonomyTypeMapper" />
          <param desc="cacheSize">20MB</param>
        </channelTaxonomyManager>

        <assetTaxonomyManager type="Sitecore.Marketing.Taxonomy.AssetTaxonomyManager, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <param desc="repository" ref="taxonomy/repositories/itemTaxonomyRepository" />
          <param desc="mapper" ref="taxonomy/mappers/taxonomyTypeMapper" />
          <param desc="cacheSize">20MB</param>
        </assetTaxonomyManager>
      </knownManagers>

      <!-- Custom manager created as needed -->
      <customTaxonomyManager type="Sitecore.Marketing.Taxonomy.CustomTaxonomyManager, Sitecore.Marketing.Taxonomy" singleInstance="true">
        <param desc="repository" ref="taxonomy/repositories/itemTaxonomyRepository" />
        <param desc="mapper" ref="taxonomy/mappers/taxonomyTypeMapper" />
        <param desc="cacheSize">20MB</param>
      </customTaxonomyManager>

      <!-- Repositories -->
      <repositories>
        <itemTaxonomyRepository type="Sitecore.Marketing.Taxonomy.Data.ItemDb.ItemTaxonomyRepository, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <param desc="databaseName">master</param>
        </itemTaxonomyRepository>

        <rdbTaxonomyRepository type="Sitecore.Marketing.Taxonomy.Data.Rdb.RdbTaxonomyRepository, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <param desc="connectionStringName">reporting</param>
        </rdbTaxonomyRepository>
      </repositories>

      <!-- Deployment -->
      <!-- Use the rdb repository-->
      <deployHandler ref="taxonomy/repositories/rdbTaxonomyRepository" />
      
      <!-- Mappers -->
      <mappers>
        <taxonomyTypeMapper type="Sitecore.Marketing.Taxonomy.Mapping.TaxonomyTypeMapper, Sitecore.Marketing.Taxonomy" singleInstance="true">
          <mappers hint="list:AddMapper">

            <!-- Campaign -->
            <campaignTaxonomyMapper type="Sitecore.Marketing.Taxonomy.Mapping.CampaignGroup.CampaignGroupTaxonomyMapper, Sitecore.Marketing.Taxonomy"/>
            <campaignMapper type="Sitecore.Marketing.Taxonomy.Mapping.CampaignGroup.CampaignGroupMapper, Sitecore.Marketing.Taxonomy"/>

            <!-- Channel -->
            <channelTaxonomyMapper type="Sitecore.Marketing.Taxonomy.Mapping.Channel.ChannelTaxonomyMapper, Sitecore.Marketing.Taxonomy"/>
            <channelTypeMapper type="Sitecore.Marketing.Taxonomy.Mapping.Channel.ChannelTypeMapper, Sitecore.Marketing.Taxonomy"/>
            <channelGroupMapper type="Sitecore.Marketing.Taxonomy.Mapping.Channel.ChannelGroupMapper, Sitecore.Marketing.Taxonomy"/>
            <channelMapper type="Sitecore.Marketing.Taxonomy.Mapping.Channel.ChannelMapper, Sitecore.Marketing.Taxonomy"/>

            <!-- Asset -->
            <assetTaxonomyMapper type="Sitecore.Marketing.Taxonomy.Mapping.Asset.AssetTaxonomyMapper, Sitecore.Marketing.Taxonomy"/>
            <assetGroupMapper type="Sitecore.Marketing.Taxonomy.Mapping.Asset.AssetGroupMapper, Sitecore.Marketing.Taxonomy"/>
            <assetMapper type="Sitecore.Marketing.Taxonomy.Mapping.Asset.AssetMapper, Sitecore.Marketing.Taxonomy"/>

            <!-- Custom -->
            <customTaxonomyMapper type="Sitecore.Marketing.Taxonomy.Mapping.Custom.CustomTaxonomyMapper, Sitecore.Marketing.Taxonomy"/>
            <customGroupMapper type="Sitecore.Marketing.Taxonomy.Mapping.Custom.CustomTaxonGroupMapper, Sitecore.Marketing.Taxonomy"/>
            <customTaxonMapper type="Sitecore.Marketing.Taxonomy.Mapping.Custom.CustomTaxonMapper, Sitecore.Marketing.Taxonomy"/>

          </mappers>
        </taxonomyTypeMapper>
      </mappers>
      
    </taxonomy>

    <pipelines>
      <group name="marketingTaxonomy" groupName="marketingTaxonomy">
        <pipelines>
          <resolveUnknownTaxon>
              <processor type="Sitecore.Marketing.Taxonomy.Pipelines.ResolveUnknownTaxon.DefaultUnknownTaxonResolver, Sitecore.Marketing.Taxonomy" />
              <processor type="Sitecore.Marketing.Taxonomy.Pipelines.ResolveUnknownTaxon.DefaultUnknownTaxonomyResolver, Sitecore.Marketing.Taxonomy" />
          </resolveUnknownTaxon>
        </pipelines>
      </group>
    </pipelines>
  </sitecore>
</configuration>