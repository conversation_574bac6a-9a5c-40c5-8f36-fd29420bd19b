﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
    <sitecore>
        <contentSearch>
            <configuration type="Sitecore.ContentSearch.ContentSearchConfiguration, Sitecore.ContentSearch">
                <indexes hint="list:AddIndex">
                    <index id="sitecore_web_index" type="Sitecore.ContentSearch.SolrProvider.SolrSearchIndex, Sitecore.ContentSearch.SolrProvider">
                        <param desc="name">$(id)</param>
                        <param desc="core">$(id)</param>
                        <param desc="propertyStore" ref="contentSearch/indexConfigurations/databasePropertyStore" param1="$(id)" />
                        <configuration ref="contentSearch/indexConfigurations/defaultSolrIndexConfiguration">
                            <fields hint="raw:AddComputedIndexField">
                                <field fieldName="clinicalsystems" returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.ClinicalSystems, Aacn.Business</field>
                                <field fieldName="professionalpractice" returnType="stringCollection">Aacn.Business.ContentSearch.ComputedFields.ProfessionalPractice, Aacn.Business</field>
                            </fields>
                        </configuration>
                        <strategies hint="list:AddStrategy">
                            <strategy ref="contentSearch/indexConfigurations/indexUpdateStrategies/onPublishEndAsync" />
                        </strategies>
                        <locations hint="list:AddCrawler">
                            <crawler type="Sitecore.ContentSearch.SitecoreItemCrawler, Sitecore.ContentSearch">
                                <Database>web</Database>
                                <Root>/sitecore</Root>
                            </crawler>
                        </locations>
                    </index>
                </indexes>
            </configuration>
        </contentSearch>
    </sitecore>
</configuration>