﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file enables the processing subsystem of the Sitecore Experience Database (xDB) by launching the processing Task
Manager and services on this server.  

If the current server does not need to perform processing tasks, you can rename this config file so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <hooks>
      <hook type="Sitecore.Analytics.Processing.ProcessingLoader, Sitecore.Analytics.Processing"/>
    </hooks>

    <processing>
      <module type="Sitecore.Analytics.Processing.ProcessingModule" singleInstance="true">
        <BackgroundServices hint="list:Add">
          <taskagent type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">processing/taskAgent</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </taskagent>
        </BackgroundServices>
      </module>
    </processing>
  </sitecore>
</configuration>