﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the Sitecore.Services.Client service layer. The service layer allows you to create consistent
client-server communication for your applications, using the .NET Web API as the foundation.
    
Important: Changing the settings in this file may impact the security of your Sitecore installation. Please consider any changes carefully
before implementing them on a production server. If you change the SecurityPolicy setting or the settings related to anonymous users, you
may allow third parties to access content directly on your server without any security restrictions.
    
Please consult your Sitecore partner before making any changes to the settings in this file.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!--  SITECORE SERVICES SECURITY POLICY
            Specifies the Sitecore.Services.Infrastructure.Web.Http.Security.IAuthorizePolicy derived type that will set the security policy 
            for the Sitecore services.
            
            Policies:
            
            Sitecore.Services.Infrastructure.Web.Http.Security.ServicesOffPolicy, Sitecore.Services.Infrastructure
            - Policy denies access to all Entity and Item Services
            
            Sitecore.Services.Infrastructure.Web.Http.Security.ServicesLocalOnlyPolicy, Sitecore.Services.Infrastructure
            - Policy denies access to all Entity and Item Services from requests originating from remote clients
            
            Sitecore.Services.Infrastructure.Web.Http.Security.ServicesOnPolicy, Sitecore.Services.Infrastructure
            - Policy allows access to all Entity and Item Services

            Default: Sitecore.Services.Infrastructure.Web.Http.Security.ServicesLocalOnlyPolicy, Sitecore.Services.Infrastructure      
      -->
      <setting name="Sitecore.Services.SecurityPolicy" value="Sitecore.Services.Infrastructure.Web.Http.Security.ServicesLocalOnlyPolicy, Sitecore.Services.Infrastructure" />

      <!--  SITECORE SERVICES ROUTE MAPPER
            Specifies the Sitecore.Services.Infrastructure.Web.Http.IMapRoutes derived type that will configure routes for the Sitecore services
            Default: Sitecore.Services.Infrastructure.Web.Http.DefaultRouteMapper, Sitecore.Services.Infrastructure    
      -->
      <setting name="Sitecore.Services.RouteMapper" value="Sitecore.Services.Infrastructure.Web.Http.DefaultRouteMapper, Sitecore.Services.Infrastructure" />

      <!--  SITECORE EXTENSION VALIDATION FOLDERPATH
            Specifies the folder in which to locate javascript validator extensions
            Default: /sitecore/shell/client/Services/Assets/lib/extensions/validators      
      -->
      <setting name="Sitecore.Extensions.Validation.FolderPath" value="/sitecore/shell/client/Services/Assets/lib/extensions/validators" />

      <!--  SITECORE SERVICES ALLOW ANONYMOUS USER
            Specifies whether anonymous users will be permitted to access the Sitecore Item Web API services
            Default: false      
      -->
      <setting name="Sitecore.Services.AllowAnonymousUser" value="false" />

      <!--  SITECORE SERVICES ANONYMOUS USER
            Specifies the user to impersonate if a request from an anonymous user is permitted to 
            access the Sitecore Item Web API services
            Default: sitecore\ServicesAPI      
      -->
      <setting name="Sitecore.Services.AnonymousUser" value="sitecore\ServicesAPI" />
    </settings>

    <pipelines>
      <initialize>
        <processor type="Sitecore.Services.Infrastructure.Sitecore.Pipelines.ServicesWebApiInitializer, Sitecore.Services.Infrastructure.Sitecore"
                   patch:after="processor[@type='Sitecore.Mvc.Pipelines.Loader.InitializeRoutes, Sitecore.Mvc']" />
      </initialize>
    </pipelines>
    
    <!-- SITECORE SERVICES WEB API FILTERS 
         Specifies the list of Web API filters to load for request handling
    -->
    <api>
      <services>
        <configuration type="Sitecore.Services.Infrastructure.Configuration.ServicesConfiguration, Sitecore.Services.Infrastructure">
          <filters hint="list:AddFilter">
            <filter>Sitecore.Services.Infrastructure.Web.Http.Filters.AnonymousUserFilter, Sitecore.Services.Infrastructure</filter>
            <filter>Sitecore.Services.Infrastructure.Web.Http.Filters.SecurityPolicyAuthorisationFilter, Sitecore.Services.Infrastructure</filter>
            <filter>Sitecore.Services.Infrastructure.Web.Http.Filters.LoggingExceptionFilter, Sitecore.Services.Infrastructure</filter>
            <!-- 
            
            Decomment this section to mandate HTTPS for all Web API requests to the site
            
            <filter>Sitecore.Services.Infrastructure.Web.Http.Filters.RequireHttpsFilter, Sitecore.Services.Infrastructure</filter>
            
            -->
          </filters>
          <allowedControllers hint="list:AddController">
            <!-- 
            
            Add allowedController elements here for any controllers that should be exempt 
            from the Sitecore Services Security Policy
            
            <allowedController>...</allowedController>
            
            -->
          </allowedControllers>
        </configuration>
      </services>
    </api>
  </sitecore>
</configuration>