﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the plugin to the Social Connected module that enables integration with Facebook (https://www.facebook.com).

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      
      <!-- SOCIAL - FACEBOOK - DEFAULT LIKE GOAL
           The Sitecore Social Connected goal that is triggered when a visitor clicks the "Like" button.
           Default value: Like
      -->
      <setting name="Social.Facebook.DefaultLikeGoal" value="Like"/>
      
      <!-- SOCIAL - FACEBOOK - HAS WRITE ACCESS
           Specifies if your app can post content, comments, and likes to a user's stream and to the streams of the user's friends. 
           This permission allows you to publish content to a user's feed at any time and does not require offline_access.
           Default value: true 
      -->
      <setting name="Social.Facebook.HasWriteAccess" value="true" />
      
      <!-- SOCIAL - FACEBOOK - IS OFFLINE ACCESS
           Specifies if the Social Connector will request the Facebook "offline_access" permission.
           Default value: true
      -->
      <setting name="Social.Facebook.IsOfflineAccess" value="false" />
      
    </settings>

    <social>
      <api>
        <messageDataBuilders>
          <builder messageItemBaseType="Sitecore.Social.Facebook.MessagePosting.Items.FacebookMessageItem, Sitecore.Social.Facebook"
                   messageDataType="Sitecore.Social.Facebook.Api.Model.FacebookMessageData,Sitecore.Social.Facebook.Api"
                   builderType="Sitecore.Social.Facebook.Api.Builders.FacebookMessageDataBuilder,Sitecore.Social.Facebook.Api" />
        </messageDataBuilders>
        <domainMessageBuilders>
          <builder networkName="Facebook"
                   builderType="Sitecore.Social.Facebook.Client.Builders.FacebookDomainMessageBuilder,Sitecore.Social.Facebook.Client" />
        </domainMessageBuilders>
      </api>

      <!-- Personalization -->
      <!-- This section configures the gender settings in the user profile. -->
      <genderRule>
        <network name="Facebook">
          <gender sitecoreKey="fb_gender" maleValue="male" femaleValue="female" />
        </network>
      </genderRule>

      <!-- This section configures the Interests field in the user profile. -->
      <interestedInRule>
        <network name="Facebook">
          <field sitecoreKey="fb_likes" />
          <field sitecoreKey="fb_movies" />
          <field sitecoreKey="fb_music" />
          <field sitecoreKey="fb_games" />
          <field sitecoreKey="fb_languages" />
          <field sitecoreKey="fb_favorite_athletes" />
          <field sitecoreKey="fb_favorite_teams" />
          <field sitecoreKey="fb_books" />
          <field sitecoreKey="fb_television" />
          <field sitecoreKey="fb_interests" />
        </network>
      </interestedInRule>

      <networks>
        <network name="Facebook" AccountExpirationDays="60" ItemId="{D7429409-D6DA-46CD-91DD-8702781AE3C4}" prefix="fb" icon="facebook3" url="http://facebook.com">
          <items>
            <message type="Sitecore.Social.Facebook.MessagePosting.Items.FacebookMessageItem, Sitecore.Social.Facebook"
                     MessageTemplateId="{D7851F00-E869-4F93-81F5-B5338EFF58A9}"
                     Renderer="Sitecore.Social.Facebook.Client.MessagePosting.Renderers.FacebookMessageRenderer, Sitecore.Social.Facebook.Client"
                     TextLimit="63000"/>
          </items>
          <messagePosting>
            <campaigns rootCampaignItemId="{6DBF6E13-5757-4C15-AB61-D610C195AE5F}" channelId="{A9F2D058-95A5-4461-B1E5-8502D2303AF1}">
              <campaign postingConfiguration="ContentPosting" itemId="{0BFFAF94-F523-452A-9F2A-1FA3292D4647}"/>
              <campaign postingConfiguration="GoalPosting" itemId="{EAEF3BE2-3800-48D4-9199-A88C3947F752}"/>
            </campaigns>
            <publisher type="Sitecore.Social.Facebook.MessagePosting.Providers.FacebookMessagePostingProvider, Sitecore.Social.Facebook"/>
          </messagePosting>
          <permissionBuilders>
            <permissionBuilder type="Sitecore.Social.Facebook.Connector.PermissionBuilders.FacebookPermissionBuilder, Sitecore.Social.Facebook" />
          </permissionBuilders>
          <providers>
            <provider type="Sitecore.Social.Facebook.Networks.Providers.FacebookProvider, Sitecore.Social.Facebook"/>
          </providers>
        </network>
      </networks>
      
    </social>

    <commands>
      <command name="social:facebook:account:add" type="Sitecore.Social.Facebook.Client.Commands.AddFacebookAccount, Sitecore.Social.Facebook.Client"/>
    </commands>

    <pipelines>
      <social.matchUser>
        <processor type="Sitecore.Social.Facebook.Connector.Pipelines.MatchUser.FindByFacebookIdsForBusiness, Sitecore.Social.Facebook" patch:before="processor[@type='Sitecore.Social.Connector.Pipelines.MatchUser.CreateUser, Sitecore.Social']" />
      </social.matchUser>
      <social.initializeApi>
        <processor type="Sitecore.Social.Facebook.IoC.IoCInitialization, Sitecore.Social.Facebook" patch:after="processor[@type='Sitecore.Social.Infrastructure.IoC.IoCInitialization, Sitecore.Social.Infrastructure']">
          <assemblies hint="list:AddAssembly">
            <assembly>Sitecore.Social.Facebook.Api.dll</assembly>
          </assemblies>
        </processor>
      </social.initializeApi>
    </pipelines>
    
    <ui>
      <usings>
        <using patch:after="using[.='Sitecore.Social.Client.Wizards.AddNetworkAccount']">Sitecore.Social.Facebook.Client.Wizards.AddNetworkAccount</using>
      </usings>
      <references>
        <reference patch:after="reference[.='/bin/Sitecore.Social.Client.dll']">/bin/Sitecore.Social.Facebook.Client.dll</reference>
        <reference patch:after="reference[.='System.dll']">System.Web.dll</reference>
      </references>
    </ui>
  </sitecore>
</configuration>
