﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file contains configuration settings for the Sitecore Path Analyzer processing agents.

This file should only be used in Sitecore instances functioning as xDB processing/aggregation instances. On all other
instance types (e.g. Content Delivery), this file can be removed or disabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- HOOKS -->
    <hooks>
      <hook type="Sitecore.PathAnalyzer.Processing.SubsystemLoader, Sitecore.PathAnalyzer"/>
    </hooks>
    <!-- PATH ANALYZER
          Define the agents and processors that are used to create and merge Path Analyzer maps.
    -->
    <pathAnalyzer>
      <subsystem type="Sitecore.Analytics.Core.Subsystem" singleInstance="true">
        <BackgroundServices hint="list:Add">
          <newMap type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">pathAnalyzer/newMapAgent</param>
            <Interval>0.00:05:00</Interval>
            <MaxThreads>1</MaxThreads>
          </newMap>
          <dailyMap type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">pathAnalyzer/dailyMapAgent</param>
            <Interval>0.00:10:00</Interval>
            <MaxThreads>1</MaxThreads>
          </dailyMap>
          <smartMerge type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">pathAnalyzer/smartMergeAgent</param>
            <Interval>0.00:15:00</Interval>
            <MaxThreads>1</MaxThreads>
          </smartMerge>
        </BackgroundServices>
      </subsystem>
      <!-- DAILY MAP AGENT
            This agent is responsible for creating maps from relevant visit data on a daily basis.
      -->
      <dailyMapAgent type="Sitecore.PathAnalyzer.Processing.Agents.DailyMapAgent, Sitecore.PathAnalyzer"/>
      <!-- NEW MAP AGENT
            This agent is responsible for creating new maps from a newly-deployed map definition.
      -->
      <newMapAgent type="Sitecore.PathAnalyzer.Processing.Agents.NewMapAgent, Sitecore.PathAnalyzer"/>
      <!-- SMART MERGE AGENT
            This agent is responsible for creating and saving "merged" trees based on tree data that
            has previously been written to storage. "Smart merge" approach is used to select the
            trees that should be merged.
      -->
      <smartMergeAgent type="Sitecore.PathAnalyzer.Processing.Agents.SmartMergeAgent, Sitecore.PathAnalyzer"/>
    </pathAnalyzer>
  </sitecore>
</configuration>
