﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<events>
			<event name="item:saved">
				<handler type="DynamicPlaceholders.Events.DynamicPlaceholder, DynamicPlaceholders" method="OnItemSaved" />
			</event>
		</events>
		<pipelines>
			<getPlaceholderRenderings>
				<processor type="DynamicPlaceholders.Pipelines.GetPlaceholderRenderings.GetDynamicKeyAllowedRenderings, DynamicPlaceholders" patch:before="processor[@type='Sitecore.Pipelines.GetPlaceholderRenderings.GetAllowedRenderings, Sitecore.Kernel']"/>
			</getPlaceholderRenderings>
			<getChromeData>
				<processor type="Sitecore.Pipelines.GetChromeData.GetPlaceholderChromeData, Sitecore.Kernel">
					<patch:attribute name="type">DynamicPlaceholders.Pipelines.GetChromeData.GetDynamicPlaceholderChromeData, DynamicPlaceholders</patch:attribute>
				</processor>
			</getChromeData>
			<executePageEditorAction>
				<processor type="Sitecore.Pipelines.ExecutePageEditorAction.ReplaceRendering, Sitecore.ExperienceEditor">
					<patch:attribute name="type">DynamicPlaceholders.Pipelines.ExecutePageEditorAction.DynamicReplaceRendering, DynamicPlaceholders</patch:attribute>
				</processor>
			</executePageEditorAction>
		</pipelines>
	</sitecore>
</configuration>