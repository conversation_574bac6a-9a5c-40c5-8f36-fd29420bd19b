﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose: This include file defines the components that are required for the intelligence features of content testing.
  Intelligence features include clustering & segmentation and test duration forecasting.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <algorithms>
      <!-- The algorithm used when performing clustering across multiple sessions. -->
      <clustering type="Sitecore.ContentTesting.Intelligence.Algorithms.Kmeans, Sitecore.ContentTesting.Intelligence"/>
    </algorithms>
    
    <pipelines>
      <!-- GET TESTING CLUSTERS
           Performs segmention over sessions for a test
      -->
      <getTestingClusters>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.GetTestingClusters.GetData, Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.GetTestingClusters.NormalizeData, Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.GetTestingClusters.Clustering, Sitecore.ContentTesting.Intelligence">
          <ClusteringAlgorithm ref="algorithms/clustering" />
        </processor>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.GetTestingClusters.SaveClusters,Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.GetTestingClusters.GetWinningCombinationsForCluster, Sitecore.ContentTesting.Intelligence"/>
      </getTestingClusters>
      
      <!-- CALCULATE STATISTICAL RELEVANCY
           Determines whether a content test has been exposed to enough visitors to successfully determine the winning variant.
      -->
      <calculateStatisticalRelevancy>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.CalculateStatisticalRelevancy.GetData, Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.CalculateStatisticalRelevancy.CalculateChiSquare, Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.CalculateStatisticalRelevancy.SaveData, Sitecore.ContentTesting.Intelligence"/>
      </calculateStatisticalRelevancy>

      <!-- TEST DURATION FORECAST
           Forecasts the number of sessions required for a successful content test and the number of days required to reach statistical relevancy.
      -->
      <forecastTestDuration>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.ForecastTestDuration.SessionCount, Sitecore.ContentTesting.Intelligence"/>
        <processor type="Sitecore.ContentTesting.Intelligence.Pipelines.ForecastTestDuration.Duration, Sitecore.ContentTesting.Intelligence"/>
      </forecastTestDuration>
            
    </pipelines>
  </sitecore>
</configuration>
