﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    
    <settings>
      <!--  ANALYTICS AUTOMATION ENGAGEMENT PLAN CACHE EXPIRATION 
            When Sitecore counts automation states in a plan the aggregated results are cached for better performance.
            The number of milliseconds before the cache expires.
            Default: 2000
      -->
      <setting name="Analytics.Automation.EngagementPlanCacheExpiration" value="2000" />

      <!--  ANALYTICS EMAIL FROM ADDRESS
            Specifies the From address in emails that contain reports.
            Default: <none>
      -->
      <setting name="Analytics.EMailFromAddress" value="" />

      <!--  ANALYTICS ENABLED
            Determines whether analytics is enabled or not.
            Default: true
      -->
      <setting name="Analytics.Enabled" value="false" />

      <!--  ANALYTICS FAIL ON DATABASE ERRORS
            Note: This is a diagnostics setting that should not be enabled on a live system.
            If set to true, web pages crash and show exception details in case
            an operation cannot access the database.
            If set to false, exceptions are suppressed and logged, and visit data
            is queued until the database is back up. See <submitQueue> section.
            Default: false
      -->
      <setting name="Analytics.FailOnDatabaseErrors" value="false" />

      <!--  ANALYTICS GEOIPS CACHESIZE
            Determines the number of cached items in the GeoIp Lookup Manager.
            Default: 1000
      -->
      <setting name="Analytics.GeoIps.CacheSize" value="1000" />

      <!--  ANALYTICS LOG LEVEL
            Determines which level of log errors are also logged in Analytics.
            Possible values are: None, Debug, Information, Warning, Error, and Fatal.
            Default: Error
      -->
      <setting name="Analytics.LogLevel" value="Error" />

      <!--  ANALYTICS - MAX ACCEPTED CLOCK DEVIATION
            Defines the largest acceptable deviation between the current server’s system time and the system time
            of other Sitecore servers in the same environment.
            The system uses this setting to compensate for any system time deviation between servers.
            Default: 0.00:00:10
      -->
      <setting name="Analytics.MaxAcceptedClockDeviation" value="0.00:00:10" />


      <!--  ANALYTICS PERFORM LOOKUP
            Determines if this server performs the lookups (DNS and URLs). Only one server should
            be responsible for performning the lookup.
            Default: true
      -->
      <setting name="Analytics.PerformLookup" value="false" />

      <!--  ANALYTICS – REPORTS – DISPLAY DATES IN UTC
            Specifies whether dates and times are displayed in UTC or the local server time zone in the Engagement Analytics reports and in the Executive Dashboard.
            If true, dates and times are displayed in UTC in reports.
            Default: false
      -->
      <setting name="Analytics.Reports.DisplayDatesInUtc" value="false" />

      <!--  MAXMIND FORMAT
            Format of the return value from MaxMind
            Default: "Country,Region,City,PostalCode,Latitude,Longitude,MetroCode,AreaCode,Isp,Owner,Error"
      -->
      <setting name="MaxMind.Format" value="Country,Region,City,PostalCode,Latitude,Longitude,MetroCode,AreaCode,Isp,Owner,Error" />
      
      <!--  MAXMIND SECURITY TOKEN
            Security token from MaxMind.
            Default: "00000000"
      -->
      <setting name="MaxMind.SecurityToken" value="00000000" />

      <!--  MAXMIND URL
            The URL of the MaxMind web service.
            Default: http://sitecore1.maxmind.com/app/sc?i={0}&amp;l={1}
      -->
      <setting name="MaxMind.Url" value="http://sitecore1.maxmind.com/app/sc?i={0}&amp;l={1}" />
    </settings>

    <interactionRegistry type="Sitecore.Analytics.Tracking.External.InteractionRegistry, Sitecore.Analytics" singleInstance="true" />

    <automation>
      <!-- Provides information about the structure and settings on the automation plans and states to the EAS functions. -->
      <metadataProvider type="Sitecore.Analytics.Automation.Data.Items.SitecoreItemsAutomationMetadataProvider,Sitecore.Analytics.Automation" singleInstance="true" />

      <mergeStrategy type="Sitecore.Analytics.Automation.Data.DefaultAutomationStatesMergeStrategy, Sitecore.Analytics.Automation" />
    </automation>

    <automationManager defaultProvider="default">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.Analytics.Automation.Data.MongoDbAutomationProvider, Sitecore.Analytics.Automation.MongoDB">
          <param desc="connectionStringName">analytics</param>
        </add>
      </providers>
    </automationManager>

    <commands>
      <command name="analytics:authoringfeedback" type="Sitecore.Shell.Applications.Analytics.AuthoringFeedback.AuthoringFeedback,Sitecore.Client"/>
      <command name="analytics:emailreport" type="Sitecore.Shell.Applications.Analytics.ReportRunner.EmailReport,Sitecore.Client"/>
      <command name="analytics:opengoals" type="Sitecore.Shell.Applications.Analytics.TrackingField.OpenGoals,Sitecore.Client"/>
      <command name="analytics:openprofiles" type="Sitecore.Shell.Applications.Analytics.TrackingField.OpenProfiles,Sitecore.Client"/>
      <command name="analytics:opentrackingfield" type="Sitecore.Shell.Applications.Analytics.TrackingField.OpenTrackingField,Sitecore.Client"/>
      <command name="analytics:viewtrackingdetails" type="Sitecore.Shell.Applications.Analytics.TrackingField.ViewTrackingDetails,Sitecore.Client"/>
    </commands>

    <contactRepository type="Sitecore.Analytics.Data.ContactRepository, Sitecore.Analytics" singleInstance="true" />

    <dataAdapterManager defaultProvider="mongo">
      <providers>
        <clear/>
        <add name="mongo" type="Sitecore.Analytics.Data.DataAccess.MongoDb.MongoDbDataAdapterProvider, Sitecore.Analytics.MongoDb" connectionStringName="analytics"/>
      </providers>
    </dataAdapterManager>

    <events>
      <event name="item:saved">
        <handler type="Sitecore.Analytics.Data.Items.ItemEventHandler, Sitecore.Analytics" method="OnItemSaved"/>
        <!--handler type="Sitecore.Analytics.Automation.Data.Items.ItemEventHandler, Sitecore.Analytics.Automation" method="OnAutomationStateChanged"/-->
      </event>
      <event name="item:deleted">
        <!--handler type="Sitecore.Analytics.Automation.Data.Items.ItemEventHandler, Sitecore.Analytics.Automation" method="OnAutomationStateChanged"/-->
      </event>
    </events>

    <maintenanceService type="Sitecore.Analytics.Core.MaintenanceAgent, Sitecore.Analytics.Core">
      <Services hint="list:RegisterService" />
    </maintenanceService>

    <!-- Core Services Subsystem -->
    <coreServices type="Sitecore.Analytics.Core.Subsystem" singleInstance="true">
      <BackgroundServices hint="list:Add">
        <maintenance type="Sitecore.Analytics.Core.BackgroundService">
          <param desc="agentName">maintenanceService</param>
          <Interval>0.00:01:00</Interval>
          <MaxThreads>1</MaxThreads>
        </maintenance>
      </BackgroundServices>
    </coreServices>

    <hooks>
      <hook type="Sitecore.Analytics.ConfigLoader, Sitecore.Analytics" />
      <hook type="Sitecore.Analytics.BackgroundServiceLoader, Sitecore.Analytics" />
      <hook type="Sitecore.Analytics.Core.SubsystemLoader, Sitecore.Analytics.Core">
        <Name>Core Services</Name>
        <Subsystem ref="coreServices" />
      </hook>
    </hooks>

    <lookupManager defaultProvider="default">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.Analytics.Lookups.MaxMindProvider,Sitecore.Analytics"/>
      </providers>
    </lookupManager>

    <multiVariateTesting>
      <multiPageTesting type="Sitecore.Analytics.Testing.TestingUtils.MultiPageTesting, Sitecore.Analytics" />
      <multiComponentTesting type="Sitecore.Analytics.Testing.TestingUtils.MultiComponentTesting, Sitecore.Analytics" />
    </multiVariateTesting>

    <pipelines>

      <automation>
        <processor type="Sitecore.Analytics.Pipelines.Automation.GetAutomationStateItem,Sitecore.Analytics.Automation" />
        <processor type="Sitecore.Analytics.Pipelines.Automation.LastAccessedDateTime,Sitecore.Analytics.Automation" />
        <processor type="Sitecore.Analytics.Automation.Pipelines.Automation.ProcessCommands,Sitecore.Analytics.Automation" />
        <processor type="Sitecore.Analytics.Pipelines.Automation.SetNextWakeUpTime,Sitecore.Analytics.Automation" />
      </automation>

      <classificationStrategy>
        <processor type="Sitecore.Analytics.Pipelines.ClassificationStrategy.OverrideClassification, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ClassificationStrategy.LocationsClassifications, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ClassificationStrategy.UserAgentsClassifications, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ClassificationStrategy.GeoIpClassification, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.ClassificationStrategy.ContactClassifications, Sitecore.Analytics"/>
      </classificationStrategy>

      <createItemChangeTest>
        <processor type="Sitecore.Analytics.Pipelines.CreateItemChangeTest.PageVersionTest,Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CreateItemChangeTest.MvTest,Sitecore.Analytics" />
      </createItemChangeTest>

      <deployDefinition>
        <processor type="Sitecore.Analytics.Pipelines.DeployDefinition.SavePageEvent,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.DeployDefinition.SaveCampaign,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.DeployDefinition.SaveTrafficType,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.DeployDefinition.SaveVisitorClassification,Sitecore.Analytics" />
      </deployDefinition>

      <findVisitorEmailAddress>
        <processor type="Sitecore.Analytics.Pipelines.FindVisitorEmailAddress.FindContactEmailAddress, Sitecore.Analytics" method="FindEmailInUserName" patch:before="processor[@type='Sitecore.Pipelines.GetVisitorEmailAddress.FindContactEmailAddress, Sitecore.Kernel']"/>
        <processor type="Sitecore.Analytics.Pipelines.FindVisitorEmailAddress.FindContactEmailAddress, Sitecore.Analytics" method="FindEmailInTags" patch:after="processor[@type='Sitecore.Pipelines.GetVisitorEmailAddress.FindContactEmailAddress, Sitecore.Kernel']"/>
      </findVisitorEmailAddress>

      <getChromeData>
        <processor type="Sitecore.Analytics.Pipelines.GetChromeData.GetRenderingTestVariations, Sitecore.Analytics" patch:after="processor[@type='Sitecore.Pipelines.GetChromeData.GetRenderingChromeData, Sitecore.Kernel']" />
      </getChromeData>

      <getContentEditorWarnings>
        <processor type="Sitecore.Analytics.Pipelines.GetContentEditorWarnings.MissingMarketingProfiles, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.GetContentEditorWarnings.IsEngagementPlanDeployed, Sitecore.Analytics"  patch:after="processor[@type='Sitecore.Pipelines.GetContentEditorWarnings.HasNoFields, Sitecore.Kernel']"/>
      </getContentEditorWarnings>

      <httpRequestBegin>
        <processor type="Sitecore.Analytics.Pipelines.HttpRequest.StartDiagnostics,Sitecore.Analytics" patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.StartMeasurements, Sitecore.Kernel']" />
      </httpRequestBegin>

      <initializeExternalSession>
        <processor type="Sitecore.Analytics.Pipelines.InitializeExternalSession.CreateStandardSession, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.InitializeExternalSession.EnsureDevice, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.InitializeExternalSession.EnsureContact, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.InitializeExternalSession.EnsureInteraction, Sitecore.Analytics" />
      </initializeExternalSession>

      <insertRenderings>
        <processor type="Sitecore.Analytics.Pipelines.InsertRenderings.Testing,Sitecore.Analytics" patch:before="processor[@type='Sitecore.Pipelines.InsertRenderings.Processors.EvaluateConditions, Sitecore.Kernel']"/>
        <processor type="Sitecore.Analytics.Pipelines.InsertRenderings.PageEditorTesting,Sitecore.Analytics" patch:before="processor[@type='Sitecore.Pipelines.InsertRenderings.Processors.EvaluateConditions, Sitecore.Kernel']"/>
        <processor type="Sitecore.Analytics.Pipelines.InsertRenderings.Personalization,Sitecore.Analytics" patch:after="processor[@type='Sitecore.Pipelines.InsertRenderings.Processors.EvaluateConditions, Sitecore.Kernel']"/>
      </insertRenderings>

      <mergeContacts>
        <processor type="Sitecore.Analytics.Pipelines.MergeContacts.MergeTags, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.MergeContacts.MergeContactCounters, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Automation.Pipelines.MergeContacts.MergeAutomationStates,Sitecore.Analytics.Automation">
          <SessionContextManager ref="tracking/sessionContextManager"></SessionContextManager>
        </processor>
        <processor type="Sitecore.Analytics.Pipelines.MergeContacts.MergeContactAttributes, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.MergeContacts.MergeContactFacets, Sitecore.Analytics"/>
      </mergeContacts>

      <parseReferrer>
        <processor type="Sitecore.Analytics.Pipelines.ParseReferrer.ParseGenericSearchEngine,Sitecore.Analytics">
          <engines hint="raw:AddHostParameterName">
            <engine hostname="www.google" parametername="q"/>
            <engine hostname="search.yahoo" parametername="p"/>
            <engine hostname="www.bing" parametername="q"/>
            <engine hostname="search.lycos" parametername="query"/>
            <engine hostname="www.baidu" parametername="wd"/>
          </engines>
        </processor>
      </parseReferrer>

      <processEmailMessage>
        <processor type="Sitecore.Analytics.Automation.Pipelines.PrepareEmail.ProcessEmailMessage, Sitecore.Analytics.Automation" method="AddHostToItemLink"/>
        <processor type="Sitecore.Analytics.Automation.Pipelines.PrepareEmail.ProcessEmailMessage, Sitecore.Analytics.Automation" method="AddHostToMediaItem"/>
        <processor type="Sitecore.Analytics.Automation.Pipelines.PrepareEmail.ProcessEmailMessage, Sitecore.Analytics.Automation" method="SendEmail"/>
      </processEmailMessage>

      <processItem>
        <processor type="Sitecore.Analytics.Pipelines.ProcessItem.CollectParameters,Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.ProcessItem.TriggerCampaigns,Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.ProcessItem.RegisterPageEvents,Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.ProcessItem.ProcessProfiles, Sitecore.Analytics" />
      </processItem>

      <beforeRecordTouchPoint>
        <processor type="Sitecore.Analytics.Pipelines.BeforeRecordTouchPoint.StartTracking, Sitecore.Analytics" />
      </beforeRecordTouchPoint>
      
      <recordTouchPoint>
        <processor type="Sitecore.Analytics.Pipelines.RecordTouchPoint.CreatePage, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.RecordTouchPoint.RunSessionBeginRules, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.RecordTouchPoint.TriggerCampaigns, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.RecordTouchPoint.RegisterEvents, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.RecordTouchPoint.ProcessItem, Sitecore.Analytics" />
      </recordTouchPoint>

      <afterRecordTouchPoint>
        <processor type="Sitecore.Analytics.Pipelines.AfterRecordTouchPoint.EndTracking, Sitecore.Analytics" />
      </afterRecordTouchPoint>

      <group groupName="analytics.bulk.contact">
        <pipelines>

          <updateFields>
          </updateFields>

          <beforePersist>
          </beforePersist>

          <afterPersist>
          </afterPersist>

        </pipelines>
      </group>

      <renderLayout>
        <!-- This processor should insert just after Sitecore.Analytics.Pipelines.HttpRequest.StartAnalytics defined in Analytics.Tracking.config -->
        <processor type="Sitecore.Analytics.Pipelines.RenderLayout.PageLevelTestItemResolver, Sitecore.Analytics" patch:before="processor[@type='Sitecore.Pipelines.RenderLayout.InsertRenderings, Sitecore.Kernel']" />
      </renderLayout>

      <sessionEnd>
        <processor type="Sitecore.Analytics.Pipelines.SessionEnd.RaiseVisitEnd,Sitecore.Analytics"/>
      </sessionEnd>

      <submitContact>
        <processor type="Sitecore.Analytics.Automation.Pipelines.SubmitContact.SaveAutomationRecords, Sitecore.Analytics.Automation" />
      </submitContact>

      <trafficTypes>
        <processor type="Sitecore.Analytics.Pipelines.TrafficTypes.Initialize,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.TrafficTypes.ReferringSite,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.TrafficTypes.SearchKeywords,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.TrafficTypes.OrganicBranded,Sitecore.Analytics"/>
      </trafficTypes>

      <triggerCampaign>
        <processor type="Sitecore.Analytics.Pipelines.TriggerCampaign.CheckPreconditions,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.TriggerCampaign.HandleTrafficType,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.TriggerCampaign.RunRules,Sitecore.Analytics"/>
        <processor type="Sitecore.Automation.MarketingAutomation.Pipelines.TriggerCampaign.RunAutomation,Sitecore.Analytics.Automation"/>
        <processor type="Sitecore.Analytics.Pipelines.TriggerCampaign.RegisterPageEvent,Sitecore.Analytics"/>
      </triggerCampaign>

      <visitEnd>
        <processor type="Sitecore.Analytics.Pipelines.VisitEnd.RunRules,Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.VisitEnd.RunAutomation,Sitecore.Analytics.Automation"/>
      </visitEnd>

      <updateClassifications>
        <processor type="Sitecore.Analytics.Pipelines.UpdateClassification.NormalizeRequest, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.UpdateClassification.UpdateCollectionData, Sitecore.Analytics">
            <ContactLockTimeout>0.00:00:03</ContactLockTimeout>
            <ContactRepository  ref="contactRepository" />
        </processor>
        <processor type="Sitecore.Analytics.Aggregation.Pipelines.UpdateClassification.UpdateReportingDimensions, Sitecore.Analytics.Aggregation"/>
        <processor type="Sitecore.Analytics.Pipelines.UpdateClassification.ScheduleUpdatingDependantClassifications, Sitecore.Analytics">
          <Worker type="Sitecore.Analytics.Pipelines.UpdateClassification.DefaultUpdateClassificationsWorker,Sitecore.Analytics" />
          <TaskManager ref="processing/taskManager" />
        </processor>
        
      </updateClassifications>

      <updateContactClassifications>
        <processor type="Sitecore.Analytics.Pipelines.UpdateContactClassification.UpdateXdbClassificationsProcessor,Sitecore.Analytics">
            <ContactLockTimeout>0.00:00:03</ContactLockTimeout>
            <ContactRepository  ref="contactRepository" />
        </processor>
        <processor type="Sitecore.Analytics.Pipelines.UpdateContactClassification.UpdateRdbClassificationProcessor,Sitecore.Analytics"/>
      </updateContactClassifications>

      <reconcileRdbClassifications>
        <processor type="Sitecore.Analytics.Aggregation.Pipelines.ReconcileRdbClassifications.UpdateLocationDimension, Sitecore.Analytics.Aggregation"/>
        <processor type="Sitecore.Analytics.Aggregation.Pipelines.ReconcileRdbClassifications.UpdateContactDimension, Sitecore.Analytics.Aggregation"/>
        <processor type="Sitecore.Analytics.Aggregation.Pipelines.ReconcileRdbClassifications.SaveDimensions, Sitecore.Analytics.Aggregation">
          <ReportingTargets hint="list:AddReportingTarget">
            <primary ref="aggregation/reportingStorageProviders/primary" />
          </ReportingTargets>
        </processor>
      </reconcileRdbClassifications>
      
    </pipelines>

    <scheduling>
      <agent type="Sitecore.Analytics.Tasks.EmailReportsTask, Sitecore.Analytics" method="Run" interval="1:00:00">
        <DatabaseName>master</DatabaseName>
      </agent>
    </scheduling>

    <tracking>
      <!--This configuration node is obsolete and will be removed in a future version of Sitecore. Use “contactRepository” node to get access to Contact repository  -->
      <contactRepository ref="contactRepository" />
      
      <nullContactRepository type="Sitecore.Analytics.Data.ContactRepository, Sitecore.Analytics" singleInstance="true">
        <param desc="dataAdapterProvider" type="Sitecore.Analytics.DataAccess.Null.NullDataAdapterProvider"/>
      </nullContactRepository>
    </tracking>

  </sitecore>
</configuration>
