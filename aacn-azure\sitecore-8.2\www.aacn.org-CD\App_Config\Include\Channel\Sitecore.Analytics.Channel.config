﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the OmniChannel tracking and management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <!-- Determines the initial channel for the interaction. -->
      <determineInteractionChannel>
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.DetermineInteractionChannel.DefaultChannel, Sitecore.Analytics.OmniChannel">
          <param decs="channelId">{B418E4F2-1013-4B42-A053-B6D4DCA988BF}</param>
        </processor>        
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.DetermineInteractionChannel.ReferringSite, Sitecore.Analytics.OmniChannel">
          <param desc="channelId">{44DD9FF5-44B2-4C59-8DF8-849E400F4B6B}</param>
        </processor>
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.DetermineInteractionChannel.SearchKeywords, Sitecore.Analytics.OmniChannel">
          <param desc="channelId">{B979A670-5AAF-4E63-94AC-C3C3E5BFBE84}</param>
        </processor>
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.DetermineInteractionChannel.OrganicBranded, Sitecore.Analytics.OmniChannel">
          <param desc="channelId">{FB8FA660-0A07-4EE9-A9F4-930FC5D10AEC}</param>
        </processor>      
      </determineInteractionChannel>

      <createVisit>
        <!-- It is important that this processor is the last in the createVisit pipeline. -->
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.CreateVisit.SetChannel, Sitecore.Analytics.OmniChannel" />
      </createVisit>

      <triggerCampaign>
        <processor type="Sitecore.Analytics.OmniChannel.Pipelines.TriggerCampaign.SetChannel, Sitecore.Analytics.OmniChannel" patch:before="processor[@type='Sitecore.Analytics.Pipelines.TriggerCampaign.HandleTrafficType, Sitecore.Analytics']" />
      </triggerCampaign>

    </pipelines>
  </sitecore>
</configuration>