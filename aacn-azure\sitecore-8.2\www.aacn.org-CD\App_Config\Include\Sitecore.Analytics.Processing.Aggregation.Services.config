﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file launches the background services that aggregate data from the collection database before it is stored in the
reporting database for use by Sitecore reporting applications.

If the current server does not need to perform aggregation tasks, you can rename this config file so that it has a ".disabled" extension.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <aggregation>

      <!-- Configure the aggregator agent: -->
      <aggregator type="Sitecore.Analytics.Aggregation.InteractionBatchAggregationAgent, Sitecore.Analytics.Aggregation">
        <Context ref="aggregation/aggregationContexts/interaction/live" />
        <DateTimeStrategy ref="aggregation/dateTimePrecisionStrategy" />
        <Aggregator type="Sitecore.Analytics.Aggregation.InteractionBatchAggregator, Sitecore.Analytics.Aggregation" singleInstance="true">
          <MultiplexingTimeout>0.00:00:01</MultiplexingTimeout>
        </Aggregator>
        <MaximumBatchSize>64</MaximumBatchSize>
      </aggregator>

      <!-- Automation Aggregation Subsystem: -->
      <automationAggregationSubsystem type="Sitecore.Analytics.Core.Subsystem" singleInstance="true">
        <BackgroundServices hint="list:Add">
          <aggregator type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/automationAggregator</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>3</MaxThreads>
          </aggregator>
          <rangeManager type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/automationRangeManager</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </rangeManager>
          <cleanup type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/automationCleanupService</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </cleanup>
        </BackgroundServices>
      </automationAggregationSubsystem>

      <!-- Configure the automation live aggregator agent: -->
      <automationAggregator type="Sitecore.Analytics.Automation.Aggregation.Data.Processing.AggregatorAgent, Sitecore.Analytics.Automation.Aggregation">
        <Dispatcher ref="aggregation/dispatchers/automationWorkDispatcher"/>
      </automationAggregator>

      <!-- Configure the automation live aggregator agent: -->
      <automationCleanupService type="Sitecore.Analytics.Automation.Aggregation.Data.Processing.CleanupAgent, Sitecore.Analytics.Automation.Aggregation">
        <Context ref="aggregation/aggregationContexts/automation/live"/>
      </automationCleanupService>

      <!-- Configure the automation live aggregator agent: -->
      <automationRangeManager type="Sitecore.Analytics.Automation.Aggregation.Data.Processing.RangeManagerAgent, Sitecore.Analytics.Automation.Aggregation">
        <RangeLength>0.00:00:30</RangeLength>
      </automationRangeManager>

      <!-- Configure the processing pool clean agent: -->
      <!-- WARNING: This agent is obsolete and will be removed in a future version -->
      <cleanup type="Sitecore.Analytics.Aggregation.Data.Processing.AggregationProcessingPoolCleanupAgent, Sitecore.Analytics.Aggregation">
        <Dispatcher ref="aggregation/dispatchers/interactionWorkDispatcher"/>
      </cleanup>

      <!-- Agent Work Dispachers: -->
      <dispatchers>
        <interactionWorkDispatcher type="Sitecore.Analytics.Aggregation.InteractionWorkDispatcher">
          <Contexts hint="list:RegisterContext">
            <live ref="aggregation/aggregationContexts/interaction/live"/>
            <history ref="aggregation/aggregationContexts/interaction/history"/>
          </Contexts>
        </interactionWorkDispatcher>
        <automationWorkDispatcher type="Sitecore.Analytics.Automation.Aggregation.AutomationWorkDispatcher, Sitecore.Analytics.Automation.Aggregation">
          <param desc="leaseDuration">0.00:00:15</param>
          <param desc="minimumRangeSize">10</param>
          <Context ref="aggregation/aggregationContexts/automation/live"/>
        </automationWorkDispatcher>
        <contactWorkDispatcher type="Sitecore.Analytics.Aggregation.Data.Contact.ContactWorkDispatcher, Sitecore.Analytics.Aggregation">
          <Context ref="aggregation/aggregationContexts/contact/live"/>
        </contactWorkDispatcher>
      </dispatchers>

      <!-- Configure the HistoryCompletionCheck agent: -->
      <historyCompletionCheck type="Sitecore.Analytics.Aggregation.Data.Processing.HistoryCompletionCheckAgent, Sitecore.Analytics.Aggregation">
        <HistoryTaskManager ref="aggregation/historyTaskManager" />
      </historyCompletionCheck>

      <!-- Configure the historyWorker agent: -->
      <historyWorker type="Sitecore.Analytics.Aggregation.Data.Processing.InteractionBatchHistoryWorker, Sitecore.Analytics.Aggregation">
        <HistoryTaskManager ref="aggregation/historyTaskManager" />
        <DateTimePrecisionStrategy ref="aggregation/dateTimePrecisionStrategy"/>
        <CollectionData ref="aggregation/collectionData" />
        <AggregationContext ref="aggregation/aggregationContexts/interaction/history" />
        <Aggregator type="Sitecore.Analytics.Aggregation.InteractionBatchAggregator, Sitecore.Analytics.Aggregation" singleInstance="true">
          <MultiplexingTimeout>0.00:00:01</MultiplexingTimeout>
        </Aggregator>
        <MaximumBatchSize>128</MaximumBatchSize>
      </historyWorker>

      <!-- Aggregation Module: -->
      <module type="Sitecore.Analytics.Aggregation.AggregationModule" singleInstance="true">
        <BackgroundServices hint="list:Add">
          <aggregator type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/aggregator</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </aggregator>
          <contactProcessing type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/contactProcessing</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </contactProcessing>
          <cleanup type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/cleanup</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </cleanup>
          <recovery type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/recovery</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </recovery>
          <rebuild type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/rebuildAgent</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </rebuild>
          <history type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/historyWorker</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </history>
          <historyCompletionCheck type="Sitecore.Analytics.Core.BackgroundService">
            <param desc="agentName">aggregation/historyCompletionCheck</param>
            <Interval>0.00:00:15</Interval>
            <MaxThreads>1</MaxThreads>
          </historyCompletionCheck>
        </BackgroundServices>
      </module>

      <rebuildAgent type="Sitecore.Analytics.Aggregation.Data.Processing.RebuildAgent, Sitecore.Analytics.Aggregation">
        <ReportingStorageManager ref="aggregation/reportingStorageManager" />
      </rebuildAgent>

      <contactProcessing type="Sitecore.Analytics.Aggregation.Data.Contact.ContactProcessingAgent, Sitecore.Analytics.Aggregation">
        <Dispatcher ref="aggregation/dispatchers/contactWorkDispatcher"/>
      </contactProcessing>

      <!-- Configure the processing pool recovery agent: -->
      <!-- WARNING: This agent is obsolete and will be removed in a future version -->
      <recovery type="Sitecore.Analytics.Aggregation.Data.Processing.AggregationProcessingPoolRecoveryAgent, Sitecore.Analytics.Aggregation">
        <Dispatcher ref="aggregation/dispatchers/interactionWorkDispatcher"/>
        <RecoveryTime>60</RecoveryTime>
      </recovery>

    </aggregation>

    <maintenanceService type="Sitecore.Analytics.Core.MaintenanceAgent, Sitecore.Analytics.Core">
      <Services>
        <rdbCleanUpPrimary ref="aggregation/reportingStorageProviders/primary"/>
      </Services>
    </maintenanceService>

    <hooks>
      <hook type="Sitecore.Analytics.Aggregation.AggregationLoader, Sitecore.Analytics.Aggregation"/>
      <hook type="Sitecore.Analytics.Automation.Aggregation.SubsystemLoader, Sitecore.Analytics.Automation.Aggregation"/>
    </hooks>

  </sitecore>
</configuration>
