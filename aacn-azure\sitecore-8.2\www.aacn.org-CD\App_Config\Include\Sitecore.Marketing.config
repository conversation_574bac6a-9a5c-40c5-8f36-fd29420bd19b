﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures Marketing platfrom definition management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <!-- hooks into the analytics deployDefinition pipeline -->
      <deployDefinition>
        <processor type="Sitecore.Marketing.Definitions.Deployment.Processors.Deploy, Sitecore.Marketing" patch:before="processor[@type='Sitecore.Analytics.Pipelines.DeployDefinition.SavePageEvent,Sitecore.Analytics']"/>
      </deployDefinition>
    </pipelines>
    <marketingDefinitions>

      <deploymentManager type="Sitecore.Marketing.Definitions.Deployment.DeploymentManager, Sitecore.Marketing" singleInstance="true">
        <!-- 
        specifies which of the repositories for the definition is used to deploy from this server i.e. rdb, remote. 
        When using remote repositories, configure marketingDefinitions/httpTransportFactory -->
        <param desc="targetRepository">rdb</param>
      </deploymentManager>

      <campaign definitionInterface="Sitecore.Marketing.Definitions.Campaigns.ICampaignActivityDefinition, Sitecore.Marketing">
        <definitionManager type="Sitecore.Marketing.Definitions.Campaigns.CampaignDefinitionManager, Sitecore.Marketing" singleInstance="true">
          <param desc="repository" ref="marketingDefinitions/campaign/repositories/item" />
          <param desc="classificationResolver" type="Sitecore.Marketing.Definitions.DefaultClassificationResolver, Sitecore.Marketing" />

          <config>
            <!--Maps custom classification fields to their correlated custom taxonomy-->
            <customTaxonomyFieldMappings>
              <!--Campaign Facet 1 - 7.  Ordered Ascending-->
              <mapping fieldId="{297AB1FA-9A3B-483D-B8F7-41E7BBE549FB}" taxonomyRootId="{9CEDAA9D-9347-428C-B412-2556FF716EE4}"/>
              <mapping fieldId="{13F24114-C29B-4ACB-A39B-445CCE2ED60C}" taxonomyRootId="{8639CCD1-D37E-473F-AB08-E9C94EE38B18}"/>
              <mapping fieldId="{336BA862-E974-4CD6-BE4D-4BFDD78994E1}" taxonomyRootId="{C3A9E862-81CB-43A2-BE18-441C38B54D53}"/>
              <mapping fieldId="{82ABE216-2119-4803-BC9B-BB69571A973B}" taxonomyRootId="{33BBBF1A-F472-4494-9573-C41C81565F0B}"/>
              <mapping fieldId="{9F517EEC-EE1A-4D36-8808-A76CF2FA46E7}" taxonomyRootId="{E5447EDF-FEDA-4878-8CE9-DF2DE42BC8D1}"/>
              <mapping fieldId="{60B84CA2-BB8E-4C8C-95F6-495057DAD4D4}" taxonomyRootId="{2CD22095-5D23-472E-902B-F5EF88257B31}"/>
              <mapping fieldId="{C713F777-C1DC-45E5-A86F-F504B864E4AB}" taxonomyRootId="{7B0B605C-2A11-427D-A0A3-3B69EBF1B3DC}"/>
            </customTaxonomyFieldMappings>

            <!--Maps known (those well identified like campaign group or channel) classification fields to their correlated taxonomy-->
            <knownTaxonomyFieldMappings>
              <!--Channel-->
              <mapping fieldId="{4C1BF83B-06E2-4B07-AD16-0E65D95E356E}" taxonomyRootId="{F575D5E6-74DE-49B6-A866-E2256D213D83}"/>
              <!--Campaign Group-->
              <mapping fieldId="{FFAEA943-DF50-4F11-94ED-4690418A7D96}" taxonomyRootId="{AB2F413C-4854-48EB-B7EB-6FD2E8142B19}"/>
              <!--Marketing Asset-->
              <mapping fieldId="{3C0EEC17-C74D-4921-9577-D41AEA11B6DB}" taxonomyRootId="{733CECC5-1A6D-4C81-8267-D52A80349EE4}"/>
            </knownTaxonomyFieldMappings>
          </config>


        </definitionManager>

        <repositories>
          <item type="Sitecore.Marketing.Definitions.Campaigns.Data.ItemDb.CampaignDefinitionItemRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="databaseName">master</param>
          </item>
          <rdb type="Sitecore.Marketing.Definitions.Campaigns.Data.Rdb.RdbCampaignDefinitionRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="connectionStringName">reporting</param>
            <param desc="cache" type="Sitecore.Marketing.Definitions.Repository.RdbCache.DefinitionCache`1[[Sitecore.Marketing.Definitions.Campaigns.Data.CampaignActivityDefinitionRecord, Sitecore.Marketing]], Sitecore.Marketing">
              <param desc="name">marketing.rdb.campaign</param>
              <param desc="maxSize">20MB</param>
            </param>
          </rdb>
        </repositories>
      </campaign>
      <asset definitionInterface="Sitecore.Marketing.Definitions.MarketingAssets.IMarketingAssetDefinition, Sitecore.Marketing">
        <definitionManager type="Sitecore.Marketing.Definitions.MarketingAssets.MarketingAssetDefinitionManager, Sitecore.Marketing" singleInstance="true">
          <param desc="repository" ref="marketingDefinitions/asset/repositories/notConfigured" />
          <param desc="classificationResolver" type="Sitecore.Marketing.Definitions.DefaultClassificationResolver, Sitecore.Marketing" />
          <config>
            <!--Maps custom classification fields to their correlated custom taxonomy-->
            <customTaxonomyFieldMappings>
              <!-- Asset 1 -3 -->
              <mapping fieldId="{C390BA94-0393-45D5-A299-0A5A8B52660F}" taxonomyRootId="{1F1D1B39-DB89-47CA-B7D9-20E51E73EEBC}" />
              <mapping fieldId="{5F70E8F4-B06E-4256-AC4F-091CF9341F95}" taxonomyRootId="{45000106-8DF4-4145-9B33-ADB09F6DD365}" />
              <mapping fieldId="{D294697A-B019-452A-8A73-AD520679318F}" taxonomyRootId="{0DA781CA-A671-4323-92D9-6126578DC94A}" />
            </customTaxonomyFieldMappings>
            <!--Maps known (those well identified like marketing asset) classification fields to their correlated taxonomy-->
            <knownTaxonomyFieldMappings>
              <!-- Marketing Asset -->
              <mapping fieldId="{70829C03-7349-47FF-8734-295F33F15AA3}" taxonomyRootId="{733CECC5-1A6D-4C81-8267-D52A80349EE4}" />
            </knownTaxonomyFieldMappings>
          </config>
        </definitionManager>

        <repositories>
          <notConfigured type="Sitecore.Marketing.Definitions.Repository.NotConfiguredRepository, Sitecore.Marketing" />
        </repositories>
      </asset>
      <goal definitionInterface="Sitecore.Marketing.Definitions.Goals.IGoalDefinition, Sitecore.Marketing">
        <definitionManager type="Sitecore.Marketing.Definitions.Goals.GoalDefinitionManager, Sitecore.Marketing" singleInstance="true">
          <param desc="repository" ref="marketingDefinitions/goal/repositories/item" />
          <param desc="classificationResolver" type="Sitecore.Marketing.Definitions.DefaultClassificationResolver, Sitecore.Marketing" />
          <config>
            <!--Maps custom classification fields to their correlated custom taxonomy-->
            <customTaxonomyFieldMappings>
              <!--Goal Facet 1 - 3.  Ordered Ascending-->
              <mapping fieldId="{E7B5C821-A54C-4264-9C06-C6EBDBFCD6B8}" taxonomyRootId="{13D17E8A-FED4-4799-8DEF-05E9691A3E15}"/>
              <mapping fieldId="{B3D6C41E-E37B-48AB-B8EC-13DCA18CE729}" taxonomyRootId="{7DE5CC49-D985-49E1-AC61-663A1411AE42}"/>
              <mapping fieldId="{781346DF-7E91-42F1-BA62-A63572520850}" taxonomyRootId="{D7D814A0-294D-444C-BED0-EEAEA7CD5A27}"/>
            </customTaxonomyFieldMappings>

            <!--Maps known (those well identified like marketing asset) classification fields to their correlated taxonomy-->
            <knownTaxonomyFieldMappings>
              <!--Marketing Asset-->
              <mapping fieldId="{8B553339-088A-4CFA-AB21-B8D0B8C0B77D}" taxonomyRootId="{733CECC5-1A6D-4C81-8267-D52A80349EE4}"/>
            </knownTaxonomyFieldMappings>
          </config>
        </definitionManager>

        <repositories>
          <item type="Sitecore.Marketing.Definitions.Goals.Data.ItemDb.GoalDefinitionItemRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="databaseName">master</param>
          </item>
          <rdb type="Sitecore.Marketing.Definitions.Goals.Data.Rdb.GoalDefinitionRdbRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="connectionStringName">reporting</param>
            <param desc="cache" type="Sitecore.Marketing.Definitions.Repository.RdbCache.DefinitionCache`1[[Sitecore.Marketing.Definitions.Goals.Data.GoalDefinitionRecord, Sitecore.Marketing]], Sitecore.Marketing">
              <param desc="name">marketing.rdb.goal</param>
              <param desc="maxSize">20MB</param>
            </param>
          </rdb>
        </repositories>
      </goal>
      <outcome definitionInterface="Sitecore.Marketing.Definitions.Outcomes.Model.IOutcomeDefinition, Sitecore.Marketing">
        <definitionManager type="Sitecore.Marketing.Definitions.Outcomes.OutcomeDefinitionManager, Sitecore.Marketing" singleInstance="true">
          <param desc="repository" ref="marketingDefinitions/outcome/repositories/item" />
          <param desc="classificationResolver" type="Sitecore.Marketing.Definitions.DefaultClassificationResolver, Sitecore.Marketing" />
        </definitionManager>

        <repositories>
          <item type="Sitecore.Marketing.Definitions.Outcomes.Data.ItemDb.ItemOutcomeDefinitionRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="databaseName">master</param>
          </item>

          <rdb type="Sitecore.Marketing.Definitions.Outcomes.Data.Rdb.RdbOutcomeDefinitionRepository, Sitecore.Marketing" singleInstance="true">
            <param desc="connectionStringName">reporting</param>
            <param desc="cache" type="Sitecore.Marketing.Definitions.Repository.RdbCache.DefinitionCache`1[[Sitecore.Marketing.Definitions.Outcomes.Data.OutcomeDefinitionRecord,Sitecore.Marketing]], Sitecore.Marketing">
              <param desc="name">marketing.rdb.outcome</param>
              <param desc="maxSize">20MB</param>
            </param>
            <param desc="typeCache" type="Sitecore.Marketing.Definitions.Repository.RdbCache.Cache`2[[Sitecore.Data.ID, Sitecore.Kernel],[Sitecore.Marketing.Definitions.Outcomes.Model.IOutcomeDefinitionType, Sitecore.Marketing]], Sitecore.Marketing">
              <param desc="name">marketing.rdb.outcome.type</param>
              <param desc="maxSize">20MB</param>
            </param>
          </rdb>
        </repositories>
      </outcome>
    </marketingDefinitions>

  </sitecore>
</configuration>