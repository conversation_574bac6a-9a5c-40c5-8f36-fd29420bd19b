﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the visitor's profile information that is requested when a visitor logs in to the website 
with their Twitter credentials.

Please read the Sitecore Social Connected documentation before changing the configuration.

You can enable or disable a field to extend or reduce the visitor's profile information that is received from Twitter.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <!--  This section configures social profile synchronization. 
            To include a specific field from the social site in the user's profile, set the 'enabled' property to 'true'. 
            To remove a specific field on the social site from the user's profile, set the 'enabled' property to 'false'. -->
      <profileKeyMappings>

        <!-- Configuration of profile synchronization with Twitter. -->
        <network name="Twitter">
          <!-- 'ID' field. -->
          <field enabled="true" originalKey="Id" mainNetworkDataPropertyName="Id" sitecoreKey="tw_Id" text="Id" />

          <!-- 'Screen name' field. -->
          <field enabled="true" originalKey="ScreenName" mainNetworkDataPropertyName="UserName" sitecoreKey="tw_ScreenName" text="Screen Name" />

          <!-- 'Created date' field. -->
          <field enabled="true" originalKey="CreatedDate" sitecoreKey="tw_CreatedDate" text="Created Date" />

          <!-- 'Description' field. -->
          <field enabled="true" originalKey="Description" sitecoreKey="tw_Description" text="Description" />

          <!-- 'Language' field. -->
          <field enabled="true" originalKey="Language" sitecoreKey="tw_language" text="Language" />

          <!-- 'Location' field. -->
          <field enabled="true" originalKey="Location" sitecoreKey="tw_Location" text="Location" />

          <!-- 'Name' field. -->
          <field enabled="true" originalKey="Name" sitecoreKey="tw_Name" text="Name" />

          <!-- 'Listed count' field. -->
          <field enabled="true" originalKey="ListedCount" sitecoreKey="tw_ListedCount" text="Listed Count" />

          <!-- 'Number of favorites' field. -->
          <field enabled="true" originalKey="FavouritesCount" sitecoreKey="tw_NumberOfFavorites" text="Number of Favorites" />

          <!-- 'Number of followers' field. -->
          <field enabled="true" originalKey="FollowersCount" sitecoreKey="tw_NumberOfFollowers" text="Number of Followers" />

          <!-- 'Number of friends' field. -->
          <field enabled="true" originalKey="FriendsCount" sitecoreKey="tw_NumberOfFriends" text="Number of Friends" />

          <!-- 'Number of statuses' field. -->
          <field enabled="true" originalKey="StatusesCount" sitecoreKey="tw_NumberOfStatuses" text="Number of Statuses" />

          <!-- 'Profile background image location' field. -->
          <field enabled="true" originalKey="ProfileBackgroundImageUrl" sitecoreKey="tw_ProfileBackgroundImageLocation" text="Profile Background Image Location" />

          <!-- 'Profile image location' field. -->
          <field enabled="true" originalKey="ProfileImageUrl" sitecoreKey="tw_ProfileImageLocation" text="Profile Image Location" />

          <!-- 'Profile background color string' field. -->
          <field enabled="true" originalKey="ProfileBackgroundColor" sitecoreKey="tw_ProfileBackgroundColorString" text="Profile Background Color String" />

          <!-- 'Profile sidebar border color string' field. -->
          <field enabled="true" originalKey="ProfileSidebarBorderColor" sitecoreKey="tw_ProfileSidebarBorderColorString" text="Profile Sidebar Border Color String" />

          <!-- 'Profile text color string' field. -->
          <field enabled="true" originalKey="ProfileTextColor" sitecoreKey="tw_ProfileTextColorString" text="Profile Text Color String" />

          <!-- 'Time zone' field. -->
          <field enabled="true" originalKey="TimeZone" sitecoreKey="tw_TimeZone" text="Time Zone" />

          <!-- 'Time zone offset' field. -->
          <field enabled="true" originalKey="UtcOffset" sitecoreKey="tw_TimeZoneOffset" text="Time Zone Offset" />

          <!-- 'Website' field. -->
          <field enabled="true" originalKey="Url" sitecoreKey="tw_Website" text="Website" />
        </network>
      </profileKeyMappings>
    </social>
  </sitecore>
</configuration>