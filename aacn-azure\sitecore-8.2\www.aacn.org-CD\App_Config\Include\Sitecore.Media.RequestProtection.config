﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file enables and configures the protection of media requests so that only server-generated requests are processed.

Important: To make your solution more secure, change the shared secret to a random string. In a multi-instance setup, use the same value
for Media.RequestProtection.SharedSecret on every instance. Otherwise, dynamic image scaling will not work correctly when the image URL is
generated by one instance and the request is handled by a different instance.

When enabled, Sitecore automatically signs the image URLs that are generated by the CMS and adds a hash value to the query string. When
processing an incoming media request, Sitecore skips image resizing/scaling if any of the relevant query string parameters in the image URL
have been altered or any extra resizing parameters have been appended to the URL. In such cases, Sitecore returns the original, unaltered
image. This ensures that the server only spends resources and disk space on valid image scaling requests. Requests for the original image
(without any resizing/scaling parameters) work as usual and are not restricted because these requests do not incur any overhead in terms of
CPU or disk space.

To disable this file, change its extension to ".disabled".

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <mediaLibrary>
      <requestProtection>
        <!--  IGNORE SITES
              Specifies a list of site names for which the media protection module should not sign image ULRs with a hash value. When
              Sitecore processes an incoming media request for these sites, it does not check if there is a valid hash value.
        -->
        <ignoreSites>
          <site name="shell"/>
          <site name="login"/>
          <site name="admin"/>
          <site name="service"/>
          <site name="scheduler"/>
          <site name="system"/>
          <site name="publisher"/>
        </ignoreSites>

        <!--  PROTECTED MEDIA QUERY PARAMETERS
              Specifies a list of parameters that are used to calculate hash values for media requests. 
              The list of parameters is used when the module calculates hash values and appends them to the query strings of the generated
              media URLs, and when the hash values are calculated for incoming media requests and compared to the expected hash values.
              You should add any custom image scaling parameters to this setting if these parameters affect the dimensions or file size of
              dynamically resized images.
        -->
        <protectedMediaQueryParameters>
          <parameter description="width" name="w"/>
          <parameter description="height" name="h"/>
          <parameter description="max width" name="mw"/>
          <parameter description="max height" name="mh"/>
          <parameter description="scale" name="sc"/>
          <parameter description="allow stretch" name="as"/>
          <parameter description="background color" name="bc"/>
          <parameter description="database name" name="db"/>
          <parameter description="ignore aspect ratio" name="iar"/>
          <parameter description="language code" name="la"/>
          <parameter description="thumbnail" name="thn"/>
          <parameter description="version number" name="vs"/>
          <parameter description="content database" name="sc_content"/>
          <parameter description="content language name" name="sc_lang"/>
          <parameter description="validationContext site" name="sc_site"/>
          <parameter description="grayscale filter" name="gray"/>
        </protectedMediaQueryParameters>

      </requestProtection>
    </mediaLibrary>

    <settings>
      <!--  MEDIA - REQUEST PROTECTION - ENABLED
            Specifies whether media request protection is enabled or not.
            Default value: true
      -->
      <setting name="Media.RequestProtection.Enabled" value="true" />
      <!--  MEDIA - REQUEST PROTECTION - HASH PARAMETER NAME
            The name of the query string parameter that stores the calculated hash value.
            Default value: hash
      -->
      <setting name="Media.RequestProtection.HashParameterName" value="hash" />
      <!--  Obsolete. Use "Media.RequestProtection.HashParameterName" instead.
      -->
      <setting name="Media.RequestProtection.HashParamaterName" value="" />
      <!--  MEDIA - REQUEST PROTECTION - LOGGING - ENABLED
            Specifies whether the media request protection feature will output detailed information to the Sitecore log file.
            Default value: true
      -->
      <setting name="Media.RequestProtection.Logging.Enabled" value="false" />
      <!--  MEDIA - REQUEST PROTECTION - SHARED SECRET
            Specifies the shared secret to use as a salt when generating hash values. You should change the shared secret to a random string
            and not use the default value.
            In a multi-instance setup, use the same value for Media.RequestProtection.SharedSecret on every instance. Otherwise, dynamic image
            signing will not work correctly if the image URL is generated by one instance and the request is handled by a different instance.
      -->
      <setting name="Media.RequestProtection.SharedSecret" value="D1FA5,7C4AED9.F0A32E84AA0/FAEF|D0DE9E|\8FD6AEC+8F87F-B037_66C8)34C99921(EB23BE79!AD9D5D@CC1DD#9AD2361$32102%900B723^CF98;0957FCAACN" />
    </settings>

    <pipelines>
      <renderField>
        <processor patch:before="processor[@type='Sitecore.Pipelines.RenderField.RenderWebEditing, Sitecore.Kernel']" type="Sitecore.Pipelines.RenderField.ProtectedImageLinkRenderer, Sitecore.Kernel" />
      </renderField>
    </pipelines>
  </sitecore>
</configuration>
