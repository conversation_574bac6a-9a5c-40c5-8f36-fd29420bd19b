﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the processing pools that are available to the processing subsystem when aggregating data from the
collection database. The processing pools are shared by the servers that run the aggregation subsystem and the servers that register visits
for aggregation.

In most cases, you should leave this file enabled, so that the server can register things for aggregation, even if the server does not run
any aggregation tasks itself. 

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <aggregation>
      <historyTaskManager type="Sitecore.Analytics.Aggregation.History.MongoDbHistoryTaskManager" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
        <ReportingStorage ref="aggregation/reportingStorageProviders/secondary.history" />
        <CollectionData ref="aggregation/collectionData" />
        <ProcessingPool ref="aggregationProcessing/processingPools/history" />
        <MinimumRangeSize>1000</MinimumRangeSize>
      </historyTaskManager>
    </aggregation>

    <aggregationProcessing>

      <maxItemErrorCount>10</maxItemErrorCount>

      <processingPools>
        <live type="Sitecore.Analytics.Data.MongoDb.ProcessingPool.MongoDbProcessingPool, Sitecore.Analytics.MongoDb" singleInstance="true" >
          <param desc="connectionStringName">tracking.live</param>
          <Name>live</Name>
          <Enabled>true</Enabled>
          <MaxItemErrorCount ref="aggregationProcessing/maxItemErrorCount" />
        </live>

        <history type="Sitecore.Analytics.Data.MongoDb.ProcessingPool.MongoDbProcessingPool, Sitecore.Analytics.MongoDb" singleInstance="true" >
          <param desc="connectionStringName">tracking.history</param>
          <Name>history</Name>
          <Enabled>true</Enabled>
          <MaxItemErrorCount ref="aggregationProcessing/maxItemErrorCount" />
        </history>

        <contact type="Sitecore.Analytics.Data.MongoDb.ProcessingPool.MongoDbProcessingPool, Sitecore.Analytics.MongoDb" singleInstance="true" >
          <param desc="connectionStringName">tracking.contact</param>
          <DuplicateKeyStrategy>AllowAndMerge</DuplicateKeyStrategy>
          <Name>contact</Name>
          <Enabled>true</Enabled>
          <MaxItemErrorCount ref="aggregationProcessing/maxItemErrorCount" />
        </contact>

      </processingPools>

    </aggregationProcessing>

    <pipelines>
      <initialize>
        <processor type="Sitecore.Analytics.Automation.Pipelines.Initialize.RegisterDataModelExtensions, Sitecore.Analytics.Automation.MongoDB" />
      </initialize>
    </pipelines>

  </sitecore>
</configuration>