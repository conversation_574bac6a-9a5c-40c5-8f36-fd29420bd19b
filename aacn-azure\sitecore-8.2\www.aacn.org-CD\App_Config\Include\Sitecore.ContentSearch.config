﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <events>
      <event name="indexing:start" />
      <event name="indexing:start:remote" />
      <event name="indexing:end">
        <handler type="Sitecore.ContentSearch.Maintenance.IndexDependentHtmlCacheManager, Sitecore.ContentSearch" method="Clear" />
      </event>
      <event name="indexing:end:remote" />

      <event name="publish:end:remote">
        <handler type="Sitecore.ContentSearch.Events.PublishingEventHandler, Sitecore.ContentSearch" method="OnFullPublishEndRemoteHandler"/>
      </event>
      <event name="publish:end">
        <handler type="Sitecore.ContentSearch.Events.PublishingEventHandler, Sitecore.ContentSearch" method="OnFullPublishEndHandler"/>
      </event>
      <event name="publish:end">
        <handler type="Sitecore.ContentSearch.Events.PublishingEventHandler, Sitecore.ContentSearch" method="OnPublishHandler"/>
      </event>
      <event name="publish:end:remote">
        <handler type="Sitecore.ContentSearch.Events.PublishingEventHandler, Sitecore.ContentSearch" method="OnPublishRemoteHandler"/>
      </event>
      <event name="indexing:updateditem">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampHandler"/>
      </event>
      <event name="indexing:deleteitem">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampHandler"/>
      </event>
      <event name="indexing:deletegroup">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampHandler"/>
      </event>
      <event name="indexing:end">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampDirectHandler"/>
      </event>
      <event name="indexing:committed">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampDirectHandler"/>
      </event>
      <event name="indexing:end:remote">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="IndexEndedRemoteHandler"/>
      </event>
      <event name="indexing:end:remote">
        <handler type="Sitecore.ContentSearch.Events.IndexingEventHandler, Sitecore.ContentSearch" method="UpdateIndexTimestampDirectHandler"/>
      </event>
      <event name="packageinstall:starting">
        <handler type="Sitecore.ContentSearch.Events.PackagingEventHandler, Sitecore.ContentSearch" method="OnPackageInstallStartingHandler"/>
      </event>
      <event name="packageinstall:poststep:starting">
        <handler type="Sitecore.ContentSearch.Events.PackagingEventHandler, Sitecore.ContentSearch" method="OnPackagePostStepInstallStartingHandler"/>
      </event>
      <event name="packageinstall:items:ended">
        <handler type="Sitecore.ContentSearch.Events.PackagingEventHandler, Sitecore.ContentSearch" method="OnPackageInstallItemsEndHandler"/>
      </event>
      <event name="packageinstall:ended">
        <handler type="Sitecore.ContentSearch.Events.PackagingEventHandler, Sitecore.ContentSearch" method="OnPackageInstallerEndHandler"/>
      </event>

    </events>
    <!-- This runs all the required initialization processes, such as EventHub registration and warm-up queries for the index. If you want 
         to add your own queries, you can extend this class or add another hook. -->
    <hooks>
      <hook type="Sitecore.ContentSearch.Hooks.Initializer, Sitecore.ContentSearch" />
    </hooks>
    <pipelines>
      <!-- SEARCH PIPELINES -->
      <!-- BUCKETS STRIP QUERY STRING PARAMETERS
           This pipeline strips out parts of a URL request before it is resolved. This is useful for removing parts of the URL that the
           LINQ layer does not need to know about e.g. IndexShardName or QueryType.
           Arguments    : (IEnumberable<SearchStringModel>) The UI Query wrapped up in strongly typed objects.
           Example Usage:If you send a direct http request to the search handler (Search.ashx) that contains query string parameters e.g. shard = 1, 
           this is processed by the full query pipeline. You can use the stripQueryStringParameters pipeline to remove any unnecessary parameters that 
           you don’t want the Linq Layer to process.

      -->
      <contentSearch.stripQueryStringParameters>
        <processor type="Sitecore.ContentSearch.Pipelines.StripQueryStringParameters.StripQueries, Sitecore.ContentSearch" />
        <processor type="Sitecore.ContentSearch.Pipelines.StripQueryStringParameters.RemoveEmptySearches, Sitecore.ContentSearch" />
      </contentSearch.stripQueryStringParameters>
      
      <!-- BUCKETS GET CONTEXT INDEX
           This pipeline retrieves the index that should be queried based on the context item or the location that the search
           was initiated from.
           Arguments    : (Item) The item used to determine the context index.
           Example Usage: If you need some custom logic to determine which index should be queried and written to based on the context item, 
                          you can create it here. For example, if the context item contains a field that links to another item that determines the context index.
      -->
      <contentSearch.getContextIndex>
        <processor type="Sitecore.ContentSearch.Pipelines.GetContextIndex.FetchIndex, Sitecore.ContentSearch" />
      </contentSearch.getContextIndex>
      
      <!-- BUCKETS GET GLOBAL SEARCH FILTERS
           This pipeline sets the global search filters for every query that is run through the UI.
           Example: You don't want anyone to be able to globally search for a particular template.
           Arguments    : (IQueryable) An open session to the index.
           Example Usage: If you want every query that goes through the UI to use a filter, such as Security, or Templates, place this filter here. 
           Best Practice: 
           The Filter predicate is a good replacement for the Where predicate, when the predicate in the Where statement is repeated in a lot of your code. 
           For example, if you find that your code is always putting the same Where predicate in all your LINQ statements, the Filter predicate is a good replacement.
           When you build your predicates for IQueryable, use the Filter predicate instead of e.g. the Where predicate.
           The Filter predicate caches itself in memory and the Search Provider does not have to keep re-running this part of the query.
           This results in search queries that consistently take the same time to run.
      -->
      <contentSearch.getGlobalSearchFilters>
        <processor type="Sitecore.ContentSearch.Pipelines.GetGlobalFilters.ApplyGlobalFilters, Sitecore.ContentSearch" />
      </contentSearch.getGlobalSearchFilters>

      <!-- GET GLOBAL LINQ FILTERS
           This pipeline allows you to apply global search filters to every query that is executed through the Linq layer or the search UI.
           For example, this pipeline could be used to apply a filter that globally limits search results based on security or templates.
           Arguments: (object) Query, (Type) QueryElementType, (List<IExecutionContext>) ExecutionContext
      -->
      <contentSearch.getGlobalLinqFilters>
        <processor type="Sitecore.ContentSearch.Pipelines.QueryGlobalFilters.ApplyGlobalLinqFilters, Sitecore.ContentSearch" />
      </contentSearch.getGlobalLinqFilters>
      
      <!-- CONTENT SEARCH GET FACETS
           This pipeline pre-processes facets.
           Arguments : (IDictionary<string, ICollection<KeyValuePair<string, int>>>) Facet values
      -->
      <contentSearch.getFacets>
        <processor type="Sitecore.ContentSearch.Pipelines.GetFacets.FacetsPreProcessing, Sitecore.ContentSearch" />
      </contentSearch.getFacets>
      
      <!-- CONTENT SEARCH PROCESS FACETS
           This pipeline post processes facets.
           Arguments : (IDictionary<string, ICollection<KeyValuePair<string, int>>>) Facet values
      -->
      <contentSearch.processFacets>
        <processor type="Sitecore.ContentSearch.Pipelines.ProcessFacets.FacetsPostProcessing, Sitecore.ContentSearch" />
      </contentSearch.processFacets>
      
      <!-- BUCKETS QUERY WARMUP
           This pipeline runs search queries that warm up the index caches when Sitecore is initialized.
           Only enable this in a production environment.
           Arguments : (IQueryable) Open session to the search index.
      -->
      <contentSearch.queryWarmup>
        <processor type="Sitecore.ContentSearch.Pipelines.QueryWarmups.RunQueries, Sitecore.ContentSearch" />
      </contentSearch.queryWarmup>
      <!-- BUCKETS TRANSLATE QUERY
           This pipeline takes a search query and manipulates the search terms before passing it to the Linq layer.
           Example usage: When the Solr search provider runs date searches, it must use a different format than Lucene. If other providers are integrated,
                          this gives the ability to modify field names, values, and boolean operations before translation to the Linq layer.
      -->
      <contentSearch.translateQuery>
        <processor type="Sitecore.ContentSearch.Pipelines.TranslateQuery.AnalyticsContextResolver, Sitecore.ContentSearch" />
        <processor type="Sitecore.ContentSearch.Pipelines.TranslateQuery.ApplySitecoreContextResolvers, Sitecore.ContentSearch" />
        <processor type="Sitecore.ContentSearch.Pipelines.TranslateQuery.ApplyProviderTranslation, Sitecore.ContentSearch" />
      </contentSearch.translateQuery>

      <!-- CONTENT SEARCH - CLEAN UP
           This pipeline allows you to modify the data of an IIndexable before the document builder processes it. For example, you can use
           this pipeline to de-duplicate, merge, or override data.
           Arguments : (IIndexable) The document being indexed, (IProviderUpdateContext) The open update context for the index, (bool) A flag
           that indicates if the IIndexable was modified during the process
      -->
      <contentSearch.cleanUp>
        <processor type="Sitecore.ContentSearch.Pipelines.CleanUp.ApplyCleanUp, Sitecore.ContentSearch"/>
      </contentSearch.cleanUp>
      
      <!-- INDEXING REBUILD
           A special pipeline designed to be executed from the Index Manager dialog box. Reserved for system use only.
           Arguments : (ISearchIndex) The search index.
      -->
      <indexing.filterIndex.inbound>
        <processor type="Sitecore.ContentSearch.Pipelines.IndexingFilters.ApplyInboundIndexFilter, Sitecore.ContentSearch"/>
      </indexing.filterIndex.inbound>

      <!-- INDEX OUTBOUND FILTER
           Pipeline designed to filter out items when they are retrieved from the index.
           By default this applies standard Sitecore item-level security restrictions.
      -->
      <indexing.filterIndex.outbound>
        <processor type="Sitecore.ContentSearch.Pipelines.IndexingFilters.ApplyOutboundSecurityFilter, Sitecore.ContentSearch"/>
      </indexing.filterIndex.outbound>

      <!-- INDEXING GET DEPENDENCIES
           This pipeline fetches dependant items when one item is being index. Useful for fetching related or connected items that also
           need to be updated in the indexes.
           Arguments: (IQueryable) Open session to the search index, (Item) The item being indexed.
           Examples: Update clone references.
                     Update the data sources that are used in the presentation components for the item being indexed.
      -->

      <indexing.getDependencies help="Processors should derive from Sitecore.ContentSearch.Pipelines.GetDependencies.BaseProcessor">
        <!-- When indexing an item, make sure its clones get re-indexed as well -->
        <!--<processor type="Sitecore.ContentSearch.Pipelines.GetDependencies.GetCloningDependencies, Sitecore.ContentSearch"/>-->
        <!-- When indexing an item, make sure its datasources that are used in the presentation details gets re-indexed as well -->
        <!--<processor type="Sitecore.ContentSearch.Pipelines.GetDependencies.GetDatasourceDependencies, Sitecore.ContentSearch"/>-->
      </indexing.getDependencies>

      <!-- RESOLVE FIELD LEVEL BOOSTING
           Pipeline for resolving boosting rules on fields.
           Arguments: (Item) Item being indexed
           Example : Boost search results by a field value.
      -->
      <indexing.resolveFieldBoost help="Processors should derive from Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveFieldBoost.BaseResolveFieldBoostPipelineProcessor">
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveFieldBoost.SystemFieldFilter, Sitecore.ContentSearch"/>
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveFieldBoost.FieldDefinitionItemResolver, Sitecore.ContentSearch"/>
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveFieldBoost.StaticFieldBoostResolver, Sitecore.ContentSearch"/>
      </indexing.resolveFieldBoost>

      <!-- RESOLVE ITEM LEVEL BOOSTING
           Pipeline for resolving boosting rules on items.
           Arguments: (Item) Item being indexed
           Example : Boost search results by a Item Template.
      -->
      <indexing.resolveItemBoost help="Processors should derive from Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveItemBoost.BaseResolveItemBoostPipelineProcessor">
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveItemBoost.ItemLocationFilter, Sitecore.ContentSearch">
          <includedLocations hint="list">
            <content>/sitecore/content</content>
            <media>/sitecore/media library</media>
          </includedLocations>
        </processor>
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveItemBoost.StaticItemBoostResolver, Sitecore.ContentSearch"/>
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveItemBoost.LocalRuleBasedItemBoostResolver, Sitecore.ContentSearch"/>
        <processor type="Sitecore.ContentSearch.Pipelines.ResolveBoost.ResolveItemBoost.GlobalRuleBasedItemBoostResolver, Sitecore.ContentSearch"/>
      </indexing.resolveItemBoost>

      <search>
        <!-- EXTEND SEARCH PIPELINE
             This processor executes search expressions that use the Sitecore.ContentSearch indexes when the Search.UseLegacySearchEngine setting is set to false.
        -->
        <processor patch:before="*[@type='Sitecore.Pipelines.Search.SearchSystemIndex, Sitecore.Kernel']" type="Sitecore.ContentSearch.Client.Pipelines.Search.SearchContentSearchIndex, Sitecore.ContentSearch.Client" />
      </search>
    </pipelines>

    <!-- BOOSTING MANAGER
         The manager class controlling the boosting resolution logic
    -->
    <boostingManager defaultProvider="default" enabled="true">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.ContentSearch.Boosting.PipelineBasedBoostingProvider, Sitecore.ContentSearch"/>
      </providers>
    </boostingManager>

    <!-- SEARCH MANAGER
         The search manager and provider classes 
    -->
    <searchManager defaultProvider="default" enabled="true">
      <providers>
        <clear/>
        <add name="default" type="Sitecore.ContentSearch.PipelineBasedSearchProvider, Sitecore.ContentSearch" />
      </providers>
    </searchManager>

    <scheduling>
      <!-- An agent to optimize the specified indexes periodically. -->
      <agent type="Sitecore.ContentSearch.Tasks.Optimize" method="Run" interval="01:00:00">
        <indexes hint="list">
          <index>sitecore_master_index</index>
        </indexes>
      </agent>
    </scheduling>

    <settings>
      <!--  INDEXING DISABLE DATABASE CACHES
            Indicates whether or not to populate database caches with data retrieved during indexing.
            If true, Sitecore indexes every version and language of the items as usual, but does not cache this item data in the database
            caches. This can reduce the amount of memory used for cached data and improve performance, especially in solutions with a large 
            number of items, versions, and languages.
            If false, Sitecore indexes every version and language of the items and caches the item data in the database caches.
            Default value: false
      -->
      <setting name="ContentSearch.Indexing.DisableDatabaseCaches" value="false"/>
      
      <!--  PARALLEL INDEXING
            Use parallel optimization when indexing.
      -->
      <setting name="ContentSearch.ParallelIndexing.Enabled" value="true" />

      <!--  PARALLEL INDEXING MAX THREAD LIMIT
            This setting allows you to limit the number of threads used for indexing operations when parallel indexing is enabled.
            If the value is set to 0, there is no limit to the number of threads.
            Default value: 3
      -->
      <setting name="ContentSearch.ParallelIndexing.MaxThreadLimit" value="3" />

      <!--  CONTENT SEARCH SEARCH MAX RESULTS
            The max number of results that a query returns.
            Every search requires that you set a limiter on the results. in accordance with Lucene best practices, this can be set but it is best kept
            as the default. It would be best to set this when you have an extremely large amount of items and you don't need to bring back all the items.
            
            A common scenario is that people search for everything but only look at page 1 or 2 of the results.
            
            Default value: "" which translates to int.MaxValue
      -->
      <setting name="ContentSearch.SearchMaxResults" value="" />

      <!--  ENABLE FULL LEVEL DEBUG OF CONTENT SEARCHES
            When enabled this will output full verbose search logging. Usage is for developers and also very useful for support.
            You will need to make sure that Log4Net is also set the DEBUG level for its verbosity.
      -->
      <setting name="ContentSearch.EnableSearchDebug" value="false" />

      <!-- CONTENT SEARCH - CRAWLING - STOP ON CRAWL ERROR
           This setting specifies whether the Sitecore crawlers stop crawling if an error occurs while processing an indexable.
           If true, the Sitecore crawlers stop crawling and throw an exception.
           If false, the Sitecore crawlers skip this indexable and then continue crawling.
           Default value: false
      -->
      <setting name="ContentSearch.Crawling.StopOnCrawlError" value="false"/>

      <!-- CONTENT SEARCH - CRAWLING - STOP ON CRAWL FIELD ERROR
           This setting specifies whether the Sitecore document builder classes will add document data to the index if an error occurs while
           processing one or more fields for the document.
           If false, the document will be added to the index.
           If true, the document will be skipped.
           Default value: false
      -->
      <setting name="ContentSearch.Crawling.StopOnCrawlFieldError" value="false"/>

      <!-- CONTENT SEARCH - DOCUMENT MAPPING - STOP ON PROPERTY MAPPING ERROR
           This setting specifies whether the document mapper will throw an exception if an error occurs during property mapping.
           If true, an exception is thrown when an error occurs.
           If false, the property where an error occurs is skipped, but property mapping will continue for the remaining properties.
           Default value: true
      -->
      <setting name="ContentSearch.DocumentMapping.StopOnPropertyMappingError" value="true"/>

      <!-- USE LEGACY SEARCH ENGINE
           This setting specifies whether the classic search functionality in Sitecore should use the legacy Sitecore.Search indexes.
           The classic search functionality is initiated from the search field above the content tree, the quick search field in the task
           bar, and the Search application. 
           If false, the search functionality uses the Sitecore.ContentSearch search indexes.
           If true, the search functionality uses the legacy Sitecore.Search search indexes.
      -->
      <setting name="Search.UseLegacySearchEngine" value="false"/>

      <!--  CONTENT SEARCH - DEFAULT INDEX TYPE
            This setting specifies the index type that should be used when an item appears in more than one index.
      -->
      <setting name="ContentSearch.DefaultIndexType" value="Sitecore.ContentSearch.LuceneProvider.LuceneIndex, Sitecore.ContentSearch.LuceneProvider" />

      <!-- CONTENT SEARCH - DEFAULT INDEX CONFIGURATION PATH 
           This setting specifies an XPath expression that points to the default index configuration. The default configuration is used for
           every index that does not have a specified configuration.
      -->
      <setting name="ContentSearch.DefaultIndexConfigurationPath" value="contentSearch/indexConfigurations/defaultLuceneIndexConfiguration" />
    </settings>
    <commands>
      <command name="indexing:rebuild"             type="Sitecore.ContentSearch.Client.Commands.Rebuild,Sitecore.ContentSearch.Client" />
      <command name="indexing:refreshtree"         type="Sitecore.ContentSearch.Client.Commands.RefreshTree,Sitecore.ContentSearch.Client" />
      <command name="indexing:rebuildall"          type="Sitecore.ContentSearch.Client.Commands.RebuildAll,Sitecore.ContentSearch.Client" />
      <command name="indexing:ui:rebuilt"          type="Sitecore.ContentSearch.Client.Commands.RebuildDone,Sitecore.ContentSearch.Client" />
      <command name="indexing:generatesolrschema"  type="Sitecore.ContentSearch.Client.Commands.RunSolrSchemaBuilder, Sitecore.ContentSearch.Client"/>
      <command name="indexing:runmanager"          type="Sitecore.ContentSearch.Client.Commands.RunIndexingManager, Sitecore.ContentSearch.Client"/>
    </commands>
  </sitecore>
</configuration>
