﻿<?xml version="1.0"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    
    <controlSources>
      <source mode="on" namespace="XCore.SitecoreModules.ImageCropping.Data.Fields" assembly="XCore.SitecoreModules.ImageCropping" prefix="contentExtensionXCore"/>
    </controlSources>
    
    <pipelines>
      <getMediaStream>
        <processor type="XCore.SitecoreModules.ImageCropping.Pipelines.GetMediaStream.CropProcessor, XCore.SitecoreModules.ImageCropping" patch:after="*[@type='Sitecore.Resources.Media.ThumbnailProcessor, Sitecore.Kernel']" />
      </getMediaStream>
      <renderField>
        <processor type="XCore.SitecoreModules.ImageCropping.Pipelines.RenderField.GetImageWithCroppingValue, XCore.SitecoreModules.ImageCropping" patch:after="*[@type='Sitecore.Pipelines.RenderField.GetImageFieldValue, Sitecore.Kernel']" />
      </renderField>
    </pipelines>
    <mediaLibrary>
      <requestProtection>
        <protectedMediaQueryParameters>
          <parameter description="cropregion" name="cropregion"/>          
        </protectedMediaQueryParameters>
      </requestProtection>
      <mediaProvider type="XCore.SitecoreModules.ImageCropping.Resources.Media.CustomMediaProvider, XCore.SitecoreModules.ImageCropping" patch:instead="*[@type='Sitecore.Resources.Media.MediaProvider, Sitecore.Kernel']"  />
    </mediaLibrary>
    <settings>
      <setting name="Media.RequestExtension">
        <patch:attribute name="value"></patch:attribute>
      </setting>
      <setting name="Media.LowercaseUrls" >
        <patch:attribute name="value">true</patch:attribute>
      </setting>
    </settings>
    <encodeNameReplacements>
      <replace mode="on" find=" " replaceWith="-"/>
    </encodeNameReplacements>
  </sitecore>
</configuration>