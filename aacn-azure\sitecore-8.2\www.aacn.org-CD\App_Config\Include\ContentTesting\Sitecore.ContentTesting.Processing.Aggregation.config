﻿<?xml version="1.0" encoding="utf-8"?>
<!--

Purpose: This include file contains aggregation processors.
ENABLE FOR SERVER ROLE: [Processing]

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>    
      <group groupName="analytics.aggregation">
        <pipelines>
          <interactions>
            <processor type="Sitecore.ContentTesting.Analytics.Aggregation.Pipeline.MvTestingProcessor, Sitecore.ContentTesting" />
            <processor type="Sitecore.ContentTesting.Analytics.Aggregation.Pipeline.MvTestingDetailsProcessor, Sitecore.ContentTesting" />
            <processor type="Sitecore.ContentTesting.Analytics.Aggregation.Pipeline.TestPageClicksProcessor, Sitecore.ContentTesting"/>
            <processor type="Sitecore.ContentTesting.Analytics.Aggregation.Pipeline.TestConversionsProcessor, Sitecore.ContentTesting" />
            <processor type="Sitecore.ContentTesting.Analytics.Aggregation.Pipeline.PersonalizationProcessor, Sitecore.ContentTesting" />
          </interactions>
        </pipelines>
      </group>
    </pipelines>
  </sitecore>
</configuration>