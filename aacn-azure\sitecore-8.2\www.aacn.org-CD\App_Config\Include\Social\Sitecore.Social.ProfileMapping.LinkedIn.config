﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the visitor's profile information that is requested when a visitor logs in to the website 
with their LinkedIn credentials.

Please read the Sitecore Social Connected documentation before changing the configuration.

You can enable or disable a field to extend or reduce the amount of visitor's profile information that is received from LinkedIn.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <social>
      <!-- This section configures social profile synchronization. 
           To include a specific field from the social site in the user's profile, set the 'enabled' property to 'true'. 
           To remove a specific field on the social site from the user's profile, set the 'enabled' property to 'false'. -->
      <profileKeyMappings>

        <!-- Configuration of LinkedIn's profile synchronization. -->
        <network name="LinkedIn">

          <!-- ================= Basic profile fields ====================================== -->

          <!-- 'ID' field. -->
          <field enabled="true" xmlPath="person/id" originalKey="id" mainNetworkDataPropertyName="Id" sitecoreKey="ln_id" text="Id" />

          <!-- 'First name' field. -->
          <field enabled="true" xmlPath="person/first-name" originalKey="first-name" sitecoreKey="ln_first-name" text="First Name" />

          <!-- 'Last name' field. -->
          <field enabled="true" xmlPath="person/last-name" originalKey="last-name" sitecoreKey="ln_last-name" text="Last Name" />

          <!-- 'Headline' field. -->
          <field enabled="true" xmlPath="person/headline" originalKey="headline" sitecoreKey="ln_headline" text="Head Line" />

          <!-- 'Location name' field. -->
          <field enabled="true" xmlPath="person/location/name" originalKey="location:(name,country:(code))" sitecoreKey="ln_location_name" text="Location Name" />

          <!-- 'Location country code' field. -->
          <field enabled="true" xmlPath="person/location/country/code" originalKey="location:(name,country:(code))" sitecoreKey="ln_location_country_code" text="Location Country Code" />

          <!-- 'Industry' field. -->
          <field enabled="true" xmlPath="person/industry" originalKey="industry" sitecoreKey="ln_industry" text="Industry" />

          <!-- 'Distance' field. -->
          <field enabled="true" xmlPath="person/distance" originalKey="distance" sitecoreKey="ln_distance" text="Distance" />

          <!-- 'Relation to viewer (distance)' field. -->
          <field enabled="true" xmlPath="person/relation-to-viewer/distance" originalKey="relation-to-viewer:(distance,num-related-connections,related-connections,connections)" sitecoreKey="ln_relation-to-viewer_distance" text="Relation to Viewer Distance" />

          <!-- 'Relation to viewer (num related connections)' field. -->
          <field enabled="true" xmlPath="person/relation-to-viewer/num-related-connections" originalKey="relation-to-viewer:(distance,num-related-connections,related-connections,connections)" sitecoreKey="ln_relation-to-viewer_num-related-connections" text="Relation to Viewer Num Related Connections" />

          <!-- 'Relation to viewer (related connections)' field. -->
          <field enabled="true" xmlPath="person/relation-to-viewer/related-connections" originalKey="relation-to-viewer:(distance,num-related-connections,related-connections,connections)" sitecoreKey="ln_relation-to-viewer_related-connections" text="Relation to Viewer Related Connections" />

          <!-- 'Current share' field. -->
          <field enabled="true" xmlPath="person/current-share" originalKey="current-share" sitecoreKey="ln_current-share" text="Current Share" />

          <!-- 'Num connections' field. -->
          <field enabled="true" xmlPath="person/num-connections" originalKey="num-connections" sitecoreKey="ln_num-connections" text="Num Connections" />

          <!-- 'Num connections capped' field. -->
          <field enabled="true" xmlPath="person/num-connections-capped" originalKey="num-connections-capped" sitecoreKey="ln_num-connections-capped" text="Num Connections Capped" />

          <!-- 'Summary' field. -->
          <field enabled="true" xmlPath="person/summary" originalKey="summary" sitecoreKey="ln_summary" text="Summary" />

          <!-- 'Specialties' field. -->
          <field enabled="true" xmlPath="person/specialties" originalKey="specialties" sitecoreKey="ln_specialties" text="Specialties" />

          <!-- 'Positions' field. -->
          <field enabled="true" xmlPath="person/positions" originalKey="positions" sitecoreKey="ln_positions" text="Positions" />

          <!-- 'Picture url' field. -->
          <field enabled="true" xmlPath="person/picture-url" originalKey="picture-url" sitecoreKey="ln_picture-url"  text="Picture Url" />

          <!-- 'Site standard profile request url' field. -->
          <field enabled="true" xmlPath="person/site-standard-profile-request/url" originalKey="site-standard-profile-request:(url)" sitecoreKey="ln_site-standard-profile-request_url" text="Site Standard Profile Request Url" />

          <!-- 'Api standard profile request url' field. -->
          <field enabled="true" xmlPath="person/api-standard-profile-request/url" originalKey="api-standard-profile-request:(url,headers)" sitecoreKey="ln_api-standard-profile-request_url" text="API Standard Profile Request Url" />

          <!-- 'Api standard profile request headers' field. -->
          <field enabled="true" xmlPath="person/api-standard-profile-request/headers" originalKey="api-standard-profile-request:(url,headers)" sitecoreKey="ln_api-standard-profile-request_headers" text="API Standard Profile Request Headers" />

          <!-- 'Public profile url' field. -->
          <field enabled="true" xmlPath="person/public-profile-url" originalKey="public-profile-url" sitecoreKey="ln_public-profile-url" text="Public Profile Url" />

          <!-- ================= Connection fields ====================================== -->

          <!-- 'Connections' field. -->
          <field enabled="false" xmlPath="person/connections" originalKey="connections" sitecoreKey="ln_connections" permission="r_network" text="Connections" />

          <!-- ================= Email profile fields ====================================== -->

          <!-- 'Email address' field. -->
          <field enabled="true" xmlPath="person/email-address" originalKey="email-address" sitecoreKey="ln_email-address" permission="r_emailaddress" text="Email Address" />

          <!-- ================= Full profile fields ====================================== -->

          <!-- 'Proposal comments' field. -->
          <field enabled="false" xmlPath="person/proposal-comments" originalKey="proposal-comments" sitecoreKey="ln_proposal-comments" permission="r_fullprofile" text="Proposal Comments" />

          <!-- 'Associations' field. -->
          <field enabled="false" xmlPath="person/associations" originalKey="associations" sitecoreKey="ln_associations" permission="r_fullprofile" text="Associations" />

          <!-- 'Honors' field. -->
          <field enabled="false" xmlPath="person/honors-awards" originalKey="honors-awards" sitecoreKey="ln_honors" permission="r_fullprofile" text="Honors and Awards" />

          <!-- 'Interests' field. -->
          <field enabled="false" xmlPath="person/interests" originalKey="interests" sitecoreKey="ln_interests" permission="r_fullprofile" text="Interests" />

          <!-- 'Publications' field. -->
          <field enabled="false" xmlPath="person/publications" originalKey="publications" sitecoreKey="ln_publications" permission="r_fullprofile" text="Publications" />

          <!-- 'Patents' field. -->
          <field enabled="false" xmlPath="person/patents" originalKey="patents" sitecoreKey="ln_patents" permission="r_fullprofile" text="Patents" />

          <!-- 'Languages' field. -->
          <field enabled="false" xmlPath="person/languages" originalKey="languages" sitecoreKey="ln_languages" permission="r_fullprofile" text="Languages" />

          <!-- 'Skills' field. -->
          <field enabled="false" xmlPath="person/skills" originalKey="skills" sitecoreKey="ln_skills" permission="r_fullprofile" text="Skills" />

          <!-- 'Certifications' field. -->
          <field enabled="false" xmlPath="person/certifications" originalKey="certifications" sitecoreKey="ln_certifications" permission="r_fullprofile" text="Certifications" />

          <!-- 'Educations' field. -->
          <field enabled="false" xmlPath="person/educations" originalKey="educations" sitecoreKey="ln_educations" permission="r_fullprofile" text="Educations" />

          <!-- 'Three current positions' field. -->
          <field enabled="false" xmlPath="person/three-current-positions" originalKey="three-current-positions" sitecoreKey="ln_three-current-positions" permission="r_fullprofile" text="Three Current Positions" />

          <!-- 'Three past positions' field. -->
          <field enabled="false" xmlPath="person/three-past-positions" originalKey="three-past-positions" sitecoreKey="ln_three-past-positions" permission="r_fullprofile" text="Three Past Positions" />

          <!-- 'Num recommenders' field. -->
          <field enabled="false" xmlPath="person/num-recommenders" originalKey="num-recommenders" sitecoreKey="ln_num-recommenders" permission="r_fullprofile" text="Num Recommenders" />

          <!-- 'Recommendations received' field. -->
          <field enabled="false" xmlPath="person/recommendations-received" originalKey="recommendations-received" sitecoreKey="ln_recommendations-received" permission="r_fullprofile" text="Recommendations Received" />

          <!-- 'Date of birth' field. -->
          <field enabled="false" xmlPath="person/date-of-birth" originalKey="date-of-birth" sitecoreKey="ln_date-of-birth" permission="r_fullprofile" text="Date of Birth" />

          <!-- 'Member url resources' field. -->
          <field enabled="false" xmlPath="person/member-url-resources" originalKey="member-url-resources" sitecoreKey="ln_member-url-resources" permission="r_fullprofile" text="Member Url Resources" />

          <!-- ================= Contact info fields ====================================== -->

          <!-- 'Phone numbers' field. -->
          <field enabled="false" xmlPath="person/phone-numbers" originalKey="phone-numbers" sitecoreKey="ln_phone-numbers" permission="r_contactinfo" text="Phone Numbers" />

          <!-- 'Im accounts' field. -->
          <field enabled="false" xmlPath="person/im-accounts" originalKey="im-accounts" sitecoreKey="ln_im-accounts" permission="r_contactinfo" text="Im Accounts" />

          <!-- 'Twitter accounts' field. -->
          <field enabled="false" xmlPath="person/twitter-accounts" originalKey="twitter-accounts" sitecoreKey="ln_twitter-accounts" permission="r_contactinfo" text="Twitter Accounts" />

          <!-- 'Main address' field. -->
          <field enabled="false" xmlPath="person/main-address" originalKey="main-address" sitecoreKey="ln_main-address" permission="r_contactinfo" text="Main Address" />
        </network>

      </profileKeyMappings>
    </social>
  </sitecore>
</configuration>