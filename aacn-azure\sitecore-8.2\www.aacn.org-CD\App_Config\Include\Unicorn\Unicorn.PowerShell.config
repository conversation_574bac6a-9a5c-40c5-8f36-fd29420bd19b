﻿<!--
	Unicorn.PowerShell.config

	Activates Sitecore PowerShell Extensions (SPE) cmdlets for Unicorn operations.
	
	The following cmdlets are currently supported:
	Get-UnicornConfiguration
	Sync-UnicornConfiguration
	Sync-UnicornItem
	Export-UnicornConfiguration
	Export-UnicornItem
	ConvertTo-RainbowYaml
	ConvertFrom-RainbowYaml
	Import-RainbowYaml
	
	Use 'Get-Help [cmdname]' at a SPE console to see the arguments for each command.
	
	NOTE: This file may remain active without SPE installed and it won't break anything.
	
	This file should be active on any environment where you wish to execute Unicorn SPE commands. Usually, that'd be dev and CE only. 
	If you're not using SPE you can disable or remove this file, but it won't hurt anything to leave it either.
	
	http://github.com/kamsar/Unicorn
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<powershell>
			<commandlets>
				<add Name="Unicorn Commandlets" type="*, Unicorn" />
			</commandlets>
		</powershell>
	</sitecore>
</configuration>