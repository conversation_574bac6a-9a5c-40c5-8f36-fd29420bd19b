﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file enables the tracking subsystem of the Sitecore Experience Database to write to and read from the collection database.

If you want to disable this functionality and prevent the tracking subsystem from communicating with the collection database, you can
rename this config file so that it has a ".disabled" extension.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <pipelines>
      
      <commitSession>
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.CheckPreconditions, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.UpdateGeoIpData, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.EnsureClassification, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.Robots, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.IgnoreVisitsWithoutPages, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.UpdateContactSaveMode, Sitecore.Analytics" />
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.SubmitSession, Sitecore.Analytics" >
          <SessionContextManager ref="tracking/sessionContextManager" />
        </processor>
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.ProcessSubscriptions, Sitecore.Analytics"/>
        <processor type="Sitecore.Analytics.Pipelines.CommitSession.ReleaseSharedSession, Sitecore.Analytics" runIfAborted="true"/>
      </commitSession>
      
      <postSessionEnd>
        <processor type="Sitecore.Analytics.Pipelines.PostSessionEnd.CommitSession, Sitecore.Analytics" />
      </postSessionEnd>
      
      <submitSessionContext>
        <processor type="Sitecore.Analytics.Pipelines.SubmitSessionContext.SaveDevice, Sitecore.Analytics" patch:before="*" />
        <processor type="Sitecore.Analytics.Pipelines.SubmitSessionContext.SaveClassificationsMap, Sitecore.Analytics" patch:after="*[1]" />
        <processor type="Sitecore.Analytics.Pipelines.SubmitSessionContext.SaveVisit, Sitecore.Analytics" patch:after="*[2]" />
      </submitSessionContext>
      
    </pipelines>
    
    <submitQueue>
      <backgroundService type="Sitecore.Analytics.SubmitQueueService, Sitecore.Analytics">
        <!-- Service wakeup interval in seconds. -->
        <Interval>60</Interval>
      </backgroundService>
    </submitQueue>
    
    <tracking>
      <contactManager type="Sitecore.Analytics.Tracking.ContactManager, Sitecore.Analytics" singleInstance="true">
        <param desc="sharedSessionStateManager" ref="tracking/sharedSessionState/manager" />
        <param desc="contactRepository">
          <patch:attribute name="ref">contactRepository</patch:attribute>
        </param>
      </contactManager>
    </tracking>
    
  </sitecore>
</configuration>