﻿<!--
	Unicorn Deployed Configuration

	This file configures Unicorn to throw up warnings and prompts when saving items that are included by Unicorn.

	This is appropriate to use in a CE environment where Unicorn items are deployed to, as it will warn careless editors to not change items controlled by Unicorn.
	Do not enable this in a development environment.

	http://github.com/kamsar/Unicorn
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<processors>
			<saveUI>
				<!--
					This processor will throw up a warning if you try to save an item that is controlled by Unicorn when it has been deployed.
				-->
				<processor patch:before="*[@type='Unicorn.UI.Pipelines.SaveUi.SerializationConflictProcessor, Unicorn']" mode="on" type="Unicorn.UI.Pipelines.SaveUi.SerializationChangeBlocker, Unicorn"/>
			</saveUI>
		</processors>

		<settings>
			<!-- Changes the text of the warning for items that are serialized by Unicorn. -->
			<setting name="Unicorn.DevMode" value="false"/>
		</settings>
	</sitecore>
</configuration>
