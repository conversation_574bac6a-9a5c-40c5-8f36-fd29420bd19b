﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file enables and configures the Path Analyzer component; a mechanism for analyzing
visitor paths through a website and evaluating the analytics values associated with those visits.
This include file contains configuration settings for the core Path Analyzer application

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- SETTINGS -->
    <settings>
      <!-- PATH ANALYZER - CACHING - TREE CACHE EXPIRATION
           Sets the absolute expiration on cached tree data.
           A value of 00:00:00 disables automatic expiration.

           Default value: 00:00:30 (30 minutes)
        -->
      <setting name="PathAnalyzer.Caching.TreeCacheExpiration" value="00:00:30" />

      <!-- PATH ANALYZER - MAX STORED CONTACTS
           Sets the maximum count of stored contact IDs on PageNodes in a PageTree.
           Default value: 10
        -->
      <setting name="PathAnalyzer.MaxStoredContacts" value="10" />

      <!-- PATH ANALYZER - PRUNING - MAX TREE DEPTH
           Specifies the maximum depth of trees. Tree node sequences will not be added beyond this depth.
        -->
      <setting name="PathAnalyzer.Pruning.MaxTreeDepth" value="20" />

      <!-- PATH ANALYZER - PRUNING - MAX TREE NODE COUNT
           A tree containing more nodes than the amount specified by this setting will be eligible for pruning.
        -->
      <setting name="PathAnalyzer.Pruning.MaxTreeNodeCount" value="10000" />

      <!-- PATH ANALYZER - PRUNING - VISIT COUNT THRESHOLD
           Specifies the visit count threshold using by the tree pruning process.
           Statistically insignificant nodes with subtree counts less than this threshold setting are eligible for pruning.
        -->
      <setting name="PathAnalyzer.Pruning.VisitCountThreshold" value="2" />

      <!-- PATH ANALYZER - REMOTE SERVICES - ENABLED
           Specifies the dictionary domain name containing Path Analyzer string resource definitions.
        -->
      <setting name="PathAnalyzer.Resources.DictionaryDomainName" value="core:PathAnalyzerDictionary" />
    </settings>
    <!-- PATH ANALYZER
         Define the types used by various Path Analyzer services.
    -->
    <pathAnalyzer>
      <!-- CONFIGURATION
           This service provides access to configuration settings used within Path Analyzer.
      -->
      <configuration type="Sitecore.PathAnalyzer.Configuration, Sitecore.PathAnalyzer" />

      <!-- CONTACT READER
           This service reads the set of contact data from contact ids.
      -->
      <contactReader type="Sitecore.PathAnalyzer.Data.DataReaders.ContactReader, Sitecore.PathAnalyzer">
        <repository ref="tracking/contactRepository" />
      </contactReader>

      <!-- DEFINITION SERVICE
           This service is used to perform CRUD operations against map definitions.
      -->
      <definitionService type="Sitecore.PathAnalyzer.Data.TreeDefinitionService, Sitecore.PathAnalyzer" />

      <!-- DEFINITION STORAGE
           This service is used to perform CRUD operations against a specific map definition data store. e.g. SQL Server.
      -->
      <definitionStorage type="Sitecore.PathAnalyzer.Data.Storage.SqlDefinitionStorage, Sitecore.PathAnalyzer" />

      <!-- MAP REPOSITORY
           This service is responsible for creating and managing map objects from Sitecore database.
      -->
      <mapRepository type="Sitecore.PathAnalyzer.Data.MapItemRepository, Sitecore.PathAnalyzer" />

      <!-- PRUNING CONFIGURATION
           This service provides access to configuration settings used by pruning operations within Path Analyzer.
      -->
      <pruningConfiguration type="Sitecore.SequenceAnalyzer.Configuration.PruningConfig, Sitecore.SequenceAnalyzer" />

      <!-- PROPERTY STORE
           This service provides access to RDB's Properties table that stores all untility information related to tree construction.
      -->
      <propertyStore type="Sitecore.PathAnalyzer.Processing.SqlPropertyStore, Sitecore.PathAnalyzer" singleInstance="true">
        <param desc="connectionString">reporting</param>
      </propertyStore>

      <!-- STORAGE
           This service is used to perform CRUD operations against a specific map data store. e.g. SQL Server.
      -->
      <storage type="Sitecore.PathAnalyzer.Data.Storage.SqlStorage, Sitecore.PathAnalyzer" />

      <!-- TREE PROVIDER
           This service is used to perform retrieval operations for maps.
      -->
      <treeProvider type="Sitecore.PathAnalyzer.Data.TreeProvider, Sitecore.PathAnalyzer" singleInstance="true" />

      <!-- TREE CACHE
           This service is responsible for caching map data retrieved from storage.
      -->
      <treeCache type="Sitecore.PathAnalyzer.Data.Caching.TreeCache, Sitecore.PathAnalyzer" singleInstance="true" />

      <!-- SOURCE DATA READER
           This service is responsible for retrieving source data from the xDB.
      -->
      <sourceDataReader type="Sitecore.PathAnalyzer.Data.DataReaders.InteractionDataReader, Sitecore.PathAnalyzer" />

      <!-- MODELS
           This section defines types used by the map serialization/deserialization processes.
      -->
      <models>
        <pageNode type="Sitecore.PathAnalyzer.Data.Models.PageNode, Sitecore.PathAnalyzer" />
        <pageTree type="Sitecore.PathAnalyzer.Data.Models.PageTree, Sitecore.PathAnalyzer" />
      </models>
    </pathAnalyzer>
    <!-- PIPELINES -->
    <pipelines>
      <initialize>
        <processor type="Sitecore.PathAnalyzer.Pipelines.Initialize.Loader, Sitecore.PathAnalyzer" />
      </initialize>
    </pipelines>
  </sitecore>
</configuration>
