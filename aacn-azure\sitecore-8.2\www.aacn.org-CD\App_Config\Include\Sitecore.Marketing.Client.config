﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures Marketing platfrom definition management module.

In most cases, you should leave this file enabled.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <commands>
      <command name="marketing:asset:deploy" type="Sitecore.Marketing.Client.Shell.Framework.Commands.Marketing.Assets.DeployAsset, Sitecore.Marketing.Client" />
      <command name="marketing:opendeploydefinitionsdialog" type="Sitecore.Marketing.Client.Shell.Framework.Commands.Marketing.OpenDeployDefinitionsDialog, Sitecore.Marketing.Client" />
    </commands>
    <pipelines>
      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Applications/Marketing/Utilities/DeployMarketingDefinitions" deep="true" category="deploymarketingdefinitions" pattern="*.js,*.css" />
          </sources>
        </processor>
      </speak.client.resolveScript>
    </pipelines>
  </sitecore>
</configuration>