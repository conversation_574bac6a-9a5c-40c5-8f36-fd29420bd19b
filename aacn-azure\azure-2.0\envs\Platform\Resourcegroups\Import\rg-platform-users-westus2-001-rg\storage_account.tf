module "users_storage" {
  source               = "../../../../../modules/storage_account/st-nocontainer"
  storage_account_name = "stplatformusers001"
  resource_group_name  = var.resource_group_name
  location             = var.location
  tags                 = var.resource_group_tags
  # container_name       = "usertobedeleted"
  }  

module "dept_storage" {
  source               = "../../../../../modules/storage_account/st-nocontainer"
  storage_account_name = "stplatformdept001"
  resource_group_name  = var.resource_group_name
  location             = var.location
  tags                 = var.resource_group_tags
  # container_name       = "datatobedeleted"
}
