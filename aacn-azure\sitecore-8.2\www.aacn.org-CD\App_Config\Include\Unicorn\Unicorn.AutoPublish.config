<!--
	Unicorn Auto Publish Configuration

	This file configures the Unicorn serialization system to automatically publish items that it syncs.

	This file should be removed on content delivery environments.

	http://github.com/kamsar/Unicorn
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<pipelines>
			<unicornSyncComplete>
				<!-- at the end of each configuration's sync, we throw the items it changed into ManualPublishQueueHandler's queue -->
				<processor type="Unicorn.Pipelines.UnicornSyncComplete.AddSyncedItemsToPublishQueue, Unicorn" />
			</unicornSyncComplete>

			<unicornSyncEnd>
				<!-- when all configurations have synced, fire off a publish that processes the queue we've accumulated -->
				<processor type="Unicorn.Pipelines.UnicornSyncEnd.TriggerAutoPublishSyncedItems, Unicorn">
					<PublishTriggerItemId>/sitecore/templates/Common/Folder</PublishTriggerItemId> <!-- the trigger item can be any leaf node Sitecore item - just has to have a 'starting point' for the publish -->
					<!-- these are the database(s) to publish synced items to -->
					<TargetDatabases hint="list:AddTargetDatabase">
						<web>web</web>
            <webcd>webcd</webcd>            
					</TargetDatabases>
				</processor>
			</unicornSyncEnd>

			<publish>
				<!--
					This handler manually injects arbitrary items into the publish queue so that the next publish to occur will cause the injected items to get published. 
					See http://www.velir.com/blog/index.php/2013/11/22/how-to-create-a-custom-publish-queue-in-sitecore/ et. al.
				-->
				<processor patch:after="*[@type='Sitecore.Publishing.Pipelines.Publish.AddItemsToQueue, Sitecore.Kernel']" type="Unicorn.Publishing.ManualPublishQueueHandler, Unicorn"/>
			</publish>
		</pipelines>
	</sitecore>
</configuration>
