<configuration>
  <sitecore>
    <settings>
      <!--  SEGMENT BUILDER - RULES EXECUTION PREVIEWER
            This setting is used by the Rule Set Editor in the Segment Builder and specifies the type that calculates the number of contacts
            selected by individual conditions in a rule and the total number of contacts selected by the rule.
            Default value: Sitecore.Rules.SegmentBuilderRulesExecutionPreviewer, Sitecore.SegmentBuilder
      -->
      <setting name="SegmentBuilder.RulesExecutionPreviewer" value="Sitecore.Rules.SegmentBuilderRulesExecutionPreviewer, Sitecore.SegmentBuilder"/>
      <!--  SEGMENT BUILDER - VISITOR COUNTS ENABLED
            This setting specifies if the Rule Set Editor in the Segment Builder should display the number of contacts selected by each
            evaluated condition when these numbers are available, as well as the total number of contacts selected by the rule. You can
            change this setting to "false" if these calculations slow down the UI.
            Default value: true
      -->
      <setting name="SegmentBuilder.VisitorCountsEnabled" value="true"/>
    </settings>
  </sitecore>
</configuration>