<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<settings>
      <setting name="FeatureToggle.EmailVerify" value="true"/>
      <setting name="FeatureToggle.EventLatLongUpdate" value="__FeatureToggle.EventLatLongUpdate__"/>
      <setting name="ExpiryHourRange" value="6"/>

      <!-- setting to display Electronic table of contents on dashboard pages  -->
      <setting name="FeatureToggle.ETOC" value="true"/>      
      <!-- setting to display Electronic table of contents on dashboard pages  -->

      <!-- setting to enable PCCN Cert Review Course Disc Replacement via Value Program  -->
      <setting name="FeatureToggle.PCCNDRVP" value="true"/>
      <!-- setting to enable PCCN Cert Review Course Disc Replacement via Value Program  -->

      <!-- setting to enable CCRN Cert Review Course Disc Replacement via Value Program  -->
      <setting name="FeatureToggle.CCRNADRVP" value="false"/>
      <setting name="FeatureToggle.CCRNPDRVP" value="false"/>
      <setting name="FeatureToggle.CCRNNDRVP" value="false"/>      
      <!-- setting to enable CCRN Cert Review Course Disc Replacement via Value Program  -->
      
      <!--  ERROR HANDLER
            Url of page handling generic errors
      -->
			<setting name="ErrorPage">
				<patch:attribute name="value">/500.aspx</patch:attribute>
			</setting>
			<!--  HTML EDITOR DEFAULT PROFILE 
            Path to the default html editor profile.
            Default value: /sitecore/system/Settings/Html Editor Profiles/Rich Text Default
      -->
			<setting name="HtmlEditor.DefaultProfile">
				<patch:attribute name="value">/sitecore/system/Settings/Html Editor Profiles/Rich Text AACN</patch:attribute>
			</setting>
			<!--  INVALID CHARS
            Characters that are invalid in an item name
      -->
			<setting name="InvalidItemNameChars">
				<patch:attribute name="value">\/:?"&lt;&gt;|[]</patch:attribute>
			</setting>
			<!--  ITEM NAME VALIDATION
            Regular expression for validating item names
      -->
			<setting name="ItemNameValidation" >
				<patch:attribute name="value">^[\w\*\$][\w\s\-\$]*(\(\d{1,}\)){0,1}$</patch:attribute>
			</setting>
			<!--overrides the default setting to remove hour and minute-->
			<setting name="BucketConfiguration.BucketFolderPath" >
				<patch:attribute name="value">yyyy\/MM\/dd</patch:attribute>
			</setting>
			<!-- update this setting to enable css in Rich text editor -->
			<!--<setting name="WebStylesheet" >
        <patch:attribute name="value"></patch:attribute>
      </setting>-->
			<setting name="ItemNotFoundUrl">
				<patch:attribute name="value">/404</patch:attribute>
			</setting>
			<setting name="LayoutNotFoundUrl" >
				<patch:attribute name="value">/404</patch:attribute>
			</setting>
			<setting name="LinkItemNotFoundUrl">
				<patch:attribute name="value">/404</patch:attribute>
			</setting>
			<!--<setting name="MailServer" >
        <patch:attribute name="value">mailtrap.io</patch:attribute>
      </setting>
      -->
			<!--  MAIL SERVER USER If the SMTP server requires login, enter the user name in this setting-->
			<!--
      <setting name="MailServerUserName">
        <patch:attribute name="value">57634b66162fe0ef5</patch:attribute>
      </setting>
      -->
			<!--  MAIL SERVER PASSWORD If the SMTP server requires login, enter the password in this setting-->
			<!--
      <setting name="MailServerPassword">
        <patch:attribute name="value">673a88fbc07f16</patch:attribute>
      </setting>
      -->
			<!--  MAIL SERVER PORT If the SMTP server requires a custom port number, enter the value in this setting. The default value is: 25 -->
			<!--
      <setting name="MailServerPort" >
        <patch:attribute name="value">2525</patch:attribute>
      </setting>-->

			<setting name="IgnoreUrlPrefixes">
				<patch:attribute name="value">/DM/Webservice/Membervalidation.asmx|/sitecore/default.aspx|/trace.axd|/webresource.axd|/sitecore/shell/Controls/Rich Text Editor/Telerik.Web.UI.DialogHandler.aspx|/sitecore/shell/applications/content manager/telerik.web.ui.dialoghandler.aspx|/sitecore/shell/Controls/Rich Text Editor/Telerik.Web.UI.SpellCheckHandler.axd|/Telerik.Web.UI.WebResource.axd|/sitecore/admin/upgrade/|/layouts/testing|/bundles</patch:attribute>
			</setting>

			<setting name="Caching.StandardValues.DefaultCacheSize" value="5MB">
				<patch:attribute name="value">1GB</patch:attribute>
			</setting>

			<setting name="Caching.UserProfileCacheSize.DefaultCacheSize" value="5MB">
				<patch:attribute name="value">1GB</patch:attribute>
			</setting>

			<setting name="RequestErrors.UseServerSideRedirect">
				<patch:attribute name="value">true</patch:attribute>
			</setting>

			<setting name="MembershipProductType" value="Membership Dues" />

			<setting name="SiteSettingsItem" value="{49AC2DB5-2502-446E-94C3-F626673EAB00}"/>

			<setting name="DesktopLoggedInNavigationItem" value="{07DF28AA-58EA-4282-89ED-88F3276AA493}"/>
			<setting name="DesktopMemberNavigationItem" value="{FE1DBCAB-F50F-40D5-BEF5-1D71A16AB6BE}"/>
			<setting name="DesktopLoggedOutNavigationItem" value="{52FB79FD-641D-4A25-96A2-C81AA32F066C}"/>

			<setting name="MobileLoggedInNavigationItem" value="{883EE0A4-6F3F-4A0E-B578-944B558F7878}"/>
			<setting name="MobileMemberNavigationItem" value="{2228A815-5B92-4CA6-9E16-BE1DA0638CC0}"/>
			<setting name="MobileLoggedOutNavigationItem" value="{1A1F8D7D-A680-44BC-80AB-5379872A2947}"/>

			<setting name="MasterAlertDataSourceItem" value="{F5028323-C9EA-416A-A916-4693ACFBABDC}"/>
			<setting name="LoggedInMissingUserName" value="Account"/>
			<setting name="JsonPostContentType" value="text/plain"/>

			<setting name="EmailManager.ContactUsEmailBodyItemId" value="{E5A8B9C8-15A8-45ED-BA12-04E971A0D8DF}" />
			<setting name="EmailManager.RecoverPasswordEmailBodyItemId" value="{DCA8F39E-FC52-4C50-A803-68F839242D7A}" />
			<setting name="EmailManager.RequestAQuoteFormBodyItemId" value="{07F97F57-08F5-4807-B52A-47E6524E9A52}" />
			<setting name="EmailManager.ELearningOrderFormBodyItemId" value="{2937828B-7159-4EE9-B929-38CB6C736DF9}" />
			<setting name="EmailManager.EulaOrderFormBodyItemId" value="{F1F97762-44C3-4E3C-A3F5-E1F6BE93DC2C}" />
			<setting name="EmailManager.PracticeResourceNetworkFormBodyItemId" value="{2C8EC659-87E4-4947-A6C9-BF0DA4BF0764}" />
			<setting name="EmailManager.InvoiceBodyItemId" value="{31A2CCAF-C035-488F-8E4A-2D13D8279152}" />
			<setting name="EmailManager.ExhibitorAppConfirmBodyItemId" value="{22421A2F-7E6B-4DBC-8CF4-6E73BC8909B3}" />
			<setting name="EmailManager.AbstractLiveConfirmBodyItemId" value="{7EFA1B7D-F0B6-4B67-8E93-62E6407E8221}" />
			<setting name="EmailManager.AbstractLiveInviteBodyItemId" value="{A0D93DAF-8AD0-42D9-BB79-C103A71C3E87}" />
			<setting name="EmailManager.AbstractLiveNotifyBodyItemId" value="{29352491-FD1F-480E-B554-95F3F2322F22}" />
			<setting name="EmailManager.AbstractPosterConfirmBodyItemId" value="{F41D5802-DF95-4BBA-A308-6CB66B39CC0E}" />
			<setting name="EmailManager.AbstractPosterInviteBodyItemId" value="{7BB4A84B-4D3E-4D5E-9F71-7E97543CF66B}" />
			<setting name="EmailManager.AbstractPosterNotifyBodyItemId" value="{504521F5-10AB-43BC-A147-01BCDB0E02D4}" />
			<setting name="EmailManager.ExpiredPagesReportEmailId" value="{DA76648F-BF58-4FE5-987F-62CF267C6AC5}" />
      <setting name="EmailManager.ChapterContactEmailId" value="{F6803DC0-25A9-43BC-AA31-DEBEBCC3E6C9}" />
      <setting name="EmailManager.VoucherApplicationConfirmItemId" value="{95BED31A-AA0F-41F9-8348-FD5CFC141693}" />
      <setting name="EmailManager.VoucherApplicationRepresentativeItemId" value="{CDFCE10A-8EC8-4BA6-A231-96A3DF811DAD}" />

      <setting name="EmailManager.FormAddress" value="<EMAIL>" />
			<setting name="EmailManager.WhatsYourStoryAddress" value="<EMAIL>" />
			<setting name="EmailManager.ValueProgramAddress" value="<EMAIL>" />

			<setting name="EmailManager.ExhibitsAddress" value="<EMAIL>" />
			<setting name="EmailManager.ExhibitsAppAddress" value="<EMAIL>" />
			<setting name="EmailManager.NoReplyAddress" value="<EMAIL>" />

			<setting name="NetForum.EmptyCartMessage" value="Cart is empty" />
			<setting name="NetForum.StoreLandingPagePathInfo" value="store" />
			<setting name="NetForum.CartPagePathInfo" value="store/cart" />
			<setting name="NetForum.CheckoutPagePathInfo" value="store/checkout" />
			<setting name="NetForum.LoginPagePathInfo" value="membership/signin" />
			<setting name="NetForum.ReceiptPagePathInfo" value="store/receipt" />
			<setting name="NetForum.AbstractDetailsPathInfo" value="/abstracts/details?abstractid={0}" />
			<setting name="NetForum.AbstractEditPathInfo" value="/abstracts/application{0}?abstractid={1}" />
			<setting name="NetForum.AbstractThankYouPathInfo" value="/abstracts/thank-you" />
			<setting name="NetForum.AbstractPortalPathInfo" value="abstracts" />
			<setting name="NetForum.SponsorPortalLoginPathInfo" value="/conferences-and-events/nti/exhibitors/expoed-evaluations" />
			<setting name="NetForum.SponsorPortalAbstractListPathInfo" value="/conferences-and-events/nti/exhibitors/expoed-abstracts" />
			<setting name="NetForum.AbstractSessionEvaluationReportPathInfo" value="/api/sitecore/Abstract/SessionEvalReport?abstractId={0}" />
			<setting name="NetForum.PA.AddProductToCartPathInfo" value="api/sitecore/programapprovalcart/addapplicationproduct" />
			<setting name="NetForum.PA.InvoicePathInfo" value="education/program-approval/invoice" />
			<setting name="NetForum.PA.DisplayProgramApproval" value="/education/program-approval/{0}/application?programApprovalId={1}" />

			<setting name="ShoppingCart.AddItem" value="api/sitecore/ShoppingCart/Add" />

			<setting name="Commerce.ProductPageSize" value="10" />

			<setting name="Authentication.SaveRawUrl">
				<patch:attribute name="value">true</patch:attribute>
			</setting>

			<setting name="Media.RequestExtension">
				<patch:attribute name="value"></patch:attribute>
			</setting>

			<setting name="ContentSearch.Solr.ServiceBaseAddress" value="http://ilsolr01.aacn.org:8983/solr"/>

			<!-- Netforum Crawler settings -->
			<setting name="ContentSearch.Solr.UseNewCrawler" value="true" />
			<!-- True=Use new crawler, False=Use new crawler -->
			<setting name="ContentSearch.Solr.NewCrawlerSolrCoreOverride" value="" />
			<!-- Blank=Use default Solr core, Not Blank=Write data to different Solr core (this setting only applies to the new crawler) -->

			<setting name="Widgets.DataSourcesRootId" value="{68EA1809-3D3E-4C00-BBC8-411577AF21FB}" />
			<setting name="Widgets.GlobalSettingsFolderId" value="{E89BC35F-B46D-4EC0-A072-4EE4FF34BCDD}" />
			<setting name="Widgets.PageTemplateRoot" value="{1B6EABDC-AD25-491E-8B58-657803CCF706}" />
			<setting name="Widgets.ParentDataSourcesFolder" value="{66A1F38B-FFAB-4657-9D48-BFF5DD85EE1D}" />
			<setting name="Widgets.ChildDataSourcesFolder" value="{85157DCE-E6D0-4096-8597-CEA576F62481}" />

			<setting name="Aacn.OrganizationTypeFolder" value="{17FB58FC-93C6-44F7-849A-6FC81EDAEA96}" />
			<setting name="Aacn.ChaptersFolder" value="{4BFDFA74-42D8-41B6-ACDE-E6D16D2F6DC1}" />
			<setting name="Aacn.PeopleFolder" value="{F991A1DD-6E81-4C71-BC9B-94FC56FA43CD}" />

			<setting name="Aacn.Wizards.AgreementLanguage" value="{AA057221-6629-4C6F-969A-5A8C7D360C32}" />
			<setting name="Aacn.Wizards.AttestationStatement" value="{36F23C2F-3F33-4208-91FE-10C074DC8C1C}" />
			<setting name="Aacn.Wizards.RushProcessing" value="{CC0A8BDB-CCDC-4393-BF73-0D5A4EB11C17}" />

			<setting name="Aacn.DateFormat" value="MMM dd, yyyy" />
			<setting name="Aacn.DateBoostingIndexField" value="publication_date_tdt"></setting>
			<setting name="Aacn.PopularityIndexField" value="popularity" />
			<setting name="Aacn.SolrHighlightsNumberOfSnippets" value="1"></setting>
			<setting name="Aacn.SolrHighlightsFields" value="highlightcontent"></setting>

			<setting name="Aacn.Certification.RenewalByExamForMembersHeader" value="Renewal by Exam for AACN Members"></setting>
			<setting name="Aacn.Certification.RenewalByExamForNonMembersHeader" value="Renewal by Exam for Non-Members"></setting>
			<setting name="Aacn.Certification.RenewalByCERPForMembersHeader" value="Renewal by Synergy CERP for AACN Members"></setting>
			<setting name="Aacn.Certification.RenewalByCERPForNonMembersHeader" value="Renewal by Synergy CERP for Non-Members"></setting>
			<setting name="Aacn.Certification.NewForMembersHeader" value="AACN Members"></setting>
			<setting name="Aacn.Certification.NewForNonMembersHeader" value="Non-Members"></setting>

			<setting name="Aacn.Certification.VerifiedCertificationPageSize" value="10" />

			<setting name="Aacn.Store.DocsBaseUrl" value="__Aacn.Store.DocsBaseUrl__"/>
			<setting name="Aacn.DocumentRepo.FileDirectory" value="\\nfdocsprod.aacn.org\nfdocs\nfProd"/>

			<setting name="Aacn.Store.ProductThumbnailFormat" value="{0}/photos/{1}xx.gif" />
			<setting name="Aacn.Store.CategoryUrlFormat" value="{0}/{1}" />
			<setting name="Aacn.Store.ProductUrlFormat" value="{0}/{1}/{2}" />
			<setting name="Aacn.Store.EventUrlFormat" value="{0}/{1}/{2}" />
			<setting name="Aacn.Store.EventSessionUrlFormat" value="{0}/{1}/{2}/{3}/{4}" />

			<setting name="Aacn.Search.Stopwords" value="a,an,and,are,as,at,be,but,by,for,if,in,into,is,it,no,not,of,on,or,s,such,t,that,the,their,then,there,these,they,this,to,was,will,with" />

			<setting name="Aacn.Journals.ContextDatabase" value="master" />
			<setting name="Aacn.Journals.JournalsEvalCode" value="JOURNEVAL" />
			<setting name="JournalsFolder" value="$(dataFolder)/Journals" />
			<setting name="Aacn.Journals.JournalsAssetsVirtualFolder" value="/journalassets" />

			<setting name="Aacn.Certification.CertificationBaseTemplateId" value="{42662DCF-6108-44D5-9270-607BBBD46931}" />
			<setting name="Aacn.Certification.CertificationByExamTemplateId" value="{5F202B76-C820-4893-9875-598B164BCB7D}" />
			<setting name="Aacn.Certification.CertificationBySynergyTemplateId" value="{81FBB9E6-FD46-4E2C-A772-599BCBBC38A1}" />
			<setting name="Aacn.Certification.EnumValueTemplateId" value="{7B4E0D9C-A4B1-45A3-8DB9-AA78F40C5CD1}" />
			<setting name="Aacn.Certification.TagValueTemplateId" value="{A8C7119C-52FD-4FE9-8EC9-A96E15B5BB20}" />

			<setting name="Aacn.Certification.CertificationListId" value="{AE100DFA-DD77-4914-BA50-2B9DADCE1BBF}" />
			<setting name="Aacn.Certification.CertificationTypeListId" value="{A93E340B-59B8-42D5-AE8A-0CE314776724}" />
			<setting name="Aacn.Certification.PatientPopulationListId" value="{E13C9E54-CE11-46F6-9F37-162D71396224}" />
			<setting name="Aacn.Certification.ProgramTypeListId" value="{926E12EE-6A1A-4B5A-97F1-BD9D2E88D7A2}" />
			<setting name="Aacn.Certification.InitialApplicationId" value="{60E4C707-4A46-49B4-9870-1D78CB49F499}" />
			<setting name="Aacn.Certification.RenewApplicationId" value="{FEC278B0-1289-4E95-827C-3F336E1F347A}" />
			<setting name="Aacn.Certification.MembershipUpsellId" value="{65A079FA-DF97-47CA-82E0-6D21844E19C7}" />
			<setting name="Aacn.Certification.PromotionUpsellId" value="{5F4AD004-834B-4F1D-A67C-E7AAD80214C1}" />

			<setting name="Aacn.Store.DonationAmounts" value="{648C9B01-5909-4D8F-B72B-3C5B6ECDD8C6}" />

			<setting name="Aacn.Peak.DomainName" value="mini.aacn.org"/>

			<!-- pipe separated list of extensions-->
			<setting name="Aacn.DocumentRepo.AllowedExtensions" value="" />

			<setting name="Aacn.TermsRoot" value="{D50E615F-A268-4CA3-99CE-C088C5002ABC}" />
      <setting name="Aacn.ReadFromSummaryFieldTemplates" value="8dd2e098f7104e29aa25eb982d9edbb8,853e9b2433274f238c9c2fb52015b7b3,ef858ffcd96641699c861c9ef4668ab4" />
			<setting name="Aacn.TagTemplateId" value="{A8C7119C-52FD-4FE9-8EC9-A96E15B5BB20}" />
			<setting name="Aacn.Tag.AreaOfPractice" value="{794040A8-19C8-4291-87F2-3F6E8E3303F6}" />
			<setting name="Aacn.Tag.Level" value="{5877EF83-F6C7-49E0-805F-3600036BEDCC}" />
			<setting name="Aacn.Tag.Roles" value="{A6FA9710-0E0B-4EB5-9452-65381B9795A7}" />
			<setting name="Aacn.Tag.ClinicalSpecialty" value="{3F489787-7848-496D-ACBC-A59819145103}" />
			<setting name="Aacn.Tag.ProfessionalPractice" value="{921F0B1C-9DC2-42D6-86BC-167250B7C39B}" />
			<setting name="Aacn.Tag.PatientPopulation" value="{E13C9E54-CE11-46F6-9F37-162D71396224}" />
			<setting name="Aacn.Tag.ClinicalSystems" value="{36C1DC70-D07D-4470-AF14-0E33BA223E1E}" />

			<setting name="Aacn.ProfileImage.Default" value="/static/img/myaacn/_profile_thumb.png" />
			<setting name="Aacn.ListingModules.Seo.PageSize" value="500" />
		</settings>
		<encodeNameReplacements>
			<replace mode="on" find=" " replaceWith="-"/>
		</encodeNameReplacements>
		<pipelines>

			<initialize>
				<!--<processor patch:instead="processory[@type='Sitecore.Mvc.Pipelines.Loader.InitializeControllerFactory, Sitecore.Mvc']" type="Aacn.SitecoreExtensions.Pipelines.Initialize.InitializeNinjectControllerFactory, Aacn.SitecoreExtensions">
          <assemblies hint="list:AddAssembly">
            <assembly>Aacn.IocIntegration.dll</assembly>
            <assembly>Aacn.SecurityProvider.dll</assembly>
          </assemblies>
        </processor>-->
				<!-- pipeline for bundling-->
				<!--<processor patch:before="processor[@type='Sitecore.Mvc.Pipelines.Loader.InitializeGlobalFilters, Sitecore.Mvc']" type="Aacn.SitecoreExtensions.Pipelines.Initialize.RegisterPlatformBundles, Aacn.SitecoreExtensions" />-->
        <processor type="Aacn.SitecoreExtensions.Pipelines.Initialize.InitializeIgnition, Aacn.SitecoreExtensions" />
      </initialize>
			<renderField>
				<!--hot fix for link BeginField() MVC helper. Please review sitecore upgrades for an official fix.-->
				<processor patch:after="*[@type='Sitecore.Pipelines.RenderField.GetInternalLinkFieldValue, Sitecore.Kernel']" type="Aacn.SitecoreExtensions.Pipelines.RenderField.FixLinkFieldTitle, Aacn.SitecoreExtensions" />
				<processor type="Aacn.SitecoreExtensions.Pipelines.RenderField.FootnoteParser, Aacn.SitecoreExtensions" />
			</renderField>
			<mvc.requestBegin>
				<processor type="Sitecore.Mvc.Analytics.Pipelines.MvcEvents.RequestBegin.StartTracking, Sitecore.Mvc.Analytics">
					<patch:delete />
				</processor>
				<processor patch:after="*[@type='Sitecore.Mvc.Pipelines.Request.RequestBegin.SetupPageContext, Sitecore.Mvc']" type="Sitecore.Mvc.Analytics.Pipelines.MvcEvents.RequestBegin.StartTracking, Sitecore.Mvc.Analytics" />

			</mvc.requestBegin>
			<mvc.getRenderer>
				<processor type="Sitecore.Mvc.Pipelines.Response.GetRenderer.GetViewRenderer, Sitecore.Mvc">
					<patch:attribute name="type">Aacn.SitecoreExtensions.Pipelines.GetRenderers.GetExceptionSafeViewRenderer, Aacn.SitecoreExtensions</patch:attribute>
				</processor>
				<processor type="Sitecore.Mvc.Pipelines.Response.GetRenderer.GetControllerRenderer, Sitecore.Mvc">
					<patch:attribute name="type">Aacn.SitecoreExtensions.Pipelines.GetRenderers.GetExceptionSafeControllerRenderer, Aacn.SitecoreExtensions</patch:attribute>
				</processor>
			</mvc.getRenderer>
			<httpRequestBegin>
				<processor patch:after="*[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']" type="Aacn.SitecoreExtensions.Pipelines.HttpRequest.CustomLinkResolver, Aacn.SitecoreExtensions">
					<!-- List the sites this processor is active for. We don't want it operating on system and client calls -->
					<sites hint="list:AddSite">
						<aacn>aacn</aacn>
						<website>Website</website>
					</sites>
				</processor>
				<processor type="Aacn.SitecoreExtensions.Pipelines.HttpRequest.ExecuteRequest, Aacn.SitecoreExtensions" patch:instead="processor[@type='Sitecore.Pipelines.HttpRequest.ExecuteRequest, Sitecore.Kernel']"/>
			</httpRequestBegin>
			<getRenderingDatasource>
				<processor patch:after="*[@type='Sitecore.Pipelines.GetRenderingDatasource.GetDatasourceLocation, Sitecore.Kernel']"
						   type="Aacn.SitecoreExtensions.Pipelines.GetRenderingDatasource.GetDatasourceLocation, Aacn.SitecoreExtensions" />
			</getRenderingDatasource>
			<startAnalytics>
				<processor type="Sitecore.Analytics.Pipelines.StartAnalytics.CheckPreconditions, Sitecore.Analytics">
					<patch:attribute name="type">Aacn.SitecoreExtensions.Pipelines.StartAnalytics.CheckPreconditionsCustom, Aacn.SitecoreExtensions</patch:attribute>
				</processor>
			</startAnalytics>
		</pipelines>

		<linkManager>
			<providers>
				<add name="sitecore">
					<patch:attribute name="addAspxExtension">false</patch:attribute>
					<patch:attribute name="languageEmbedding">never</patch:attribute>
					<patch:attribute name="lowercaseUrls">true</patch:attribute>
					<patch:attribute name="alwaysIncludeServerUrl">false</patch:attribute>
					<patch:attribute name="siteResolving">true</patch:attribute>
				</add>
			</providers>
		</linkManager>
		<mediaLibrary>
			<mediaTypes>
				<mediaType name="SVG image" extensions="svg">
					<mimeType>image/svg+xml</mimeType>
					<forceDownload>false</forceDownload>
					<sharedTemplate>system/media/unversioned/image</sharedTemplate>
					<versionedTemplate>system/media/versioned/image</versionedTemplate>
					<mediaValidator type="Sitecore.Resources.Media.ImageValidator"/>
					<thumbnails>
						<generator type="Sitecore.Resources.Media.ImageThumbnailGenerator, Sitecore.Kernel">
							<extension>png</extension>
						</generator>
						<width>150</width>
						<height>150</height>
						<backgroundColor>#FFFFFF</backgroundColor>
					</thumbnails>
				</mediaType>
				<mediaType name="m4v video" extensions="m4v">
					<mimeType>video/mp4</mimeType>
					<forceDownload>false</forceDownload>
					<sharedTemplate>system/media/unversioned/Movie</sharedTemplate>
					<versionedTemplate>system/media/versioned/Movie</versionedTemplate>
				</mediaType>
				<mediaType name="PDF file" extensions="pdf">
					<forceDownload>
						<patch:delete/>
					</forceDownload>
					<forceDownload>false</forceDownload>
				</mediaType>
			</mediaTypes>
		</mediaLibrary>
		<events>
			<event name="item:added">
				<handler type="Aacn.SitecoreExtensions.Events.ItemEventHandler,Aacn.SitecoreExtensions" method="OnItemAdded" >
					<Database>master</Database>
				</handler>
			</event>
		</events>
		<watchers>
			<journals>
				<folder ref="settings/setting[@name='JournalsFolder']/@value" />
				<filter>*</filter>
			</journals>
		</watchers>
		<journalUpload>
			<watcher>
				<ignoreList>
					<ignore contains="icon16x16" />
					<ignore contains="icon32x32" />
					<ignore contains="icon48x48" />
					<ignore contains="_thumb" />
					<ignorepath contains=".svn" />
				</ignoreList>
			</watcher>
			<parser>
				<transforms>
					<transform name="CurrentJournalArticle" path="/Content/JournalCurrent.xslt" />
					<transform name="NamespaceRemoval" path="/Content/Namespace.xslt" />
				</transforms>
			</parser>
		</journalUpload>
	</sitecore>
</configuration>
