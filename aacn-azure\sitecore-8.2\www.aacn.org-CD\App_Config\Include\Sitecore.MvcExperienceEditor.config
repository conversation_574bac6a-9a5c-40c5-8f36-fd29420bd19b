﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <hasPresentation>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.HasPresentation.CheckController, Sitecore.Mvc.ExperienceEditor"/>
      </hasPresentation>
      
      <executePageEditorAction>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.ExecutePageEditorAction.TryGetMvcRendering, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Pipelines.ExecutePageEditorAction.TryGetXslContol, Sitecore.ExperienceEditor']"/>
      </executePageEditorAction>
     
      <mvc.customizeRendering>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.CustomizeRendering.SkipIfDesigning, Sitecore.Mvc.ExperienceEditor" patch:before="processor[1]" />
		<processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.CustomizeRendering.SelectVariation, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Mvc.Analytics.Pipelines.Response.CustomizeRendering.SelectVariation, Sitecore.Mvc.Analytics']" />        
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.CustomizeRendering.Personalize, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Mvc.Analytics.Pipelines.Response.CustomizeRendering.Personalize, Sitecore.Mvc.Analytics']"/>
      </mvc.customizeRendering>

      <mvc.getPageRendering>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.GetPageRendering.GetDesigningRendering, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Response.GetPageRendering.GetLayoutRendering, Sitecore.Mvc']" />
      </mvc.getPageRendering>

      <mvc.getXmlBasedLayoutDefinition>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.GetXmlBasedLayoutDefinition.GetPageDesigningLayout, Sitecore.Mvc.ExperienceEditor" patch:before="processor[1]"/>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.GetXmlBasedLayoutDefinition.SetLayoutContext, Sitecore.Mvc.ExperienceEditor" patch:after="processor[@type='Sitecore.Mvc.Pipelines.Response.GetXmlBasedLayoutDefinition.GetFromLayoutField, Sitecore.Mvc']"/>
      </mvc.getXmlBasedLayoutDefinition>

      <mvc.renderPageExtenders>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.RenderPageExtenders.RenderExtendersContainer, Sitecore.Mvc.ExperienceEditor"></processor>

        <!-- Uncomment the page extenders below and comment the "SPEAK-based" Experience Editor ribbon processors to switch to old SheerUI-based Experience Editor ribbon. -->
        <!-- The SheerUI-based Experience Editor ribbon.  -->
        <!-- 
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.RenderPageExtenders.RenderPageEditorExtender, Sitecore.Mvc.ExperienceEditor"></processor>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.RenderPageExtenders.RenderPreviewExtender, Sitecore.Mvc.ExperienceEditor"></processor>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.RenderPageExtenders.RenderDebugExtender, Sitecore.Mvc.ExperienceEditor"></processor>
        -->
        
        <!-- The SPEAK-based Experience Editor ribbon-->
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.RenderPageExtenders.SpeakRibbon.RenderPageEditorSpeakExtender, Sitecore.Mvc.ExperienceEditor"></processor>
      </mvc.renderPageExtenders>

      <mvc.renderPlaceholder>        
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.RenderPlaceholder.AddWrapper, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Response.RenderPlaceholder.PerformRendering, Sitecore.Mvc']"/>
      </mvc.renderPlaceholder>

      <mvc.renderRendering>        
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Response.RenderRendering.AddWrapper, Sitecore.Mvc.ExperienceEditor" patch:before="processor[@type='Sitecore.Mvc.Pipelines.Response.RenderRendering.ExecuteRenderer, Sitecore.Mvc']"/>        
      </mvc.renderRendering>
     
      <mvc.requestEnd>
        <processor type="Sitecore.Mvc.ExperienceEditor.Pipelines.Request.RequestEnd.AddPageExtenders, Sitecore.Mvc.ExperienceEditor"></processor>
      </mvc.requestEnd>     
    </pipelines>
  </sitecore>
</configuration>