﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures how the processing subsystem of the Sitecore Experience Database (xDB) aggregates data from the
collection database before it is stored in the reporting database for use by Sitecore reporting applications.

This file should always be enabled in case the current server needs to:
- Perform aggregation tasks (a processing server).
- Perform management tasks such as rebuilding the reporting database (a content management server).

If the current server does not need to have the aggregation subsystem enabled, you can rename this config file so that it has a ".disabled"
extension.

If this config file is enabled, the Sitecore.Analytics.Processing.Aggregation.ProcessingPools.config file must also be enabled.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>

    <aggregation>

      <aggregationContexts>

        <interaction>
          <live type="Sitecore.Analytics.Aggregation.Data.AggregationContext">
            <Pool ref="aggregationProcessing/processingPools/live" />
            <Source ref="aggregation/collectionData" />
            <ReportingTargets hint="list:AddReportingTarget">
              <primary ref="aggregation/reportingStorageProviders/primary" />
              <secondary ref="aggregation/reportingStorageProviders/secondary.live" />
            </ReportingTargets>
          </live>
          <history type="Sitecore.Analytics.Aggregation.Data.AggregationContext">
            <Pool ref="aggregationProcessing/processingPools/history" />
            <Source ref="aggregation/collectionData" />
            <ReportingTargets hint="list:AddReportingTarget">
              <secondary ref="aggregation/reportingStorageProviders/secondary.history" />
            </ReportingTargets>
          </history>
        </interaction>

        <automation>
          <live type="Sitecore.Analytics.Automation.Aggregation.Data.AutomationAggregationContext">
            <ReportingTargets hint="list:AddReportingTarget">
              <primary ref="aggregation/reportingStorageProviders/primary" />
              <secondary ref="aggregation/reportingStorageProviders/secondary.live" />
            </ReportingTargets>
          </live>
          <history type="Sitecore.Analytics.Automation.Aggregation.Data.AutomationAggregationContext">
            <ReportingTargets hint="list:AddReportingTarget">
              <secondary ref="aggregation/reportingStorageProviders/secondary.history" />
            </ReportingTargets>
          </history>
        </automation>
      
        <contactChangeNotification>
        <!-- Placeholder for contact change notification pipeline. -->
        </contactChangeNotification>

        <contact>
          <live type="Sitecore.Analytics.Aggregation.Data.Contact.ContactProcessingContext, Sitecore.Analytics.Aggregation">
            <Pool ref="aggregationProcessing/processingPools/contact" />
          </live>
        </contact>

      </aggregationContexts>

      <!-- Content Collection Data Provider: -->
      <collectionData type="Sitecore.Analytics.Aggregation.MongoDbCollectionDataProvider, Sitecore.Analytics.MongoDB" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
      </collectionData>

      <dateTimePrecisionStrategy type="Sitecore.Analytics.Aggregation.Data.DateTimeMinutePrecisionStrategy, Sitecore.Analytics.Aggregation" />

      <reportingStorageManager type="Sitecore.Analytics.Aggregation.History.ReportingStorageManager" singleInstance="true">
        <TimeToClearStorage>0.00:01:00</TimeToClearStorage>
        <ReportingTargetConfigPath>aggregation/reportingStorageProviders/secondary.history</ReportingTargetConfigPath>
        <HistoryAggregatorManagers hint="list:AddAggregator">
          <automation type="Sitecore.Analytics.Automation.Aggregation.Data.Processing.AutomationHistoryAggregatorManager" singleInstance="true">
            <TaskManager ref="processing/taskManager" />
          </automation>
          <interaction type="Sitecore.Analytics.Aggregation.History.InteractionHistoryAggregatorManager" singleInstance="true">
            <TaskManager ref="aggregation/historyTaskManager" />
          </interaction>
        </HistoryAggregatorManagers>
        <RebuildTaskDataProvider type="Sitecore.Analytics.Aggregation.History.MongoDbRebuildReportingTaskDataProvider" singleInstance="true">
          <param desc="connectionStringName">analytics</param>
        </RebuildTaskDataProvider>
      </reportingStorageManager>

      <!-- Reporting Storage Providers: -->
      <reportingStorageProviders>
        <primary type="Sitecore.Analytics.Aggregation.SqlReportingStorageProvider, Sitecore.Analytics.Sql" singleInstance="true">
          <param desc="connectionStringName">reporting</param>
          <TrailLength>7.00:00:00</TrailLength>
        </primary>
        <secondary.live type="Sitecore.Analytics.Aggregation.SqlReportingStorageProvider, Sitecore.Analytics.Sql" singleInstance="true">
          <param desc="connectionStringName">reporting.secondary</param>
          <param desc="cutoffBehavior">StoreOnlyAfterCutOffInclusive</param>
        </secondary.live>
        <secondary.history type="Sitecore.Analytics.Aggregation.SqlReportingStorageProvider, Sitecore.Analytics.Sql" singleInstance="true">
          <param desc="connectionStringName">reporting.secondary</param>
          <param desc="cutoffBehavior">StoreOnlyBeforeCutOffExclusive</param>
        </secondary.history>
      </reportingStorageProviders>
 
      <routines>
        
        <ExecRoutineStatementBuilder type="Sitecore.Analytics.Aggregation.Scripting.Builders.ExecRoutineStatementBuilder, Sitecore.Analytics.Sql" singleInstance="true">
          <mappings hint="list:AddMapping">

            <!-- Dimensions -->
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Assets</Table>
              <Routine>Ensure_Assets_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>BusinessUnits</Table>
              <Routine>Ensure_BusinessUnits_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>DeviceNames</Table>
              <Routine>Ensure_DeviceNames_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>FailureDetails</Table>
              <Routine>Ensure_FailureDetails_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Items</Table>
              <Routine>Ensure_Items_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Keywords</Table>
              <Routine>Ensure_Keywords_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>ReferringSites</Table>
              <Routine>Ensure_ReferringSites_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>SiteNames</Table>
              <Routine>Ensure_SiteNames_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Languages</Table>
              <Routine>Ensure_Languages_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
            </SqlMappingEntity>

            <!-- Mutable Dimensions -->
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Accounts</Table>
              <Routine>Upsert_Account</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Contacts</Table>
              <Routine>Upsert_Contact</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>TrafficTypes</Table>
              <Routine>Upsert_TrafficType</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>VisitorClassification</Table>
              <Routine>Upsert_VisitorClassification</Routine>
            </SqlMappingEntity>

            <!-- Facts -->
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_Conversions</Table>
              <Routine>Add_Conversions_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>Conversions_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_Downloads</Table>
              <Routine>Add_Downloads_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>Downloads_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_Failures</Table>
              <Routine>Add_Failures_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>Failures_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_FollowHits</Table>
              <Routine>Add_FollowHits_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>FollowHits_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_MvTesting</Table>
              <Routine>Add_MvTesting</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_TestStatistics</Table>
              <Routine>Add_TestStatistic</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_PageViews</Table>
              <Routine>Add_PageViews</Routine>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_SiteSearches</Table>
              <Routine>Add_SiteSearches_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>SiteSearches_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_SlowPages</Table>
              <Routine>Add_SlowPages_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>SlowPages_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_Traffic</Table>
              <Routine>Add_Traffic_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>Traffic_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_ValueBySource</Table>
              <Routine>Add_ValueBySource_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>ValueBySource_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_Visits</Table>
              <Routine>Add_Visits_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>Visits_Type</TableType>
            </SqlMappingEntity>
            <SqlMappingEntity type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_VisitsByBusinessContactLocation</Table>
              <Routine>Add_VisitsByBusinessContactLocation_Tvp</Routine>
              <IsMultiRow>True</IsMultiRow>
              <TableType>VisitsByBusinessContactLocation_Type</TableType>
            </SqlMappingEntity>

          </mappings>
        </ExecRoutineStatementBuilder>

        <TypeBasedStatementBuilder type="Sitecore.Analytics.Aggregation.Scripting.Builders.TypeBasedStatementBuilder, Sitecore.Analytics.Sql" singleInstance="true">
          <mappings hint="list:AddPrefix">
            <SqlRoutineTypeMap type="Sitecore.Analytics.Aggregation.SqlRoutineTypeMap, Sitecore.Analytics.Sql">
              <TableType>Sitecore.Analytics.Aggregation.Data.Model.Fact, Sitecore.Analytics.Aggregation</TableType>
              <RoutinePrefix>Add_</RoutinePrefix>
            </SqlRoutineTypeMap>
            <SqlRoutineTypeMap type="Sitecore.Analytics.Aggregation.SqlRoutineTypeMap, Sitecore.Analytics.Sql">
              <TableType>Sitecore.Analytics.Aggregation.Data.Model.Dimension, Sitecore.Analytics.Aggregation</TableType>
              <RoutinePrefix>Ensure_</RoutinePrefix>
            </SqlRoutineTypeMap>
          </mappings>
        </TypeBasedStatementBuilder>
        
      </routines>

    </aggregation>

    <pipelines>

      <group groupName="analytics.aggregation">
        <pipelines>

          <automationStates>
            <processor type="Sitecore.Analytics.Automation.Aggregation.Pipelines.AutomationStates.AutomationStateAggregationProcessor, Sitecore.Analytics.Automation.Aggregation" />
          </automationStates>

          <interactions>
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.TrafficProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.ValueBySourceProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.PageViewsProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.ValueByBusinessUnitProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.VisitsProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.FailuresProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.ConversionsProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.DownloadsProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.SiteSearchesProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.FollowHitsProcessor, Sitecore.Analytics.Aggregation" />
            <processor type="Sitecore.Analytics.Aggregation.Pipeline.SlowPagesProcessor, Sitecore.Analytics.Aggregation" />
          </interactions>

          <contacts>
          </contacts>

        </pipelines>
      </group>

    </pipelines>

  </sitecore>
</configuration>