<!--

Purpose: This include file changes the "dataFolder" setting

To enable this, rename this file so that it has a ".config" extension

Notice how "patch:attribute" is used to change the value of attributes that 
are specified for an existing element in the web.config file

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <sc.variable name="dataFolder">
      <patch:attribute name="value">c:\sites\SC_AACN\Data</patch:attribute>
    </sc.variable>
  </sitecore>
</configuration>