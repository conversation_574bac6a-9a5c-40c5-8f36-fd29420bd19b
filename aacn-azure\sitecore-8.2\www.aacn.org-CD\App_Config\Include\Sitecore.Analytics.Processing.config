﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures the processing subsystem of the Sitecore Experience Database (xDB). Most importantly, it defines the
API that you use to register tasks in the processing Task Manager, regardless of whether this server is configured to perform processing
tasks or not. For more information about using the Task Manager, see the Processing API section in the xDB API Reference Guide.

In most cases, you should leave this file enabled. If the current server does not need to perform processing tasks, you should disable the
Sitecore.Analytics.Processing.Services.config file instead. 

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>

      <copyAutomationStates>
        <processor type="Sitecore.Analytics.Automation.Pipelines.CopyAutomationStates.CopyAutomationStates, Sitecore.Analytics.Automation" >
          <TaskManager ref="processing/taskManager"/>
        </processor>
      </copyAutomationStates>
      
      <updateAutomationStates>
        <processor type="Sitecore.Analytics.Automation.Pipelines.UpdateAutomationStates.UpdateAutomationStates, Sitecore.Analytics.Automation" >
          <TaskManager ref="processing/taskManager"/>
        </processor>
      </updateAutomationStates>

    </pipelines>

    <processing>

      <processingPoolFactory type="Sitecore.Analytics.Data.Processing.MongoDbProcessingPoolFactory" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
      </processingPoolFactory>

      <!-- Obsolete: this node is obsolete and will be removed in a future version -->
      <rangeMapFactory type="Sitecore.Analytics.Processing.Tasks.MongoDbRangeMapPool" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
      </rangeMapFactory>

      <rangeMaps type="Sitecore.Analytics.Processing.Tasks.MongoDbRangeMapPool2" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
      </rangeMaps>

      <sequenceFactory type="Sitecore.Analytics.Processing.Tasks.MongoDbSequenceFactory2" singleInstance="true">
        <param desc="connectionStringName">analytics</param>
      </sequenceFactory>

      <taskAgent type="Sitecore.Analytics.Processing.TasksAgent">
        <TaskManager ref="processing/taskManager"/>
      </taskAgent>
      
      <taskManager type="Sitecore.Analytics.Processing.TaskManager" singleInstance="true">
        <Queue type="Sitecore.Analytics.Processing.TaskQueue">
          <TaskStorage type="Sitecore.Analytics.Processing.MongoDbTaskStorageProvider">
            <param desc="connectionStringName">tracking.live</param>
            <param desc="collectionName">TaskQueue</param>
          </TaskStorage>
          <!--
            LOCK TIMEOUT
            Specifies the time after which a lock placed on a task will expire.
            This setting applies only to "deferred action" tasks executed by the framework.
            Default: 0.00:00:03
          -->
          <LockTimeout>0.00:00:03</LockTimeout>
        </Queue>
        <SequenceFactory ref="processing/sequenceFactory" />
        <RangeMapFactory ref="processing/rangeMapFactory" />
        <RangeMaps ref="processing/rangeMaps" />
        <ProcessingPoolFactory ref="processing/processingPoolFactory" />
        <!-- MAINTENANCE INTERVAL
          Specifies the minimum time interval between executions of maintenance operations on the task queue.
          During maintenance operations task manager will remove and cleanup entries of the tasks older than "task entry afterlife".
          
          Default: 01:00:00
        -->
        <MaintenanceInterval>01:00:00</MaintenanceInterval>
        
        <!-- TASK ENTRY AFTERLIFE
          Specifies the minimum time a task entry (and all associated data structures) is kept in the task queue after 
          the task has executed.
          
          Default: 8.00:00:00
        -->
        <TaskEntryAfterLife>8.00:00:00</TaskEntryAfterLife>

        <!-- LOCK TIMEOUT SECONDS
          This setting specifies the time interval in seconds that a work unit (range) is locked and allocated to a specific worker thread.
          When the time interval has expired, the owner of the lock is considered to be "hung", and another worker thread can pick up the 
          same work unit (range).
          If you set this value too high, it may take more time for the system to recover from random errors, such as network jams or server restarts.
          If you set this value too low, it can cause system instability, as worker threads may try to steal work from each other.
          Default value: 15
        -->
        <SchedulerSettings.LockTimeoutSeconds>15</SchedulerSettings.LockTimeoutSeconds>

        <!-- MAXIMUM BATCH SIZE
          This setting specifies the maximum number of work items that a worker thread can load from the xDB and pass to an application-specific handler 
          in a single iteration.
          If you set this value too low, it can cause excessive amounts of network traffic and may have a significant impact 
          on system throughput.
          If you set this value too high, it may affect tasks that require communication with SQL Server. For example, it may affect the size of transactions
          sent to SQL Server and this might require you to set higher 'lock timeout' values as it takes longer to process large batches.
          Default value: 128
        -->
        <SchedulerSettings.MaximumBatchSize>128</SchedulerSettings.MaximumBatchSize>

        <!-- MINIMUM RANGE SIZE
          This setting specifies the estimated number of work items that are in the processing range when the current working thread stops sharing work with 
          other threads. The scheduler uses this value as the threshold to decide if a worker thread is close to the end 
          of a work unit (range) and whether it should complete the current work unit or spend time synchronizing other worker threads.
          Default: 100
        -->
        <SchedulerSettings.MinimumRangeSize>100</SchedulerSettings.MinimumRangeSize>
      </taskManager>
      
    </processing>
  </sitecore>
</configuration>