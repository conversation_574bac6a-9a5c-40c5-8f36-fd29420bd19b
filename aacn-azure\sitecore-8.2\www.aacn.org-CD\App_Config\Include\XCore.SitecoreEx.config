<!--

Purpose: This include file changes the "dataFolder" setting

To enable this, rename this file so that it has a ".config" extension

Notice how "patch:attribute" is used to change the value of attributes that 
are specified for an existing element in the web.config file

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- settings-->
    <settings>
      <!-- default from email address for workflows
      Default: <EMAIL>
      -->
      <setting name="XDefaultFromEmail" value="<EMAIL>"/>
      <!--  EMAIL FIELD
            Defines the User Profile field for the Visitor Tag called "Email"
            Default: Email
      -->
      <setting name="EmailField" value="Email" />
      <!--  FIRST NAME FIELD
            Defines the User Profile field for the Visitor Tag called "First Name".
            Default: FullName
      -->
      <setting name="FirstNameField" value="FullName" />
      <!--  SECOND NAME FIELD
            Defines the User Profile field for the Visitor Tag called "Second Name"
            Default: {blank}
      -->
      <setting name="SecondNameField" value="" />
      <setting name="XCoreObjectCacheSize" value="25MB"/>
      <setting name="BROWSER_TITLE_VALIDATION" value=""/>
      <setting name="NoAccessUrl">
        <patch:attribute name="value">/access-denied</patch:attribute>
      </setting>
    </settings>
    <!-- custom commands-->
    <commands>
      <command name="xcore:copyitemshortidtoclipboard" type="XCore.SitecoreExtensions.Commands.CopyItemShortIDToClipboard, XCore.SitecoreExtensions"/>
      <command name="xcore:copytemplateshortidtoclipboard" type="XCore.SitecoreExtensions.Commands.CopyTemplateShortIDToClipboard, XCore.SitecoreExtensions"/>
      <command name="xcore:editsitecorefields" type="XCore.SitecoreExtensions.Commands.EditSitecoreFields, XCore.SitecoreExtensions"/>
    </commands>
    <!-- pipeline steps-->
    <pipelines>
      <!-- Analytics -->
      <!--<startTracking>
        <processor type="XCore.SitecoreExtensions.Pipelines.Analytics.StartTracking.AddVisitorIdentityTag,XCore.SitecoreExtensions" patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.UpdateGeoIpData,Sitecore.Analytics']" />
      </startTracking>-->

      <renderField>
        <!--pipeline step to add titles to images-->
        <processor patch:before="*[@type='Sitecore.Pipelines.RenderField.AddBeforeAndAfterValues, Sitecore.Kernel']" type="XCore.SitecoreExtensions.Pipelines.AddImageTitles, XCore.SitecoreExtensions" />
      </renderField>
      <httpRequestBegin>
        <processor patch:before="*[@type='Sitecore.Pipelines.HttpRequest.ExecuteRequest, Sitecore.Kernel']" type="XCore.SitecoreExtensions.Pipelines.SaveQueryString, XCore.SitecoreExtensions" />
        <processor patch:before="*[@type='Sitecore.Pipelines.HttpRequest.ExecuteRequest, Sitecore.Kernel']" type="Aacn.SitecoreExtensions.Pipelines.HttpRequest.AccessDenied, Aacn.SitecoreExtensions" />
        <processor patch:before="*[@type='Sitecore.Pipelines.HttpRequest.ExecuteRequest, Sitecore.Kernel']" type="Aacn.SitecoreExtensions.Pipelines.HttpRequest.AjaxRequestProcessor, Aacn.SitecoreExtensions" />
      </httpRequestBegin>
    </pipelines>
    <databases>
      <database id="core" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <cacheSizes hint="setting">
          <patch:delete />
        </cacheSizes>
        <cacheSizes hint="setting">
          <data>300MB</data>
          <items>300MB</items>
          <paths>2500KB</paths>
          <itempaths>100MB</itempaths>
          <standardValues>2500KB</standardValues>
        </cacheSizes>
      </database>
      <database id="master" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <cacheSizes hint="setting">
          <patch:delete />
        </cacheSizes>
        <cacheSizes hint="setting">
          <data>300MB</data>
          <items>300MB</items>
          <paths>2500KB</paths>
          <itempaths>100MB</itempaths>
          <standardValues>2500KB</standardValues>
        </cacheSizes>
      </database>
      <database id="web" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
        <cacheSizes hint="setting">
          <patch:delete />
        </cacheSizes>
        <cacheSizes hint="setting">
          <data>500MB</data>
          <items>500MB</items>
          <paths>10MB</paths>
          <itempaths>50MB</itempaths>
          <standardValues>10MB</standardValues>
        </cacheSizes>
      </database>
    </databases>  
  </sitecore>
</configuration>