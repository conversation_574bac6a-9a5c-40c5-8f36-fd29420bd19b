﻿<!--

Purpose: This include file needs to be enabled in Content Management and Reporting Service Environments.

It extends the default implementation of IDefinitionsStorageProvider with ability to support custom deployable definitions.

-->
<configuration xmlns:x="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <aggregation>
      <!-- Patching the default reportingStorageProviders to be re-routed to the custom IDefinitionsStorageProvider defined in experienceAnalytics section below.-->
      <reportingStorageProviders>
        <primary>
          <storageProviders hint="list:AddCustomProviders">
            <segments ref="experienceAnalytics/primarySegmentStorageProvider" />
          </storageProviders>
        </primary>
        <secondary.live>
          <storageProviders hint="list:AddCustomProviders">
            <segments ref="experienceAnalytics/secondarySegmentStorageProvider" />
          </storageProviders>
        </secondary.live>
        <secondary.history>
          <storageProviders hint="list:AddCustomProviders">
            <segments ref="experienceAnalytics/secondarySegmentStorageProvider" />
          </storageProviders>
        </secondary.history>
      </reportingStorageProviders>
    </aggregation>
    <!-- IDefinitionsStorageProvider implementation that supports custom deployable definitions.-->
    <experienceAnalytics>
      <primarySegmentStorageProvider type="Sitecore.ExperienceAnalytics.Client.Platform.SegmentStorageProvider, Sitecore.ExperienceAnalytics.Client" singleInstance="true">
        <param desc="connectionStringName">reporting</param>
      </primarySegmentStorageProvider>
      <secondarySegmentStorageProvider type="Sitecore.ExperienceAnalytics.Client.Platform.SegmentStorageProvider, Sitecore.ExperienceAnalytics.Client" singleInstance="true">
        <param desc="connectionStringName">reporting.secondary</param>
      </secondarySegmentStorageProvider>
    </experienceAnalytics>
  </sitecore>
</configuration>