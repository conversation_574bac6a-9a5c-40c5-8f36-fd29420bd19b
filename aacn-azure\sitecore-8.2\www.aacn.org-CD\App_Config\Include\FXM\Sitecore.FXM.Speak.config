﻿<?xml version="1.0"?>
<!--

Purpose
Configures the customizations required to the Speak framework to allow the FXM UI to work.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <pipelines>
      <getExperienceEditorRibbon>
        <processor patch:before="*[1]"
                   type="Sitecore.FXM.Speak.Ribbon.GetExperienceEditorRibbon.AddFxmEditRibbon, Sitecore.FXM.Speak" />
      </getExperienceEditorRibbon>
    </pipelines>

    <sitecore.experienceeditor.speak.requests>
      <!-- FXM Rules -->
      <request name="ExperienceEditor.FXM.GetRulesEditorUrl" type="Sitecore.FXM.Speak.Ribbon.Requests.Rules.GetRulesEditorUrl, Sitecore.FXM.Speak" />

      <!-- FXM Analytics -->
      <request name="ExperienceEditor.FXM.GetGoalsEditorUrl" type="Sitecore.FXM.Speak.Ribbon.Requests.Analytics.GetGoalsEditorUrl, Sitecore.FXM.Speak" />
      <request name="ExperienceEditor.FXM.GetAttributesEditorUrl" type="Sitecore.FXM.Speak.Ribbon.Requests.Analytics.GetAttributesEditorUrl, Sitecore.FXM.Speak" />

      <!-- FXM Matchers -->
      <request name="ExperienceEditor.FXM.GetClientActionsData" type="Sitecore.FXM.Speak.Ribbon.Requests.Matchers.GetClientActionsData, Sitecore.FXM.Speak" />
      <request name="ExperienceEditor.FXM.GetElementReplacersData" type="Sitecore.FXM.Speak.Ribbon.Requests.Matchers.GetElementReplacersData, Sitecore.FXM.Speak" />
    </sitecore.experienceeditor.speak.requests>

    <pipelines>
      <speak.client.resolveScript>
        <processor type="Sitecore.Resources.Pipelines.ResolveScript.Controls, Sitecore.Speak.Client">
          <sources hint="raw:AddSource">
            <source folder="/sitecore/shell/client/Applications/FXM/" deep="true" category="FXM" pattern="*.js,*.css" />
          </sources>
        </processor>
      </speak.client.resolveScript>
    </pipelines>
  </sitecore>
</configuration>
