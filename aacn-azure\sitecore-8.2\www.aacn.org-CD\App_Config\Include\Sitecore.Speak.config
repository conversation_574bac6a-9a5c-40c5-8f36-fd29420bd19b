﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!--  SPEAK COMPONENT KNOCKOUT PRESENTER
            The name of the Knockout Presenter.
            Default: scKoPresenter
      -->
      <setting name="Speak.Components.KnockoutPresenter" value="scKoPresenter"/>

      <!--  SPEAK COMPONENT SPEAK PRESENTER
            The name of the SPEAK Presenter.
            Default: scSpeakPresenter
      -->
      <setting name="Speak.Components.SpeakPresenter" value="scSpeakPresenter"/>

      <!--  SPEAK HTML MINIFY SCRIPTS
            Indicates whether scripts should be bundled and minified.
            Default: false
      -->
      <setting name="Speak.Html.MinifyScripts" value="false"/>
      
      <!--  SPEAK HTML MINIFY STYLESHEETS
            Indicates whether stylesheets should be bundled and minified.
            Default: false
      -->
      <setting name="Speak.Html.MinifyStylesheets" value="false"/>
 
      <!--  SPEAK HTML REQUIRE JS CUSTOM HANDLER
            Specifies the prefix that should trigger the HTTP request customer handler (see the customHandlers section in the web.config file).
            Default: /-/speak/v1/
      -->
      <setting name="Speak.Html.RequireJsCustomHandler" value="/-/speak/v1/"/>

      <!--  SPEAK HTML REQUIRE JS MAIN FILE
            Specifies the path of the main.js version SPEAK 1.2 file which configures Require.js.
            Default: /-/speak/v1/assets/main-1.2.js
      -->
      <setting name="Speak.Html.RequireJsMainFile" value="/-/speak/v1/assets/main-1.2.js"/>

      <!--  SPEAK HTML REQUIRE JS MAIN FILE
            Specifies the path of the minfied SPEAK version for 1.2 of main.js file which configures Require.js.
            Default: /-/speak/v1/assets/main-1.2.min.js
      -->
      <setting name="Speak.Html.RequireJsMainFileMinified" value="/-/speak/v1/assets/main-1.2.min.js"/>

      <!--  SPEAK BINDING JS FILE
            Specifies the path of the binding plugin required by SPEAK to apply bindings.
            Default: /sitecore/shell/client/Speak/Assets/lib/core/1.2/deps/scBindingPlugin.js
      -->
      <setting name="Speak.Html.BindingPluginFile" value="/sitecore/shell/client/Speak/Assets/lib/core/1.2/deps/scBindingPlugin.js"/>

      <!--  SPEAK BINDING JS FILE MINIFIED
            Specifies the path of the binding plugin minified required by SPEAK to apply bindings.
            Default: /sitecore/shell/client/Speak/Assets/lib/core/1.2/deps/scBindingPlugin.min.js
      -->
      <setting name="Speak.Html.BindingPluginFileMinified" value="/sitecore/shell/client/Speak/Assets/lib/core/1.2/deps/scBindingPlugin.min.js"/>
      
      <!--  THE MAIN FILE for the CMS 7.1
            Specifies the path of the main.js file which configures Require.js.
            Default: /-/speak/v1/assets/main.js
      -->
      <setting name="Speak.Html.RequireJSBackwardCompatibilityFile" value="/-/speak/v1/assets/main.js"/>

      <!--  SPEAK REQUIRE WAIT SECONDS
            Overrides the Require.js waitSeconds setting
            Default: 30
      -->
      <setting name="Speak.Html.RequireJsWaitSeconds" value="30"/>

      <!--  SPEAK HTTPCACHING ETAGENABLED
            Determines if the Http Cache Header If-None-Match should be checked.
            If the value is true, the etag is set to every item and file touched
            during the request.
            Default: false
      -->
      <setting name="Speak.HttpCaching.ETagEnabled" value="false"/>
      
      <!--  SPEAK HTTPCACHING SETMAXAGE
            Determines if the Http Cache Header SetMaxAge should be set.
            If the value is true, the header is set to 0 seconds, making the
            browser request the file from the server every time. If value is 
            false, the file is retrieved from the browser cache.
            Default: true
      -->
      <setting name="Speak.HttpCaching.SetMaxAge" value="true"/>
      
      <!--  IS PRODUCTION MODE
            Indicates whether the system is in production mode or not.
            Default: false
      -->      
      <setting name="IsProductionMode" value="false" />

      <!--  MASTER RENDERING ID
            The item ID of the Master rendering, which can be expanded.
            Default: {5AA4AD6B-1228-45C1-B4C3-B1847AE60FA2}
      -->      
      <setting name="Speak.Mvc.MasterRenderingID" value="{3DC89CCA-D94A-4C46-A16D-AF6D0CB7E721}" />

      <!--  LaunchPad ItemId  
            The LaunchPad item ID for the GobalLogo usage.
            Default: {6B846FBD-8549-4C91-AE6B-18286EFE82D2}
      -->      
      <setting name="Speak.GobalLogo.LaunchPadId" value="{6B846FBD-8549-4C91-AE6B-18286EFE82D2}" />
    </settings>

    <customHandlers>
      <handler trigger="/-/speak/v1/" handler="sitecore_speak.ashx" />
    </customHandlers>
    
    <pipelines>
      <!-- Initialize -->
      <!-- Enable this processor to precompile MVC views. This allows SPEAK applications
           to start up faster by precompiling the views at start up (in a separate thread).
           Paths: Pipe (|) separated list of folders to precompile (recursively).
      -->
      <initialize>
        <processor type="Sitecore.Pipelines.Initialize.PrecompileSpeakViews, Sitecore.Speak.Client">
          <Paths>/sitecore/shell/client/Business Component Library</Paths>
        </processor>
      </initialize>
      
      <!-- Http Request -->
      <preprocessRequest>
        <processor type="Sitecore.Pipelines.HttpRequest.ResolveClientPath, Sitecore.Speak.Client" />
      </preprocessRequest>

      <speak.client.addBinding>
        <processor type="Sitecore.Web.Pipelines.AddBindings.AddConverterBinding, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.AddBindings.AddSimpleBinding, Sitecore.Speak.Client" />
      </speak.client.addBinding>

      <speak.client.getClick>
        <processor type="Sitecore.Web.Pipelines.GetClick.HandleClick, Sitecore.Speak.Client" />
      </speak.client.getClick>

      <speak.client.getControlId>
        <processor type="Sitecore.Web.Pipelines.GetControlId.HandleEmptyControlId, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetControlId.HandleInvalidId, Sitecore.Speak.Client" />
      </speak.client.getControlId>

      <speak.client.getOverlays>
        <processor type="Sitecore.Web.Pipelines.GetOverlays.GetWebConfigOverlays, Sitecore.Speak.Client">
          <!--
		      Example of how to add <overlay> definitions:
          <overlays hint="raw:AddOverlay">
            <overlay url="/sitecore/shell/client/Speak/Assets/Overlay.js" />
          </overlays>
          -->
        </processor>
      </speak.client.getOverlays>

      <speak.client.getPageScripts>
        <processor type="Sitecore.Web.Pipelines.GetPageScripts.GetFileNames, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageScripts.Minify, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageScripts.BuildScripts, Sitecore.Speak.Client" />
      </speak.client.getPageScripts>

      <speak.client.getPageStylesheets>
        <processor type="Sitecore.Web.Pipelines.GetPageStylesheets.GetFileNames, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageStylesheets.GetTheme, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageStylesheets.GetSubthemes, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageStylesheets.Minify, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetPageStylesheets.BuildStylesheets, Sitecore.Speak.Client" />
      </speak.client.getPageStylesheets>

      <speak.client.getStyle>
        <processor type="Sitecore.Web.Pipelines.GetStyle.Margin, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetStyle.Padding, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetStyle.Border, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.GetStyle.IsVisible, Sitecore.Speak.Client" />
      </speak.client.getStyle>

      <speak.client.initialize.layout>
        <processor type="Sitecore.Web.Pipelines.InitializeSpeakLayout.CheckUserAccess, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.InitializeSpeakLayout.SetDisplayMode, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.InitializeSpeakLayout.SetAntiForgeryToken, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.InitializeSpeakLayout.DisableAnalytics, Sitecore.Speak.Client" />
      </speak.client.initialize.layout>

      <speak.client.parseRenderingParameter>
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseDataBinding, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseContextItemValue, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseFormValue, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseQueryStringValue, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseSessionValue, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParseAppModelValue, Sitecore.Speak.Client" />
        <processor type="Sitecore.Web.Pipelines.SetAttribute.ParsePageModelValue, Sitecore.Speak.Client" />
      </speak.client.parseRenderingParameter>

      <speak.client.resolveRequire>
        <processor type="Sitecore.Web.Pipelines.ResolveRequire.CustomHandler, Sitecore.Speak.Client" />
      </speak.client.resolveRequire>
      
      <speak.logout argsType="Sitecore.Pipelines.Logout.LogoutArgs">
        <processor mode="on" type="Sitecore.Pipelines.Logout.ClearCache, Sitecore.Kernel"/>        
        <processor mode="on" type="Sitecore.Pipelines.Logout.ClearSession, Sitecore.Kernel"/>
        <processor mode="on" type="Sitecore.Pipelines.Logout.RemoveTicket, Sitecore.Kernel"/>        
      </speak.logout>
      
      <mvc.buildPageDefinition>
        <processor type="Sitecore.Mvc.Pipelines.Response.BuildPageDefinition.ProcessMasterRendering, Sitecore.Speak.Client"/>
      </mvc.buildPageDefinition>
    </pipelines>

    <speak>

    </speak>
    
  </sitecore>
</configuration>