<!--
	Unicorn Data Provider Configuration

	This file configures the Unicorn data provider. The data provider writes updated serialized items to disk when they are changed.

	This file should be removed in ANY deployed instance (CE or CD) that does not act as a source for serialized item updates.
	Generally speaking that's anywhere other than a developer workstation, so your CI process (you have one, right?) should remove this file during the build.
	IMPORTANT EXCEPTION: If you are using Transparent Sync as a deployment mechanism, this file must remain on your CE environment.

	http://github.com/kamsar/Unicorn
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<dataProviders>
			<!--
				Register the Unicorn data provider for use. If a database hooks to the Unicorn data provider it will
				automatically write changes to the database that match any configured predicate into the serialization provider.

				Changes that only affect Revision, Modified, or any fields ignored by FieldPredicates will be ignored.
			-->
		</dataProviders>

		<!--
			Hook the Unicorn Data Provider into the master and core databases. If you're not
			syncing anything in core you can safely unregister it from here. If you want to
			sync something to another database register	it here.

			It's safe to remove this config section on any environment where you are not
			collecting item changes, which may mean anywhere other than local development
			sites. This will avoid any performance hit from writing unused serialized files.
		-->
	</sitecore>
</configuration>
