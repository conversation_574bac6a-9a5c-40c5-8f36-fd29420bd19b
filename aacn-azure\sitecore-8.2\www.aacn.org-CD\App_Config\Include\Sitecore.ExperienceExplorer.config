﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file configures a work of Sitecore Experience Explorer.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
        
      <!--  EXPERIENCE EXPLORER PIPELINE IS ENABLED
            Global setting for enabling Experience Explorer on server
            Is useful in multi-server environment
            Default value: true
      -->
      <setting name="ExperienceModePipeline.Enabled" value="True" />
      
      <!--  EXPERIENCE EXPLORER QUERY STRING KEY
            Specifies the key for enabling the experience explorer on the query string.
            Default value: sc_expview
      -->
      <setting name="ExperienceExplorer.AddOnQueryStringKey" value="sc_expview" />

      <!--  EXPERIENCE EXPLORER CONTEXT DEVICE QUALIFIER
            The query string key for passing the id of current Sitecore Device 
            Default value: sc_exp_deviceid
            Note: this setting has been deprecated. It will be removed in the next product version
      -->
      <setting name="ExperienceExplorer.ContextDeviceIdQueryStringKey" value="sc_exp_deviceid" />
      
      <!--  EXPERIENCE EXPLORER CONTEXT ITEM QUALIFIER
            The query string key for passing the id of the current Sitecore Context Item 
            Default value: sc_exp_itemid
      -->                                                                                                           
      <setting name="ExperienceExplorer.ContextItemIdQueryStringKey" value="sc_exp_itemid" />

	        <!--  EXPERIENCE EXPLORER TESTING IP
            The IP used for testing MAXMIND Connectivity
            Default value: ***************
      -->
      <setting name="ExperienceExplorer.GeoTestingIp" value="***************" />

      <!--  MODEL POPULATIAON THRESHOLD
            Defines the threshold value (in milliseconds) for logging population render operations.
            Default value: 100
      -->
      <setting name="ExperienceExplorer.ModelPopulationThreshold" value="100" />

      <!--  EXPERIENCE EXPLORER QUERY STRING KEY
            Specifies the key for enabling the experience explorer on the query string.
            Default: sc_expview
      -->
      <setting name="ExperienceExplorer.CurrentWebsiteStringKey" value="sc_pagesite" />

      <!--  EXPERIENCE EXPLORER PREVIEW DATE QUALIFIER
            The query string key for passing the preset for the of current Sitecore session in GUID or name
            Allows preview via query string: ?sc_preset={0B56B799-14E8-4EF3-BE06-50F093434E39} or ?sc_preset=cecile
            
            Default value: sc_preset
            Note: this setting has been deprecated. It will be removed in the next product version
      -->
      <setting name="ExperienceExplorer.PresetQueryStringKey" value="sc_preset" />

      <!--  EXPERIENCE EXPLORER PREVIEW DATE QUALIFIER
            The query string key for passing the preview date for the of current Sitecore session in YYYY-MM-DD
            Allows preview via query string: ?sc_previewdate=2013-03-05 
            
            Default value: sc_previewdate
      -->
      <setting name="ExperienceExplorer.PreviewDateQueryStringKey" value="sc_previewdate" />

      <!--  EXPERIENCE EXPLORER VISITOR ID QUALIFIER
            The query string key for previewing the Sitecore session of a specific visitor
            
            Default value: sc_visitorid
      -->
      <setting name="ExperienceExplorer.VisitorIdQueryStringKey" value="sc_visitorid" />
    </settings>

    <pipelines>
      <mvc.renderPageExtenders>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.Mvc.RenderPageExtenders.InjectExperienceExplorerControl, Sitecore.ExperienceExplorer.Business"></processor>
      </mvc.renderPageExtenders>
      <getItemPersonalizationVisibility>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.GetItemPersonalizationVisibility.ResolveExplorerPresetsRoot, Sitecore.ExperienceExplorer.Business"
           patch:after="processor[@type='Sitecore.Pipelines.GetItemPersonalizationVisibility.CheckSectionAvailability, Sitecore.Kernel']" />        
      </getItemPersonalizationVisibility>
      <commitSession>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.CommitSession.PreventSessionCommitPipeline, Sitecore.ExperienceExplorer.Business" 
                   patch:before="processor[@type='Sitecore.Analytics.Pipelines.CommitSession.CheckPreconditions, Sitecore.Analytics']" />
      </commitSession>
      <startTracking>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.JourneyPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.RaiseStartTracking, Sitecore.Analytics']" />
        
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.GeoIpPipeline, Sitecore.ExperienceExplorer.Business" 
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.UpdateGeoIpData, Sitecore.Analytics']" />
        
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.GoalPipeline, Sitecore.ExperienceExplorer.Business" 
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.ProcessItem, Sitecore.Analytics']" />

        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.PageEventPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.ProcessItem, Sitecore.Analytics']" />

        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.TagsPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.ProcessItem, Sitecore.Analytics']" />
        
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.CampaignPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.StartTracking.ProcessItem, Sitecore.Analytics']" />
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.StartTracking.ReferralPipeline, Sitecore.ExperienceExplorer.Business"></processor>
      </startTracking>
      <processItem>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.ProcessItem.InteractionValuePipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Analytics.Pipelines.ProcessItem.RegisterPageEvents,Sitecore.Analytics']"></processor>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.ProcessItem.SaveInitialProfilesPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Analytics.Pipelines.ProcessItem.ProcessProfiles, Sitecore.Analytics']"></processor>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.ProcessItem.ProfilesPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Analytics.Pipelines.ProcessItem.ProcessProfiles, Sitecore.Analytics']"></processor>
      </processItem>
      <httpRequestBegin>    
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.EnableExperienceModePipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.DatabaseResolver, Sitecore.Kernel']" />
        
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.DevicePipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.DeviceResolver, Sitecore.Kernel']" />

        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.PreviewResolverPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']" />
        
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.PresetResolverPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.DevicePipeline, Sitecore.ExperienceExplorer.Business']" />
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.ContextItemPipeline, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']"></processor>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequest.ExecuteRequest, Sitecore.ExperienceExplorer.Business"
                   patch:before="processor[@type='Sitecore.Pipelines.HttpRequest.ExecuteRequest, Sitecore.Kernel']"></processor>
      </httpRequestBegin>
      <getContentEditorWarnings>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.ContentEditorWarnings.GetMultiVariantWarnings, Sitecore.ExperienceExplorer.Business"
                   patch:after="processor[@type='Sitecore.Pipelines.GetContentEditorWarnings.Notifications, Sitecore.Kernel']" />
      </getContentEditorWarnings>
      <renderLayout>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.RenderLayout.InjectExperienceExplorerControlPipeline, Sitecore.ExperienceExplorer.Business" />
      </renderLayout>
      <getPageEditorNotifications>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.GetPageEditorNotifications.GetExperienceExplorerNotification, Sitecore.ExperienceExplorer.Business" />
      </getPageEditorNotifications>
      <httpRequestEnd>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequestProcessed.LoginVirtualUser, Sitecore.ExperienceExplorer.Business" 
                   patch:before="processor[@type='Sitecore.Pipelines.PreprocessRequest.CheckIgnoreFlag, Sitecore.Kernel']" />
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequestProcessed.RetrieveRenderingsPipeline, Sitecore.ExperienceExplorer.Business"></processor>
        <processor type="Sitecore.ExperienceExplorer.Business.Pipelines.HttpRequestProcessed.SetModeChangedMarker, Sitecore.ExperienceExplorer.Business"></processor>
      </httpRequestEnd>
    </pipelines>

    <commands>
      <command name="experienceexplorer:expview" type="Sitecore.ExperienceExplorer.Business.Commands.ExperienceExplorerWebEditCommand, Sitecore.ExperienceExplorer.Business" />
    </commands>

    <xamlsharp>
      <sources>
        <source patch:before="source[watchers/watcher/folder/text()='/sitecore/shell/Applications']" type="Sitecore.Web.UI.XamlSharp.Xaml.XamlFileControlSource, Sitecore.Kernel" >
          <watchers hint="list:AddWatcher">
            <watcher type="Sitecore.Web.UI.XamlSharp.Xaml.XamlFileWatcher, Sitecore.Kernel">
              <folder>/sitecore/shell/override</folder>
              <filter>*.xaml.xml</filter>
              <codefilter>*.xaml.xml.cs</codefilter>
              <includesubdirectories>true</includesubdirectories>
            </watcher>
          </watchers>
        </source>
      </sources>
    </xamlsharp>
  </sitecore>
</configuration>
