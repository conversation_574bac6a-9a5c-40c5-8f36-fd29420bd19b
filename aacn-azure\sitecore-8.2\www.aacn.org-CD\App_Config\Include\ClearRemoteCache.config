﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:set="http://www.sitecore.net/xmlconfig/set/">
	<sitecore>
		<!-- register new events with handlers -->
		<events>
			<!-- New local event to clear CM cache too -->
			<event name="cache:clear">
				<handler type="Aacn.Mvc.Events.RemoteCacheClearHandler, Aacn.Mvc" method="ClearCache" />
			</event>
			<!-- New remote event to clear CD caches -->
			<event name="cache:clear:remote">
				<handler type="Aacn.Mvc.Events.RemoteCacheClearHandler, Aacn.Mvc" method="ClearCache" />
			</event>
			<event name="publish:startPublishing">
				<handler type="Aacn.Mvc.Events.RemoteCacheClearHandler, Aacn.Mvc" method="ClearCache" />
			</event>
			<event name="publish:end">
				<handler type="Aacn.Mvc.Events.RemoteCacheClearHandler, Aacn.Mvc" method="ClearCache"/>
			</event>
			<event name="publish:end:remote">
				<handler type="Aacn.Mvc.Events.RemoteCacheClearHandler, Aacn.Mvc" method="ClearCache"/>
			</event>
		</events>

		<!-- register new command for added button in publish ribbon; this button broadcasts both events above. -->
		<!-- Tip: restrict permission to button if desired using standard sitecore access editor on core database button item -->
		<commands>
			<command name="remotecache:clear" type="Aacn.Mvc.Customization.ClearHtmlCache, Aacn.Mvc"/>
		</commands>

		<!-- initialize remote machines to listen for new custom remote event -->
		<pipelines>
			<initialize>
				<processor type="Aacn.Mvc.Pipelines.Initialize.RemoteCacheEventHandler, Aacn.Mvc" method="Initialize" />
			</initialize>
		</pipelines>
	</sitecore>
</configuration>