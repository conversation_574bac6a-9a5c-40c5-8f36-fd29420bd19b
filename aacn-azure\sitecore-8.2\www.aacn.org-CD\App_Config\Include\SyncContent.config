﻿<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
  
  <eventing defaultProvider="sitecore">
  
  
  <eventQueueProvider>
  
  
  
  <eventQueue name="mastersync" patch:after="eventQueue[@name='web']" type="Sitecore.Data.Eventing.$(database)EventQueue, Sitecore.Kernel">
  
  
  
  
  <param ref="dataApis/dataApi[@name='$(database)']" param1="$(name)"/>
  
  
  
  
  <param hint="" ref="PropertyStoreProvider/store[@name='$(name)']"/>
  
  
  
  </eventQueue>
  
  
  </eventQueueProvider>
  
  </eventing>
  
  <PropertyStoreProvider>
  
  
  <store name="mastersync" patch:after="store[@name='web']" prefix="mastersync" getValueWithoutPrefix="true" singleInstance="true"
  
  
  
  type="Sitecore.Data.Properties.$(database)PropertyStore, Sitecore.Kernel">
  
  
  
  <param ref="dataApis/dataApi[@name='$(database)']" param1="$(name)"/>
  
  
  
  <param resolve="true" type="Sitecore.Abstractions.BaseEventManager, Sitecore.Kernel"/>
  
  
  
  <param resolve="true" type="Sitecore.Abstractions.BaseCacheManager, Sitecore.Kernel"/>
  
  
  </store>
  
  </PropertyStoreProvider>
  
  <databases>
  
  
  <!-- Preview-->
  
  
  <database id="mastersync" singleInstance="true" type="Sitecore.Data.Database, Sitecore.Kernel">
  
  
  
  <param desc="name">$(id)</param>
  
  
  
  <icon>Images/database_web.png</icon>
  
  
  
  <securityEnabled>true</securityEnabled>
  
  
  
  <dataProviders hint="list:AddDataProvider">
  
  
  
  
  <dataProvider ref="dataProviders/main" param1="$(id)">
  
  
  
  
  
  <disableGroup>publishing</disableGroup>
  
  
  
  
  
  <prefetch hint="raw:AddPrefetch">
  
  
  
  
  
  
  <sc.include file="/App_Config/Prefetch/Common.config"/>
  
  
  
  
  
  
  <sc.include file="/App_Config/Prefetch/Webdb.config"/>
  
  
  
  
  
  </prefetch>
  
  
  
  
  </dataProvider>
  
  
  
  </dataProviders>
  
  
  
  <proxiesEnabled>false</proxiesEnabled>
  
  
  
  <proxyDataProvider ref="proxyDataProviders/main" param1="$(id)"/>
  
  
  
  <archives hint="raw:AddArchive">
  
  
  
  
  <archive name="archive"/>
  
  
  
  
  <archive name="recyclebin"/>
  
  
  
  </archives>
  
  
  
  <cacheSizes hint="setting">
  
  
  
  
  <data>100MB</data>
  
  
  
  
  <items>50MB</items>
  
  
  
  
  <paths>2500KB</paths>
  
  
  
  
  <itempaths>50MB</itempaths>
  
  
  
  
  <standardValues>2500KB</standardValues>
  
  
  
  </cacheSizes>
  
  
  </database>
  
  </databases>
  </sitecore>
</configuration>