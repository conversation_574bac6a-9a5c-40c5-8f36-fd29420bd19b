<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/" xmlns:set="http://www.sitecore.net/xmlconfig/set/">
  <sitecore>
    <!-- PIPELINES -->
    <pipelines>
      <initialize>
        <processor type="Sitecore.ListManagement.Services.Pipelines.Initialize.RegisterHttpRoutes, Sitecore.ListManagement.Services" />
      </initialize>
    </pipelines>

    <!-- SITECORE SERVICES WEB API FILTERS 
         Specifies the list of Web API filters to load for request handling
    -->
    <api>
      <services>
        <configuration>
          <allowedControllers hint="list:AddController">
            <allowedController desc="ActionsController">Sitecore.ListManagement.Services.ActionsController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="ContactListController">Sitecore.ListManagement.Services.ContactListController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="ContactsController">Sitecore.ListManagement.Services.ContactsController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="ImportController">Sitecore.ListManagement.Services.ImportController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="SecurityController">Sitecore.ListManagement.Services.SecurityController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="SegmentedListController">Sitecore.ListManagement.Services.SegmentedListController, Sitecore.ListManagement.Services</allowedController>
            <allowedController desc="ListLockingController">Sitecore.ListManagement.Services.ListLockingController, Sitecore.ListManagement.Services</allowedController>
          </allowedControllers>
        </configuration>
      </services>
    </api>
  </sitecore>
</configuration>