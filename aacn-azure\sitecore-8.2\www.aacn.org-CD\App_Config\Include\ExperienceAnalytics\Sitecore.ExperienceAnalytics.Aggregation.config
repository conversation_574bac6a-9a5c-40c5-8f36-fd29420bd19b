﻿<!--

Purpose: This include file needs to be enabled in Processing Environment.

It enables the components and features related to aggregation subsystem.

-->
<configuration xmlns:x="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <experienceAnalytics>
      <aggregation>
        <!-- This section hosts the definitions of the Experience Analytics dimensions
            'id' attribute is the unique identifier and should match the ID of the corresponding dimension definition item in Marketing Control Panel
            'type' attribute references the fully qualified class name and assembly name with the dimension implementation.
        -->
        <dimensions>
          <dimension id="{EF129780-FFCF-4EAF-B429-80D25315248E}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByEmailManager, Sitecore.ExperienceAnalytics" />
          <dimension id="{A1BCAB9A-1A98-4A3D-A58F-34ACF7931C49}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByEmail, Sitecore.ExperienceAnalytics" />
          <dimension id="{214E8E8C-519A-4803-8579-3CA466F9F29F}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.Exm.ByLanguageSpecificEmail, Sitecore.ExperienceAnalytics" />
          <dimension id="{988A1D00-9D00-4F05-97EC-DF3391F8CCE7}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCountry, Sitecore.ExperienceAnalytics" />
          <dimension id="{1879168B-AF5E-4E9C-9DAE-8B71125F2AD2}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByRegion, Sitecore.ExperienceAnalytics" />
          <dimension id="{6237CC24-4FF5-4869-B898-FC6A534F3C3E}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannelType, Sitecore.ExperienceAnalytics" />
          <dimension id="{86CA3214-A950-4EEF-BEE6-A2C5CF6FAC24}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannel, Sitecore.ExperienceAnalytics" />
          <dimension id="{595A12E2-CEF6-4B1C-9990-83FDD7173533}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByChannelGroup, Sitecore.ExperienceAnalytics" />
          <dimension id="{33ACD611-FE19-4769-99F9-1EF1D997BDC5}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCity, Sitecore.ExperienceAnalytics" />
          <dimension id="{DACF0445-EDB1-4A90-851E-380DC6A36541}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByDownloads, Sitecore.ExperienceAnalytics" />
          <dimension id="{A8EC3E11-A417-4624-A612-12A406284D4B}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCampaignGroup, Sitecore.ExperienceAnalytics" />
          <dimension id="{3E01BA28-2B4D-408A-A4BA-6C51ED9FFB9C}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByCampaign, Sitecore.ExperienceAnalytics" />
          <dimension id="{6295C8F0-5ACB-4634-A1D6-6D3248EC210C}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByLanguage, Sitecore.ExperienceAnalytics" />
          <dimension id="{C001F4B2-0523-436E-9097-AD4E39D51835}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPattern, Sitecore.ExperienceAnalytics" />
          <dimension id="{E6CC7A2B-6E72-40CE-9315-88F4617CDB10}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByReferringSite, Sitecore.ExperienceAnalytics" />
          <dimension id="{F197D8FF-D4C8-42DF-BC6E-512F17292674}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPage, Sitecore.ExperienceAnalytics" />
          <dimension id="{81379156-7721-4EE5-8951-AE92E1CDF090}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByEntryPage, Sitecore.ExperienceAnalytics" />
          <dimension id="{3DC11B43-1D7F-4169-A87D-291D57ECEDA6}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByExitPage, Sitecore.ExperienceAnalytics" />
          <dimension id="{F2C1CCBF-C2D6-4FF9-A3BF-DD55E60071A2}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPageUrl, Sitecore.ExperienceAnalytics" />
          <dimension id="{6B945AE7-673F-412A-96F8-F53EF9D63BBC}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByEntryPageUrl, Sitecore.ExperienceAnalytics" />
          <dimension id="{CBD2E37E-CC79-4A5A-8B3B-95F6B7D7FF53}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByExitPageUrl, Sitecore.ExperienceAnalytics" />
          <dimension id="{CBEE2CF5-51DB-4106-A7A9-BF87AB173742}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByLocalSearchKeyword, Sitecore.ExperienceAnalytics" />
          <dimension id="{DF6FCD1E-CBCA-4747-9109-ABF132189A01}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.BySearchKeyword, Sitecore.ExperienceAnalytics" />
          <dimension id="{8345E6DF-8982-4F32-B478-40D672E914BB}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByPageviews, Sitecore.ExperienceAnalytics" />
          <dimension id="{6DD3E3A7-FE91-4860-9BF6-388863AD7C9D}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByConversions, Sitecore.ExperienceAnalytics" />
          <dimension id="{6F502E86-5CDB-4D4E-B3A1-905D41B86E9F}" type="Sitecore.ExperienceAnalytics.Aggregation.Dimensions.ByGoal, Sitecore.ExperienceAnalytics" />
        </dimensions>
        <services>
          <!-- DimensionDefinitionService is responsible for instantiating Experience Analytics Dimensions objects from configuration.
               The 'pathToConfigNode' parameter specifies the element where the dimensions are expected to be configured.
               Default: "experienceAnalytics/aggregation/dimensions"
           -->
          <dimensionDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.DimensionDefinitionService, Sitecore.ExperienceAnalytics" >
            <param desc="pathToConfigNode">experienceAnalytics/aggregation/dimensions</param>
          </dimensionDefinitionService>

          <!-- SegmentDefinitionService is responsible for reading data from the Segments dimension table in RDB.
               The 'connectionStringName' parameter the name of the connection string of the RDB instance to connect to.
               Default: "reporting"
          -->
          <segmentDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.LocalSegmentDefinitionService, Sitecore.ExperienceAnalytics">
            <param desc="connectionStringName">reporting</param>
          </segmentDefinitionService>

          <!-- SiteDefinitionService is responsible for reading data from the SiteNames dimension table in RDB.
               The 'connectionStringName' parameter the name of the connection string of the RDB instance to connect to.
               Default: "reporting"
          -->
          <siteDefinitionService type="Sitecore.ExperienceAnalytics.Core.Repositories.LocalSiteDefinitionService, Sitecore.ExperienceAnalytics">
            <param desc="connectionStringName">reporting</param>
          </siteDefinitionService>
        </services>
      </aggregation>
    </experienceAnalytics>
    <aggregation>
      <routines>
        <ExecRoutineStatementBuilder>
          <mappings>
            <SqlMappingEntity uid="Fact_SegmentMetrics" type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>Fact_SegmentMetrics</Table>
              <Routine>Add_SegmentMetrics_Tvp</Routine>
              <TableType>SegmentMetrics_Type</TableType>
              <IsMultiRow>True</IsMultiRow >
            </SqlMappingEntity>
            <SqlMappingEntity uid="SegmentRecords" type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>SegmentRecords</Table>
              <Routine>Ensure_SegmentRecords_Tvp</Routine>
              <TableType>SegmentRecords_Type</TableType>
              <IsMultiRow>True</IsMultiRow >
            </SqlMappingEntity>
            <SqlMappingEntity uid="DimensionKeys" type="Sitecore.Analytics.Aggregation.SqlMappingEntity, Sitecore.Analytics.Sql">
              <Table>DimensionKeys</Table>
              <Routine>Ensure_DimensionKeys_Tvp</Routine>
              <TableType>DimensionKeys_Type</TableType>
              <IsMultiRow>True</IsMultiRow >
            </SqlMappingEntity>
          </mappings>
        </ExecRoutineStatementBuilder>
      </routines>
    </aggregation>
    <pipelines>
      <group groupName="analytics.aggregation">
        <pipelines>
          <interactions>
            <!-- The pipeline processor is building Facts and Dimensions for each segment registered in Experience Analytics -->
            <processor type="Sitecore.ExperienceAnalytics.Aggregation.Pipeline.SegmentProcessor, Sitecore.ExperienceAnalytics" />
          </interactions>
        </pipelines>
      </group>
    </pipelines>
    <settings>
      <!-- EXPERIENCE ANALYTICS - AGGREGATION - SEGMENT DEPLOY DATE OFFSET PADDING
           Controls additional padding when interaction's SaveDateTime is compared with segment's deploy date.
           The timespan built from the value of this setting is added to the segment's deploy date.
           When a segment is being deployed, the DeployDate will be DateTime.UtcNow.Add(SegmentDeployDateOffsetPadding)
           Interaction data for the particular segment will be processed if interaction.SaveDateTime > segment.DeployDate
           Default value: 00:30:00 (30 minutes)
        -->
      <setting name="ExperienceAnalytics.Aggregation.SegmentDeployDateOffsetPadding" value="00:30:00" />
    </settings>
  </sitecore>
</configuration>