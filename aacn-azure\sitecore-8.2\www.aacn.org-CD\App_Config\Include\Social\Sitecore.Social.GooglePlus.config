﻿<?xml version="1.0" encoding="utf-8" ?>
<!--
    
Purpose: This include file configures the plugin to the Social Connected module that enables integration with Google+ (https://plus.google.com/).

Please read the Sitecore Social Connected documentation before changing the configuration.
    
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>

      <!-- SOCIAL - GOOGLE - DEFAULT GOOGLE PLUS ONE GOAL
           The Sitecore Social Connected goal that is triggered when a visitor clicks the "+1" button.
           Default value: Google Plus One
      -->
      <setting name="Social.Google.DefaultGooglePlusOneGoal" value="Google Plus One"/>

      <!-- SOCIAL - GOOGLE - IS OFFLINE ACCESS
           Specifies if the Social Connector will request the Google+ "offline_access" permission.
           Default value: true 
      -->
      <setting name="Social.Google.IsOfflineAccess" value="true" />

    </settings>

    <social>

      <!-- Personalization -->
      <!-- This section configures the gender settings in the user profile. -->
      <genderRule>
        <network name="Google">
          <gender sitecoreKey="gg_Gender" maleValue="male" femaleValue="female" />
        </network>
      </genderRule>
      
      <networks>
        <network name="Google" ItemId="{F4E43218-A31C-4A70-B9D6-6ECC8CCD1F38}" prefix="gg" icon="googleplus3" url="https://plus.google.com/">
          <permissionBuilders>
            <permissionBuilder type="Sitecore.Social.GooglePlus.Connector.PermissionBuilders.GooglePermissionBuilder, Sitecore.Social.GooglePlus" />
          </permissionBuilders>
          <providers>
            <provider type="Sitecore.Social.GooglePlus.Networks.Providers.GooglePlusProvider, Sitecore.Social.GooglePlus"/>
          </providers>
        </network>
      </networks>
      
    </social>
    
  </sitecore>
</configuration>