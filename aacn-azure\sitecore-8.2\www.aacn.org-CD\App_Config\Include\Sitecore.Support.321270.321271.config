﻿<?xml version="1.0" encoding="utf-8"?>
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <!-- 
		Setting Preview.ResolveSite to true will allow resolving of site during preview in presentation tab and in page editor 
	-->
      <setting name="Preview.ResolveSite" value="true" />
    </settings>
    <pipelines>
      <httpRequestBegin>
        <processor type="Sitecore.Support.Pipelines.HttpRequest.PreviewSiteResolver, Sitecore.Support.321270.321271" patch:after="*[@type='Sitecore.Pipelines.HttpRequest.ItemResolver, Sitecore.Kernel']" />
      </httpRequestBegin>
    </pipelines>
  </sitecore>
</configuration>