<!--

Purpose: This include file adds a new site definition

To enable this, rename this file so that it has a ".config" extension and 
change all the parameters to suit your own scenario

Notice how "patch:before" is used to insert the site definition BEFORE the 
existing <site name="website" ...> element 

You can use "patch:before" and "patch:after" as an attribute of an inserted 
element to specify an insertion point for the new element. Both accept an 
XPath relative to the parent node of the inserted element.

-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <sites>
      <site name="aacn" patch:before="site[@name='website']" virtualFolder="/" physicalFolder="/" rootPath="/sitecore/content" startItem="/AACN Website/Home"
        database="web" domain="aacn" allowDebug="true" cacheHtml="true" htmlCacheSize="40MB" enablePreview="true" enableWebEdit="true"
        enableDebugger="true" disableClientData="false" loginPage="/membership/signin" dictionaryDomain="{695A0A93-055F-43A0-8824-D6B699193E7A}"/>
    </sites>
    <!--todo add site for on publish cache cleaning events-->
    <cacheSizes>
      <sites>
        <aacn>
          <html>500MB</html>
          <registry>0</registry>
          <viewState>0</viewState>
          <xsl>5MB</xsl>
        </aacn>
      </sites>
    </cacheSizes>
  </sitecore>
</configuration>