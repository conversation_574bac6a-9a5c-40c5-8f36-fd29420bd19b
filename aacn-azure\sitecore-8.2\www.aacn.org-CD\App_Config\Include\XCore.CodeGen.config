<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <settings>
      <setting name="XCore.CodeGen.IncludeNamespaces" value="System|System.Collections.Generic|Sitecore.Data.Items|Sitecore.Data.Fields|XCore.Framework.ItemMapper|XCore.Framework.ItemMapper.Configuration.Attributes|Aacn.DomainObjects.Data.Items.BaseTemplates|Aacn.DomainObjects.Data.Items.Datasources.WidgetSubitems" />
      <setting name="XCore.CodeGen.GeneratedCodeFolder" value="$(dataFolder)\GeneratedCode" />
      <setting name="XCore.CodeGen.TemplateAssemblyNames" value="DomainObjects" />
      <setting name="XCore.CodeGen.CustomFieldMappings" value="image with cropping:ImageField|custom index multilist with search:Guid[]" />
    </settings>
  </sitecore>
</configuration>
