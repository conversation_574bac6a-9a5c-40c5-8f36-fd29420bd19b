﻿<?xml version="1.0" encoding="utf-8" ?>
<!--

Purpose: This include file needs to be enabled in Content Management and Reporting Service Environments.
It extends the default implementation of IDefinitionsStorageProvider with ability to support custom deployable definitions.

-->

<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <aggregation>
      <!-- Patching the default reportingStorageProviders to be re-routed to the custom IDefinitionsStorageProvider
           defined in pathAnalyzer/definitionStorageProviders section below.-->
      <reportingStorageProviders>
        <primary>
          <storageProviders hint="list:AddCustomProviders">
            <treeDefinitions ref="pathAnalyzer/definitionStorageProviders/primary" />
          </storageProviders>
        </primary>
        <secondary.live>
          <storageProviders hint="list:AddCustomProviders">
            <treeDefinitions ref="pathAnalyzer/definitionStorageProviders/secondary" />
          </storageProviders>
        </secondary.live>
        <secondary.history>
          <storageProviders hint="list:AddCustomProviders">
            <treeDefinitions ref="pathAnalyzer/definitionStorageProviders/secondary" />
          </storageProviders>
        </secondary.history>
      </reportingStorageProviders>
    </aggregation>
    <pathAnalyzer>
      <definitionStorageProviders>
        <primary type="Sitecore.PathAnalyzer.Data.TreeDefinitionsStorageProvider, Sitecore.PathAnalyzer">
          <param desc="connectionStringName">reporting</param>
        </primary>
        <secondary type="Sitecore.PathAnalyzer.Data.TreeDefinitionsStorageProvider, Sitecore.PathAnalyzer">
          <param desc="connectionStringName">reporting.secondary</param>
        </secondary>
      </definitionStorageProviders>
    </pathAnalyzer>
  </sitecore>
</configuration>
