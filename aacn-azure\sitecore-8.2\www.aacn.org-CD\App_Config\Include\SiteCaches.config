<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
  <sitecore>
    <!-- Caches - set sizes to whatever values make sense -->
    <databases>
      <!-- core -->
      <database id="core">
        <cacheSizes hint="setting">
          <data>40MB</data>
          <items>20MB</items>
          <paths>1MB</paths>
          <itempaths>20MB</itempaths>
          <standardValues>1MB</standardValues>
        </cacheSizes>
      </database>
      <!-- master -->
      <database id="master">
        <cacheSizes hint="setting">
          <data>500MB</data>
          <items>500MB</items>
          <paths>10MB</paths>
          <itempaths>50MB</itempaths>
          <standardValues>10MB</standardValues>
        </cacheSizes>
      </database>
      <!-- webcd -->
      <database id="webcd">
        <cacheSizes hint="setting">
          <data>2.5GB</data>
          <items>2.5GB</items>
          <paths>10MB</paths>
          <itempaths>50MB</itempaths>
          <standardValues>10MB</standardValues>
        </cacheSizes>
      </database>
    </databases>
  </sitecore>
</configuration>
