<!--
	<PERSON>.config

	This file configures the Rainbow serialization library
	
	This file should be present on all environments <PERSON> is present on. 
	
	http://github.com/kamsar/Rainbow
-->
<configuration xmlns:patch="http://www.sitecore.net/xmlconfig/">
	<sitecore>
		<settings>
			<!--  <PERSON> - SERIALIZATION FOLDER PATH MAX LENGTH
				In Windows, there is a 248 character limit on the length of file system paths. To avoid exceeding the maximum path length, Rainbow will loop
						long paths back to the root. This setting specifies the maximum length of the path to the serialization root path,
						which determines how long item paths can be before they are looped.
				Important: The value of this setting must be the same on all Sitecore instances accessing the serialized data. 
				Important: When changing this value, you must reserialize all configurations!
				Example: A value of "90" for this setting will mean that item paths longer than 150 characters will be shortened, since Sitecore 
				reserves 8 characters (and 248 - 8 - 90 = 150). 
				Default value: 110
			-->
			<setting name="Rainbow.SFS.SerializationFolderPathMaxLength" value="110" />

			<!--  Rainbow MAX ITEM NAME LENGTH BEFORE TRUNCATION
				Sitecore item names can become so long that they will not fit on the filesystem without hitting the max path length.
						This setting controls when <PERSON> truncates item file names that are extremely long so they will fit on the filesystem.
						The value must be less than MAX_PATH - SerializationFolderPathMaxLength - Length of GUID - length of file extension.
				Default value: 30
			-->
			<setting name="Rainbow.SFS.MaxItemNameLengthBeforeTruncation" value="30" />

			<!--  Rainbow EXTRA INVALID FILENAME CHARACTERS
				Sometimes source control systems, like TFS with '$', impose additional limitations on what files on disk may be named.
				Adding characters to this setting will disallow their use in written SFS item names on disk.
				Path.GetInvalidFileNameChars() are always included as invalid characters; this setting is for extra characters only.
				Default value: $
			-->
			<setting name="Rainbow.SFS.ExtraInvalidFilenameCharacters" value="$" />

			<!--  Rainbow INVALID FILE NAMES
				Windows has certain file names that are disallowed that are device names. These apply regardless of file extension, so should you name an item PRN, we'll fix that.
				See https://msdn.microsoft.com/en-us/library/windows/desktop/aa365247(v=vs.85).aspx
				If an item name matches one of these names it will be prefixed with an underscore to make the name valid (e.g. COM1 -> _COM1)
			-->
			<setting name="Rainbow.SFS.InvalidFilenames" value="CON,PRN,AUX,NUL,COM1,COM2,COM3,COM4,COM5,COM6,COM7,COM8,COM9,LPT1,LPT2,LPT3,LPT4,LPT5,LPT6,LPT7,LPT8,LPT9" />
		</settings>
	</sitecore>
</configuration>
